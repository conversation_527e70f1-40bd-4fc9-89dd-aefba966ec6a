// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { WEBSITE_NAME } from '@/constants/common';

export const randomId = () => {
  return `${WEBSITE_NAME.toLowerCase()}-${Math.random()
    .toString(36)
    .slice(2, 11)}`;
};

export const randomUuid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

export const getSafeId = (uid: string, errorMessage: string) => {
  return (value: string) => {
    if (typeof value !== 'string' || value.trim().length === 0) {
      throw new Error(errorMessage);
    }

    return `${uid}-${value}`;
  };
};
