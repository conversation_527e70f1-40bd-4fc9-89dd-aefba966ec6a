// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import classNames from 'classnames';
import { FC } from 'react';
import { Button } from '@/components/Button';
import Link from 'next/link';
import { routers } from '@/constants/routes';

export interface ICurrentPointProps {
  label?: string;
  currentPoint: number;
  className?: string;
  isShowHowToGetPointsLink?: boolean;
  isShowPossessionPoints?: boolean;
  handleRedeemPointsButtonClick?: () => void;
}

const CurrentPoint: FC<ICurrentPointProps> = ({
  label,
  className,
  currentPoint,
  isShowHowToGetPointsLink,
  isShowPossessionPoints = true,
  handleRedeemPointsButtonClick,
}) => (
  <div
    className={classNames(
      'flex justify-center items-center flex-col rounded py-6',
      className
    )}
  >
    <h2 className="font-bold text-base mb-4">
      {isShowPossessionPoints && <span className="mr-4">保有ポイント</span>}
      <span className="text-3xl font-semibold font-hind">
        {currentPoint.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
      </span>
      <span className="ml-1">pt</span>
    </h2>
    <Button
      color="primary"
      className="px-3 py-1"
      onClick={() => handleRedeemPointsButtonClick?.()}
    >
      <span className="font-bold">{label ?? 'ポイント履歴・交換'}</span>
    </Button>
    {isShowHowToGetPointsLink && (
      <Link
        href={routers.howToGetPoints}
        className="text-theme-info-primary text-sm border-b border-theme-info-primary pb-[1px] my-3"
      >
        ポイント獲得一覧
      </Link>
    )}
  </div>
);

export default CurrentPoint;
