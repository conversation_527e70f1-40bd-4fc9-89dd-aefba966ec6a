// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import NewsItem from '@/pages/mypage/news-list/components/news-item';
import mockRouter from 'next-router-mock';

jest.mock('next/router', () => require('next-router-mock'));

describe('pages/mypage/news-list/components/news-item.spec.tsx', () => {
  const props = {
    news_id: '1',
    title: 'testing title',
    published_at: '2023-04-01T17:00:00.000Z',
  };

  describe('prop', () => {
    it('Should render heading', async () => {
      render(<NewsItem {...props} />);
      const heading = screen.getByRole('heading');

      expect(heading).toHaveTextContent('testing title');
    });
  });

  describe('User interactions', () => {
    it('should trigger `onClick` button', async () => {
      const { news_id } = props;

      mockRouter.push('/example-path');

      render(<NewsItem {...props} />);

      const redirectLink = screen.getByTestId('news-item-link');

      fireEvent.click(redirectLink);

      await waitFor(() => {
        expect(mockRouter).toMatchObject({
          asPath: `/example-path#new${news_id}`,
        });
      });
    });
  });
});
