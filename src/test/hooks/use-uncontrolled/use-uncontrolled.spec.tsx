// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { act, renderHook } from '@testing-library/react';
import { useUncontrolled } from '@/hooks/use-uncontrolled';

describe('hooks/use-uncontrolled/use-uncontrolled.spec.tsx', () => {
  describe('A11y', () => {
    it('should returns default value for initial uncontrolled state', () => {
      const { result } = renderHook(() =>
        useUncontrolled({
          value: undefined,
          defaultValue: 'test-default',
          finalValue: 'test-final',
        })
      );
      expect(result.current[0]).toBe('test-default');
    });

    it('should returns final value for initial uncontrolled state if default value was not provided', () => {
      const { result } = renderHook(() =>
        useUncontrolled({
          value: undefined,
          defaultValue: undefined,
          finalValue: 'test-final',
        })
      );
      expect(result.current[0]).toBe('test-final');
    });
  });

  describe('User interactions', () => {
    it('should supports uncontrolled state', () => {
      const { result } = renderHook(() =>
        useUncontrolled({ defaultValue: 'default-value' })
      );
      act(() => result.current[1]('change-value'));
      expect(result.current[0]).toBe('change-value');
    });

    it('should calls onChange with uncontrolled state', () => {
      const spy = jest.fn();
      const { result } = renderHook(() =>
        useUncontrolled({ defaultValue: 'default-value', onChange: spy })
      );
      act(() => result.current[1]('change-value'));
      expect(spy).toHaveBeenCalledWith('change-value');
    });

    it('should supports controlled state', () => {
      const spy = jest.fn();
      const { result } = renderHook(() =>
        useUncontrolled({ value: 'controlled-value', onChange: spy })
      );

      act(() => result.current[1]('change-value'));
      expect(result.current[0]).toBe('controlled-value');

      expect(spy).toHaveBeenCalledTimes(1);
      expect(spy).toHaveBeenCalledWith('change-value');
    });
  });
});
