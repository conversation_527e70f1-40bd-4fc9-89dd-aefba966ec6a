// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Button } from '@/components/Button';

const button = () => screen.getByRole('button');
const buttonLink = () => screen.getByRole('link');
const buttons = () => screen.getAllByRole('button');

describe('components/Button/Button.spec.tsx', () => {
  describe('A11y', () => {
    it('should have `role="button"` by default', () => {
      render(<Button>Hi there</Button>);

      expect(button()).toBeInTheDocument();
    });

    it('should be able to use any other role permitted for `<button>`s', () => {
      render(<Button role="status">Hi there</Button>);

      const buttonRole = screen.getByRole('status');

      expect(buttonRole).toBeInTheDocument();
    });
  });

  describe('Keyboard interactions', () => {
    it('should trigger `onClick` when `Space` is pressed', async () => {
      const user = userEvent.setup();
      const onClick = jest.fn();

      render(<Button onClick={onClick}>Hi there</Button>);

      await user.click(button());

      expect(onClick).toHaveBeenCalledTimes(1);
    });

    it('should focus when `Tab` is pressed', async () => {
      const user = userEvent.setup();
      render(<Button>Hi there</Button>);

      await user.tab();

      expect(button()).toHaveFocus();
    });

    it('should be possible to `Tab` out', async () => {
      const user = userEvent.setup();
      render(
        <>
          <Button>Hi there</Button>
          <Button>Hello there</Button>
          <button type="submit">Submit</button>
        </>
      );

      await user.tab();

      expect(buttons()[0]).toHaveFocus();

      await user.tab();

      expect(buttons()[1]).toHaveFocus();

      await user.tab();

      expect(buttons()[2]).toHaveFocus();
    });
  });

  describe('Props', () => {
    it('should allow HTML attributes for `<button>`s', () => {
      render(
        <Button formAction="post.php" type="submit">
          Hi there
        </Button>
      );

      expect(button()).toHaveAttribute('formAction', 'post.php');
      expect(button()).toHaveAttribute('type', 'submit');
    });

    it('should be disabled when `disabled={true}`', () => {
      render(<Button disabled>Hi there</Button>);

      expect(button()).toBeDisabled();
    });
  });

  describe('Rendering', () => {
    it('should render when `children={0}`', () => {
      render(<Button>0</Button>);

      expect(button()).toHaveTextContent('0');
    });

    it('should render an anchor `<a>` when `href="..."`', () => {
      render(<Button href="#" />);

      expect(buttonLink()).toBeInTheDocument();
    });
  });
});
