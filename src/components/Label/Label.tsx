// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import classNames from 'classnames';
import type { ComponentProps, FC, PropsWithChildren } from 'react';
import { IThemeColors } from '@/types/theme';
import { styles } from './Label.styles';

export interface ILabelTheme {
  base: string;
  colors: ILabelColors;
  disabled: string;
}

export interface ILabelColors extends IThemeColors {
  [key: string]: string;
  default: string;
}

export interface ILabelProps
  extends PropsWithChildren<Omit<ComponentProps<'label'>, 'color'>> {
  color?: keyof ILabelColors;
  value?: string;
  disabled?: boolean;
}

export const Label: FC<ILabelProps> = ({
  children,
  color = 'default',
  disabled = false,
  value,
  className,
  ...props
}): JSX.Element => {
  const theme = styles;
  return (
    <label
      className={classNames(
        theme.base,
        theme.colors[color],
        disabled && theme.disabled,
        className
      )}
      {...props}
    >
      {value ?? children ?? ''}
    </label>
  );
};
