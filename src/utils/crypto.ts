// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import CryptoJ<PERSON> from 'crypto-js';
import { HASH_SECRET_KEY } from '@/constants/envs';

export const decrypt = (ciphertext: string) => {
  const decryptValue =
    CryptoJS.AES.decrypt(ciphertext, HASH_SECRET_KEY).toString(
      CryptoJS.enc.Utf8
    ) || '';
  return decryptValue;
};

export const encrypt = (plaintext: string) => {
  const encryptValue = CryptoJS.AES.encrypt(
    plaintext,
    process.env.HASH_SECRET_KEY || 'somethingsecret'
  ).toString();
  return encryptValue;
};
