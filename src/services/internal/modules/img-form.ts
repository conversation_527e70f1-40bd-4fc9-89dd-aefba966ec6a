// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import {
  IGetImageFormResponse,
  IUploadImageFormResponse,
} from '@/types/img-form';
import service from '..';

const basePath = '/image-form';

const imageFormApi = {
  getImageForm: (formId: string) =>
    service.get<IGetImageFormResponse>(`${basePath}/${formId}`),
  uploadImageForm: (formId: string, payload: FormData) =>
    service.upload<IUploadImageFormResponse>(`${basePath}/${formId}`, payload),
};

export default imageFormApi;
