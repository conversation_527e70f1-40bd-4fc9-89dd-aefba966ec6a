// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { MessageBox } from '@/components/MessageBox';
import { FC } from 'react';

interface NewsErrorProps {
  retry?: () => void;
  title: string;
}

export const NewsError: FC<NewsErrorProps> = ({ retry, title }) => {
  return (
    <MessageBox className="pt-[70px]">
      <MessageBox.Body className="max-w-[75vw]">
        <MessageBox.Icon name="AccessDenied" />
        <MessageBox.Content>
          現在{title}を読み込めません。
          <br />
          時間を空けて再度お試しください。
        </MessageBox.Content>
      </MessageBox.Body>
      <MessageBox.Action onClick={retry}>再度読み込む</MessageBox.Action>
    </MessageBox>
  );
};
