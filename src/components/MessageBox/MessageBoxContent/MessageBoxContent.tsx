// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { ComponentProps, FC } from 'react';
import { styles } from './MessageBoxContent.styles';
import classNames from 'classnames';

export interface MessageBoxContentTheme {
  base: string;
}

export const MessageBoxContent: FC<ComponentProps<'p'>> = ({
  className,
  children,
  ...props
}) => {
  return (
    <p className={classNames(styles.base, className)} {...props}>
      {children}
    </p>
  );
};
