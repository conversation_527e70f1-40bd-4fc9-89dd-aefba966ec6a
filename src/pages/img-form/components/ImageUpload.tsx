// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { IMAGE_UPLOAD_ACCEPT } from '@/constants/img-form';
import React, { ClassAttributes, InputHTMLAttributes } from 'react';

const ImageUpload = (
  props: JSX.IntrinsicAttributes &
    ClassAttributes<HTMLInputElement> &
    InputHTMLAttributes<HTMLInputElement>
) => {
  return (
    <input
      type="file"
      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
      accept={IMAGE_UPLOAD_ACCEPT}
      {...props}
    />
  );
};

export default ImageUpload;
