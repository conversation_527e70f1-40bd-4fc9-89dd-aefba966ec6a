// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen } from '@testing-library/react';
import { Label } from '@/components/Label';
import { TextInput } from '@/components/TextInput';
import { Button } from '@/components/Button';

const TestForm = (): JSX.Element => (
  <form>
    <div>
      <Label htmlFor="email">Your email</Label>
      <TextInput
        id="email"
        type="email"
        placeholder="Please enter your email address"
      />
    </div>
    <div>
      <Label htmlFor="password">Your password</Label>
      <TextInput
        id="password"
        type="password"
        placeholder="Please enter your password"
      />
    </div>
    <div>
      <Button type="submit">Submit</Button>
    </div>
  </form>
);

describe('components/Label/Label.spec.tsx', () => {
  describe('A11y', () => {
    it('should have `role="label"`', () => {
      render(<Label role="label" value="Hi there" />);

      expect(screen.getByRole('label')).toBeInTheDocument();
    });

    it('should have not content', () => {
      render(<Label disabled role="label" />);

      expect(screen.getByRole('label')).toHaveTextContent('');
    });

    it('should provide accessible name to any form control associated by `htmlFor`', () => {
      const inputLabels = ['Your email', 'Your password'];
      render(<TestForm />);

      inputLabels.forEach(label =>
        expect(screen.getByLabelText(label)).toHaveAccessibleName(label)
      );
    });
  });

  describe('Props', () => {
    it('should render when `children={0}`', () => {
      render(<Label role="label">Hi there</Label>);

      expect(screen.getByRole('label')).toHaveTextContent('Hi there');
    });
  });
});
