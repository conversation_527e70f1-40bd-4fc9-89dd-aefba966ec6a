// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { act, render, screen, waitFor } from '@testing-library/react';
import type { Liff } from '@line/liff';
import authApi from '@/services/internal/modules/auth';
import { USER_REGISTER_SUCCESS } from '@/mocks/users';
import userEvent from '@testing-library/user-event';
import { USER_INFO_REGISTER } from '@/constants/user';
import { USER_REGISTER_FORM } from '@/mocks/users';
import { LiffContextMockProviders, MockLiffProvidersProps } from '@/test/mocks';
import RegisterConfirmScreen from '@/pages/register-confirm/components/RegisterConfirmScreen';

jest.mock('next/router', () => require('next-router-mock'));

const TestRegisterConfirmScreen = () => {
  const liffContextValue: MockLiffProvidersProps = {
    liff: {
      getContext: () => ({}),
      getFriendship: () => ({
        friendFlag: true,
      }),
    } as unknown as Liff,
  };

  return (
    <LiffContextMockProviders contextValue={liffContextValue}>
      <RegisterConfirmScreen companyId="C0001" />
    </LiffContextMockProviders>
  );
};

describe('pages/register-confirm/components/RegisterConfirmScreen.spec.tsx', () => {
  describe('User Interacion', () => {
    it('Should handle click button register when have user info', async () => {
      const user = userEvent.setup();
      jest
        .spyOn(authApi, 'registerUser')
        .mockResolvedValue(USER_REGISTER_SUCCESS);
      window.sessionStorage.setItem(
        USER_INFO_REGISTER,
        JSON.stringify(USER_REGISTER_FORM)
      );

      render(<TestRegisterConfirmScreen />);

      await waitFor(async () => {
        const buttonRegister = screen.getByRole('button', {
          name: '登録する',
        });
        await user.click(buttonRegister);

        expect(buttonRegister).toHaveFocus();
      });
    });

    it('Should handle click button register when have error', async () => {
      const user = userEvent.setup();
      jest.spyOn(authApi, 'registerUser').mockRejectedValue({
        errors: [
          {
            property: 'email',
            constraints: {
              UpdateEmailUnique: 'example',
            },
          },
        ],
      });
      window.sessionStorage.setItem(
        USER_INFO_REGISTER,
        JSON.stringify(USER_REGISTER_FORM)
      );

      render(<TestRegisterConfirmScreen />);

      await waitFor(async () => {
        const buttonRegister = screen.getByRole('button', {
          name: '登録する',
        });
        await user.click(buttonRegister);

        expect(buttonRegister).toHaveFocus();
      });
    });

    it('Should handle click button register when have not user info', async () => {
      const user = userEvent.setup();
      window.sessionStorage.clear();

      render(<TestRegisterConfirmScreen />);

      await waitFor(async () => {
        const buttonRegister = screen.getByRole('button', {
          name: '登録する',
        });
        await user.click(buttonRegister);

        expect(buttonRegister).toHaveFocus();
      });
    });

    it('should handle click button redirect', async () => {
      const user = userEvent.setup();

      render(<TestRegisterConfirmScreen />);
      const buttonRedirect = screen.getAllByRole('button')[1];
      await act(() => user.click(buttonRedirect));

      expect(buttonRedirect).toHaveFocus();
    });
  });
});
