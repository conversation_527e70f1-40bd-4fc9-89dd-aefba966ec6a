// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { IModalTheme } from './Modal';

export const styles: IModalTheme = {
  base: 'fixed top-0 right-0 left-0 z-50 overflow-y-auto overflow-x-hidden md:inset-0 h-full',
  show: {
    on: 'flex bg-gray-900 bg-opacity-50',
    off: 'hidden',
  },
  content: {
    base: 'relative flex items-center justify-center h-full w-full p-4 md:h-auto',
    inner: 'relative w-full rounded-modal bg-white shadow',
  },
  body: {
    base: 'px-4',
    popup: '!p-4',
  },
  header: {
    base: 'rounded-t-modal px-4 pt-8 pb-5',
    popup: '!p-4',
  },
  footer: {
    base: 'rounded-b-modal px-4 py-6',
    popup: '!p-4',
  },
  sizes: {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '3xl': 'max-w-3xl',
    '4xl': 'max-w-4xl',
    '5xl': 'max-w-5xl',
    '6xl': 'max-w-6xl',
    '7xl': 'max-w-7xl',
  },
  positions: {
    'top-left': 'items-start justify-start',
    'top-center': 'items-start justify-center',
    'top-right': 'items-start justify-end',
    'center-left': 'items-center justify-start',
    center: 'items-center justify-center',
    'center-right': 'items-center justify-end',
    'bottom-right': 'items-end justify-end',
    'bottom-center': 'items-end justify-center',
    'bottom-left': 'items-end justify-start',
  },
};
