// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React, { ComponentProps, FC } from 'react';
import { formatTime } from '@/utils/time';
import { Gift } from '@/components/Gift';
import { Button } from '@/components/Button';
import { Spinner } from '@/components/Spinner';
import { HandleToggleGiftProps } from './ExchangedGiftList';
import { IExchangedGiftDetail } from '@/types/exchange-points';
import { TAB_EXCHANGED_GIFT_AVAILABLE } from '@/constants/mypage';
import { EXCHANGED_EXPIRE_TIME_PLACEHOLDER } from '@/constants/exchange-points';
import { useRouter } from 'next/router';

interface ExchangedGiftListAvailableProps extends ComponentProps<'div'> {
  gifts: IExchangedGiftDetail[];
  toggle: (options: HandleToggleGiftProps) => void;
}

export const ExchangedGiftListAvailable: FC<
  ExchangedGiftListAvailableProps
> = ({ gifts, toggle, ...props }) => {
  const router = useRouter();

  return (
    <div className="space-y-4" {...props}>
      {gifts.map((exchangedGift: IExchangedGiftDetail) => {
        const { gift, gift_type, exchanged_at, uid, loading } = exchangedGift;

        const exchangedAtTime = formatTime({
          time: exchanged_at,
          format: 'YYYY/M/DD',
        });

        const exchangedExpireTimeOrigin =
          gift_type === 'choosable' ? gift?.choosable_end_at : gift?.expired_at;

        /**
         * In case of gift_type==="fixed", when you exchange gift success.
         * Right away, go back to exchanged history screen you will recive expires_at===null.
         * So in the case will display text default
         */
        const exchangedExpireTime = exchangedExpireTimeOrigin
          ? formatTime({
              format: 'YYYY年M月DD日まで有効',
              time: exchangedExpireTimeOrigin,
            })
          : EXCHANGED_EXPIRE_TIME_PLACEHOLDER;

        return (
          <Gift key={uid} className="gap-2.5 bg-theme-modal-gradient-bottom">
            <Gift.Body
              showChevron
              type="complex"
              contentClassName="items-center"
              onClick={() => {
                if (gift_type === 'coupon') {
                  const couponUrl = `/mypage/exchange-point/coupon?couponId=${encodeURIComponent(
                    gift.gift_id_in_pc
                  )}&code=${encodeURIComponent(gift.code || '')}`;

                  router.push(couponUrl);
                } else {
                  window.location.href = gift.gift_url;
                }
              }}
            >
              <Gift.Thumb
                width={55}
                height={55}
                src={gift?.gift_image}
                alt={gift.gift_name}
              />
              <Gift.Content
                title={gift?.gift_name}
                company={gift?.gift_company_name}
                exchangedAt={exchangedAtTime}
                exchangedExpire={exchangedExpireTime}
              />
            </Gift.Body>

            <Gift.Action className="gap-4">
              <Button
                color="secondary"
                className="w-1/2"
                customSpan="!text-xs !px-1 max-[320px]:!text-[10px]"
                disabled={loading}
                onClick={() =>
                  toggle({ uid, type: TAB_EXCHANGED_GIFT_AVAILABLE })
                }
              >
                {loading && (
                  <div className="mr-2">
                    <Spinner size="md" color="secondary" />
                  </div>
                )}
                利用済みにする
              </Button>
              <Button
                disabled
                color="gray-10"
                className="w-1/2"
                customSpan="!text-xs !px-1 max-[320px]:!text-[10px]"
              >
                使えるギフトに戻す
              </Button>
            </Gift.Action>

            <Gift.Note>使用したギフトは利用済として記録をしましょう</Gift.Note>
          </Gift>
        );
      })}
    </div>
  );
};
