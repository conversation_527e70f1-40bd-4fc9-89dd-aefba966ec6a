// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import type { Meta } from '@storybook/react';
import { styles } from './TabsList/TabsList.styles';
import { Tabs, TabsProps } from './Tabs';

export default {
  title: 'Components/Tabs',
  component: Tabs,
  argTypes: {
    color: {
      options: Object.keys(styles.styles),
      control: { type: 'select' },
    },
  },
} as Meta;

const base = (
  <>
    <Tabs.List>
      <Tabs.Tab value="react">React</Tabs.Tab>
      <Tabs.Tab value="sv">Svelte</Tabs.Tab>
      <Tabs.Tab value="ng">Wrapped tab</Tabs.Tab>
      <Tabs.Tab value="ds" disabled>
        Disabled
      </Tabs.Tab>
    </Tabs.List>

    <Tabs.Panel value="react">React Panel</Tabs.Panel>
    <Tabs.Panel value="sv">Svelte Panel</Tabs.Panel>
    <Tabs.Panel value="ng">Angular Panel</Tabs.Panel>
  </>
);

const Template = (props: TabsProps) => <Tabs {...props} />;

export const Base = () => <Template defaultValue="react">{base}</Template>;
