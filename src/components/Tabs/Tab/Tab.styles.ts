// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { IThemeTabsTab } from './Tab';

export const styles: IThemeTabsTab = {
  base: 'flex items-center justify-center p-2.5 text-base text-black font-bold first:ml-0 disabled:cursor-not-allowed disabled:text-gray-400 transition-all duration-200 ease',
  styles: {
    default: {
      base: 'rounded-t-lg',
      active: {
        on: 'bg-gray-200 text-blue-600',
        off: 'text-gray-500 hover:bg-gray-100 hover:text-gray-600',
      },
    },
    underline: {
      base: '',
      active: {
        on: 'text-blue-600 border-b-2 border-blue-600',
        off: 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-600',
      },
    },
    pills: {
      base: '',
      active: {
        on: 'rounded-lg bg-blue-600 text-white',
        off: 'rounded-lg hover:text-gray-900 hover:bg-gray-100',
      },
    },
    separate: {
      base: 'flex-1',
      active: {
        on: 'border-b-[3px] text-sm border-theme-primary',
        off: 'border-b-[3px] border-transparent text-sm text-theme-pale hover:text-theme-main',
      },
    },
  },
  tabIcon: 'mr-2',
  tabLabel: '',
  tabRightSection: 'ml-2',
};
