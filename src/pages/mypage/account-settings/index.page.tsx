// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { ACCOUNT_SETTING_MENU } from '@/constants/mypage';
import MenuItem from '@/pages/mypage/components/MenuItem';
import MypageLayout from '../components/MypageLayout';

const AccountSetting: FC = () => {
  return (
    <>
      <MypageLayout spacingTop="none" spacingBottom="4xl">
        {/* Menu */}
        <ul>
          {ACCOUNT_SETTING_MENU.map((menuItem, index) => (
            <MenuItem
              key={index}
              label={menuItem.label}
              icon={menuItem.icon}
              to={menuItem.to}
            />
          ))}
        </ul>
      </MypageLayout>
    </>
  );
};

export default AccountSetting;
