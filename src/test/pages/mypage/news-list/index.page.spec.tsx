// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen } from '@testing-library/react';
import AnnouncementScreen from '@/pages/mypage/news-list/index.page';

jest.mock('next/router', () => require('next-router-mock'));

describe('pages/mypage/news-list/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('Should render in document', async () => {
      render(<AnnouncementScreen />);
      const button = screen.getByRole('link', {
        name: '＼マイページTOPに戻る／',
      });

      expect(button).toBeInTheDocument();
    });
  });
});
