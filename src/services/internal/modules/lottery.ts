// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import service from '..';
import {
  IGetLotteryPayload,
  IGetLotteryResponse,
  ILotteryApplyResponse,
} from '@/types/lottery';

const basePath = '/lotteries';

const lotteryApi = {
  getLottery: (payload: IGetLotteryPayload) =>
    service.post<IGetLotteryResponse>(
      `${basePath}/get-lottery-on-rich-menu`,
      payload
    ),
  apply: (payload: FormData) =>
    service.upload<ILotteryApplyResponse>(`${basePath}/member-apply`, payload),
};

export default lotteryApi;
