// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
export interface IUserInfo {
  line_user_id: string;
  peer_conne_id: string;
  email: string;
  company_id: string;
  birthday: string;
  gender: string;
  prefecture: string;
  is_follow: boolean;
  current_point: number;
}

export interface IUserRegisterForm {
  email: string;
  password: string;
  confirmPassword: string;
  birthYear: string;
  birthMonth: string;
  prefecture: string;
  gender: string;
  isAcceptPolicies: boolean;
  isAcceptTerms: boolean;
}

export interface IChangeUserInformationForm {
  email: string;
  birthYear: string;
  birthMonth: string;
  prefecture: string;
  gender: string;
}

export interface IUserGender {
  id: string;
  label: string;
  value: string;
}

export interface IUserRegisterPayload {
  memberLineId: string;
  companyId: string;
  email: string;
  password: string;
  birthday: string;
  gender: string;
  prefecture: string;
  isFollow: boolean;
}

export interface IUserUpdatePayload {
  memberLineId: string;
  email: string;
  birthday: string;
  gender: string;
  prefecture: string;
}

export interface IUserUpdateResponse {
  birthday: string;
  email: string;
  gender: string;
  prefecture: string;
}

export interface IUserRegisterResponse {
  memberLineId: string;
  email: string;
  companyId: string;
  amountPoint: number;
  birthday: string;
  gender: string;
  prefecture: string;
  isFollow: boolean;
  accessToken: string;
}

export interface IVerifyUserPayload {
  accessTokenLine: string;
}

export type IVerifyUserResponse = Pick<IUserInfo, 'isRegister' | 'accessToken'>;

export interface IUpdateCompanyIdPayload {
  companyId: string;
  memberLineId: string;
}

export interface IUpdateCompanyIdResponse {
  isSuccess?: boolean;
  errors?: {
    statusCode: number;
    message: string;
    error?: string;
  };
}

export interface IWithDrawResponse {
  withdrawal_at: string;
}

export interface IIsRegisteredPayload {
  lineUserId: string;
}

export interface IIsRegisteredResponse {
  isRegister: boolean;
}

export interface IVerifyUserV2Payload {
  line_access_token: string;
  company_id?: string | undefined;
  service_id?: string;
  token?: string;
  is_generate_token?: boolean;
}
export interface IVerifyUserV2Response {
  is_member: boolean;
  is_user_linked_with_inflow_company: boolean;
  external_link_company?: string;
  access_token?: string;
  peer_conne_id?: string;
  partner_group_ids?: string[];
  company?: { id: string; name: string; partner_group_ids: string[] };
  registrable_partner_group_ids?: string[];
  member_information?: IUserInfo;
  external_linked?: 'success' | 'failed';
}

export interface IUserLoginPayload {
  email: string;
  password: string;
  auth_method: string;
  line_access_token: string;
}

export interface IUserLoginResponseProfile {
  email: string;
  birthday: string;
  gender: string;
  prefecture: string;
  phone: string;
}

export interface IUserLoginResponse {
  profile: IUserLoginResponseProfile;
  access_token: string;
  partner_group_ids: string[];
}

export interface ImemberStatus {
  is_member: boolean;
  is_user_linked_with_inflow_company: boolean;
}
