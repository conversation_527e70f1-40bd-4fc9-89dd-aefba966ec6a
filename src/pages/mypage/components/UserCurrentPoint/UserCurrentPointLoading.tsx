// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React, { FC } from 'react';
import { UserCurrentPointProps } from './UserCurrentPoint';

export const UserCurrentPointLoading: FC<UserCurrentPointProps> = ({
  layout,
}) => {
  return (
    <div
      data-testid="loading"
      className="w-full animate-pulse flex flex-col justify-center items-center bg-theme-primary/10 rounded gap-4 p-4"
    >
      {layout === 'short' ? (
        <div className="w-[240px] h-9 bg-slate-200 rounded" />
      ) : layout === 'medium' ? (
        <>
          <div className="w-[240px] h-9 bg-slate-200 rounded" />
          <div className="w-[174px] h-11 bg-slate-200 rounded" />
        </>
      ) : (
        <>
          <div className="w-[240px] h-9 bg-slate-200 rounded" />
          <div className="w-[174px] h-11 bg-slate-200 rounded" />
          <div className="w-[112px] h-5 bg-slate-200 rounded" />
        </>
      )}
    </div>
  );
};
