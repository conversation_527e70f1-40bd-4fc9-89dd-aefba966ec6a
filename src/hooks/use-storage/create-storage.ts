// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
/* eslint-disable react-hooks/exhaustive-deps */
import { useState, useCallback, useEffect } from 'react';

export type StorageType = 'localStorage' | 'sessionStorage';

export interface IStorageProperties<T> {
  /** Storage key */
  key: string;

  /** Default value that will be set if value is not found in storage */
  defaultValue?: T;

  /** If set to true, value will be update is useEffect after mount */
  getInitialValueInEffect?: boolean;

  /** Function to serialize value into string to be save in storage */
  serialize?(value: T): string;

  /** Function to deserialize string value from storage to value */
  deserialize?(value: string): T;

  /** if has callback, it will executed */
  callback?: (value: T) => void;
}

function serializeJSON<T>(value: T, hookName: string) {
  try {
    return JSON.stringify(value);
  } catch (error) {
    throw new Error(`${hookName}: Failed to serialize the value`);
  }
}

function deserializeJSON(value: string) {
  try {
    return JSON.parse(value);
  } catch {
    return value;
  }
}

export function createStorage<T>(type: StorageType, hookName: string) {
  return function useStorage({
    key,
    defaultValue = undefined,
    getInitialValueInEffect = true,
    deserialize = deserializeJSON,
    serialize = (value: T) => serializeJSON(value, hookName),
    callback,
  }: IStorageProperties<T>) {
    const readStorageValue = useCallback(
      (skipStorage?: boolean): T => {
        if (
          typeof window === 'undefined' ||
          !(type in window) ||
          window[type] === null ||
          skipStorage
        ) {
          return defaultValue as T;
        }

        const storageValue = window[type].getItem(key);

        return storageValue !== null
          ? deserialize(storageValue)
          : (defaultValue as T);
      },
      [key, defaultValue]
    );

    const [value, setValue] = useState<T>(
      readStorageValue(getInitialValueInEffect)
    );

    const setStorageValue = useCallback(
      (val: T | ((prevState: T) => T)) => {
        if (val instanceof Function) {
          setValue(current => {
            const result = val(current);
            window[type].setItem(key, serialize(result));
            return result;
          });
        } else {
          window[type].setItem(key, serialize(val));
          setValue(val);
        }
      },
      [key]
    );

    const removeStorageValue = useCallback(() => {
      window[type].removeItem(key);
    }, []);

    useEffect(() => {
      if (defaultValue !== undefined && value === undefined) {
        setStorageValue(defaultValue);
      }
    }, [defaultValue, value, setStorageValue]);

    useEffect(() => {
      if (getInitialValueInEffect) {
        setValue(readStorageValue());
      }
    }, []);

    useEffect(() => {
      if (typeof callback === 'function') {
        callback(readStorageValue());
      }
    }, []);

    return [
      value === undefined ? defaultValue : value,
      setStorageValue,
      removeStorageValue,
    ] as const;
  };
}
