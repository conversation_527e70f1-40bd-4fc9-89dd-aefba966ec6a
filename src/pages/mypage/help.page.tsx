// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC, useState } from 'react';
import { useRouter } from 'next/router';
import { ChevronRightIcon } from '@heroicons/react/24/solid';
import { IMenuItem, HELP_MENU } from '@/constants/mypage';
import { SHOW_PRIVACY_MODAL, SHOW_TERMS_MODAL } from '@/constants/common';
import { useLiff } from '@/contexts/liff';
import { Button } from '@/components/Button';
import TermsModal from '@/components/Modal/TermsModal';
import PoliciesModal from '@/components/Modal/PoliciesModal';
import MypageLayout from './components/MypageLayout';

const HelpScreen: FC = () => {
  const router = useRouter();
  const { show } = router.query;
  const { liff } = useLiff();
  const [isShowPoliciesModal, setIsShowPoliciesModal] = useState(
    () => show === SHOW_PRIVACY_MODAL
  );
  const [isShowTermsModal, setIsShowTermsModal] = useState(
    () => show === SHOW_TERMS_MODAL
  );

  const onclick = (item: IMenuItem) => {
    if (item.type === 'policies') setIsShowPoliciesModal(true);
    if (item.type === 'terms') setIsShowTermsModal(true);
    if (item.to) {
      if (item.type === 'redirect') {
        liff.openWindow({
          url: item.to,
          external: false,
        });
      } else {
        router.push(item.to);
      }
    }
  };

  return (
    <>
      <MypageLayout spacingBottom="4xl">
        <div className="space-y-2">
          {HELP_MENU.map((item: IMenuItem) => (
            <button
              key={item.label}
              className="w-full h-[64px] border-solid border rounded border-gray-10 px-3 flex justify-between items-center cursor-pointer"
              onClick={() => onclick(item)}
            >
              <div className="flex items-center">
                <div className="ml-3 text-gray-80 text-sm font-normal truncate">
                  {item.label}
                </div>
              </div>

              <ChevronRightIcon className="w-4 h-4 stroke-[2px] stroke-gray-80" />
            </button>
          ))}
        </div>
      </MypageLayout>
      <PoliciesModal
        isShowModal={isShowPoliciesModal}
        setIsShowModal={setIsShowPoliciesModal}
        customFooter={
          <Button
            outline
            fullSized
            type="submit"
            size="xl"
            color="primary"
            onClick={() => setIsShowPoliciesModal(false)}
          >
            閉じる
          </Button>
        }
      />
      <TermsModal
        title="会員規約"
        isShowModal={isShowTermsModal}
        setIsShowModal={setIsShowTermsModal}
        customFooter={
          <Button
            outline
            fullSized
            type="submit"
            size="xl"
            color="primary"
            onClick={() => setIsShowTermsModal(false)}
          >
            閉じる
          </Button>
        }
      />
    </>
  );
};

export default HelpScreen;
