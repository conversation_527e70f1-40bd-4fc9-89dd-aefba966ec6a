// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { Button } from '@/components/Button';
import Loading from '@/components/Spinner/Loading';
import { routers } from '@/constants/routes';
import { NOT_FOUND } from '@/constants/status-response';
import { useFetch } from '@/hooks/use-fetch';
import { NewsError } from '@/pages/mypage/news-list/components/NewsError';
import newsLiffApi from '@/services/internal/modules/news-list';
import { INewsLiffDetailResponse } from '@/types/news-list';
import { convertToCharacterSeparatedFormat } from '@/utils/time';
import router from 'next/router';
import { FC } from 'react';

interface IOError {
  errors: {
    error: string;
    message: string;
    statusCode: number;
  };
}

const AnnouncementDetailScreen: FC = () => {
  const { newsId } = router.query;

  const { isFetching, data, isErrorFetching, fetch } =
    useFetch<INewsLiffDetailResponse>({
      dependencies: [],
      fetcher: () => newsLiffApi.getNewsLiffDetail({ id: newsId as string }),
      initialValue: {} as INewsLiffDetailResponse,
      handleError(error) {
        const _error = (error as IOError).errors;
        if (_error.statusCode === NOT_FOUND) {
          router.push(routers.notFound, router.asPath);
        }
      },
    });

  return (
    <>
      {isFetching ? (
        <Loading />
      ) : (
        Object.keys(data).length && (
          <div className="container mx-auto">
            <div className="w-full md:max-w-md mx-auto pt-6 min-h-screen flex flex-col justify-between">
              {isErrorFetching ? (
                <>
                  <div />
                  <NewsError retry={fetch} title="お知らせ詳細" />
                </>
              ) : (
                <div className="main">
                  <span className="font-normal text-[10px] leading-[14.48px] block">
                    {data?.published_at &&
                      convertToCharacterSeparatedFormat(
                        data?.published_at,
                        'dot'
                      )}
                  </span>
                  <h6 className="my-6 text-xl text-theme-primary pb-2 border-b border-theme-primary">
                    {data?.title}
                  </h6>
                  <p className="text-sm leading-[150%] whitespace-pre-line">
                    {data?.content}
                  </p>
                </div>
              )}

              <div className="pt-8">
                <Button
                  fullSized
                  size="xl"
                  color="link-primary"
                  customSpan="leading-6"
                  href={routers.myPageTop}
                >
                  ＼マイページTOPに戻る／
                </Button>
              </div>
            </div>
          </div>
        )
      )}
    </>
  );
};

export default AnnouncementDetailScreen;
