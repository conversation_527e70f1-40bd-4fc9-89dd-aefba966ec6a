// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import {
  act,
  fireEvent,
  render,
  screen,
  waitFor,
  within,
} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import TermsModal from '@/components/Modal/TermsModal';
import { Button } from '@/components/Button';
import { useState } from 'react';
import { IModalProps } from '@/components/Modal';

const TestModal = ({ root }: Pick<IModalProps, 'root'>): JSX.Element => {
  const [open, setOpen] = useState(false);

  return (
    <>
      <Button onClick={() => setOpen(true)}>Toggle modal</Button>
      <TermsModal root={root} isShowModal={open} setIsShowModal={setOpen} />
    </>
  );
};

describe('components/Modal/TermsModal.spec.tsx', () => {
  describe('A11y', () => {
    it('should be removed from DOM and garbage collected', async () => {
      const root = document.createElement('div');

      const { unmount } = render(<TestModal root={root} />);

      unmount();

      await waitFor(() => expect(root.childNodes.length).toBe(0));
    });

    it('should have `role="dialog"`', async () => {
      const root = document.createElement('div');
      const user = userEvent.setup();

      render(<TestModal root={root} />);

      const openButton = screen.getByRole('button');

      await act(() => user.click(openButton));

      const modal = within(root).getByRole('dialog');
      const closeButton = within(modal).getByText('閉じる');

      expect(modal).toHaveAttribute('aria-hidden', 'false');

      await act(() => user.click(closeButton));

      expect(modal).toHaveAttribute('aria-hidden', 'true');
    });

    it('should view all terms`', async () => {
      const root = document.createElement('div');
      const user = userEvent.setup();

      render(<TestModal root={root} />);

      const openButton = screen.getByRole('button');

      await act(() => user.click(openButton));

      const modal = within(root).getByRole('dialog');

      const closeButton = within(modal).getByText('会員規約に同意する');

      // eslint-disable-next-line testing-library/no-node-access
      fireEvent.scroll(modal.querySelector('.overflow-x-auto') || window, {
        target: { scrollY: 1500 },
      });

      await act(() => user.click(closeButton));

      expect(modal).toHaveAttribute('aria-hidden', 'false');
    });
  });
});
