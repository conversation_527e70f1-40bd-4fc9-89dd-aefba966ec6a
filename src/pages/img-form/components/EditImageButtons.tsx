// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { Button } from '@/components/Button';
import { SvgIcon } from '@/components/SvgIcon';
import React from 'react';
import ImageUpload from './ImageUpload';

interface EditImageButtonsProps {
  disabled: boolean;
  index: number;
  handleDeleteImage: (index: number) => void;
  handleUploadImages: (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => void;
}
const EditImageButtons: React.FC<EditImageButtonsProps> = ({
  disabled,
  index,
  handleDeleteImage,
  handleUploadImages,
}) => {
  return (
    <div className="flex mt-2 gap-5 items-center">
      <Button
        onClick={() => handleDeleteImage(index)}
        color="white"
        outline
        className="rounded-[4px]"
        customSpan="!px-4 !py-[6px]"
        disabled={disabled}
      >
        <SvgIcon name="Trash" height={20} />
        <span className="text-theme-primary ml-3 whitespace-nowrap">
          画像を削除する
        </span>
      </Button>
      <Button
        className="relative rounded-[4px]"
        color="white"
        outline
        customSpan="!px-4 !py-[6px]"
        disabled={disabled}
      >
        <SvgIcon name="Change" />
        <span className="text-theme-primary ml-3 whitespace-nowrap">
          画像を変更する
        </span>
        <ImageUpload
          onChange={e => handleUploadImages(e, index)}
          disabled={disabled}
        />
      </Button>
    </div>
  );
};

export default EditImageButtons;
