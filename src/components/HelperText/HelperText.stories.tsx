// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import type { Meta, Story } from '@storybook/react';
import type { IHelperTextProps } from './HelperText';
import { HelperText } from './HelperText';
import { styles } from './HelperText.styles';

export default {
  title: 'Components/HelperText',
  component: HelperText,
  argTypes: {
    color: {
      options: Object.keys(styles.colors),
      control: { type: 'radio' },
    },
  },
} as Meta;

const Template: Story<IHelperTextProps> = args => <HelperText {...args} />;

export const Default = Template.bind({});
Default.storyName = 'HelperText';
Default.args = {
  color: 'info',
  value: 'Default helper text example',
};
