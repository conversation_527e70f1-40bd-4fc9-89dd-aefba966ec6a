// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import service from '..';
import {
  IServiceLinkagePayload,
  IServiceLinkageResponse,
  IVerifyExternalToPeerConneLinkageRequest,
  IVerifyExternalToPeerConneLinkageResponse,
} from '@/types/linkage';

const basePath = '/service';

const peerConneServiceApi = {
  getPeerConneToken: (payload: IServiceLinkagePayload) =>
    service.post<IServiceLinkageResponse>(`${basePath}/linkage`, payload),
  verifyExternalToPeerConneLinkage: (
    payload: IVerifyExternalToPeerConneLinkageRequest
  ) =>
    service.post<IVerifyExternalToPeerConneLinkageResponse>(
      `${basePath}/external/verify-token`,
      payload
    ),
};

export default peerConneServiceApi;
