// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import {
  IVerifyRedirectExternalPayload,
  IVerifyRedirectGifteePayload,
  IVerifyRedirectExternalResponse,
  IVerifyRedirectGifteeResponse,
  IVerifyRedirectGoogleFormPayload,
  IVerifyRedirectGoogleFormResponse,
  IVerifyRedirectKyosaiId,
  IVerifyRedirectKyosaiIdResponse,
  IVerifyRedirectShotanId,
  IVerifyRedirectShotanIdResponse,
} from '@/types/redirect';
import service from '..';

const baseUrl = '/redirects';

const redirectApi = {
  verifyRedirectExternalUrl: (payload: IVerifyRedirectExternalPayload) =>
    service.post<IVerifyRedirectExternalResponse>(
      `${baseUrl}/verify-redirect-url`,
      payload
    ),
  verifyRedirectGifteeUrl: (payload: IVerifyRedirectGifteePayload) =>
    service.post<IVerifyRedirectGifteeResponse>(
      `${baseUrl}/giftee-prize-url/${payload.lotteryId}`
    ),
  verifyRedirectGoogleFormUrl: (payload: IVerifyRedirectGoogleFormPayload) =>
    service.post<IVerifyRedirectGoogleFormResponse>(
      `${baseUrl}/questionnaire/${payload.questionnaireId}`
    ),
  verifyRedirectKyosai: (payload: IVerifyRedirectKyosaiId) =>
    service.get<IVerifyRedirectKyosaiIdResponse>(`/kyosai/${payload.kyosaiId}`),
  verifyRedirectShotan: (payload: IVerifyRedirectShotanId) =>
    service.get<IVerifyRedirectShotanIdResponse>(`/shotan/${payload.shotanId}`),
};

export default redirectApi;
