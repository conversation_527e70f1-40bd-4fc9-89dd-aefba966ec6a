// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { OptionType } from '@/components/Select2';
import { Message } from '@/constants/message';
import { useFetch } from '@/hooks/use-fetch';
import messageDeliverySettingsApi from '@/services/internal/modules/message-delivery-settings';
import { IGroupedCompanies } from '@/types/message-delivery-settings';
import { useEffect, useState } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { toast } from 'react-toastify';

interface IUseMessageDeliverySettingsChange {
  companies: IGroupedCompanies;
  myPartnerGroup: OptionType[];
  companyOn: string[];
  reCallApi: () => void;
}

export const useMessageDeliverySettingsChange = ({
  companies,
  myPartnerGroup,
  companyOn = [],
  reCallApi,
}: IUseMessageDeliverySettingsChange) => {
  const [isOpenModalConfirm, setIsOpenModalConfirm] = useState(false);
  const [valueSelected, setValueSelected] = useState<OptionType>(
    myPartnerGroup[0]
  );
  const [preValueSelected, setPreValueSelected] = useState<OptionType>();
  const [valueSelectedTemp, setValueSelectedTemp] = useState<OptionType>();

  useEffect(() => {
    valueSelected.value && setPreValueSelected(valueSelected);
  }, [valueSelected]);

  const { register, handleSubmit, getValues, reset, control, setValue } =
    useForm({
      mode: 'onChange',
      defaultValues: {
        companies: companyOn.length ? companyOn : [],
      },
    });

  useWatch({
    name: 'companies',
    control,
  });

  const { fetch: changeProfileInformation, isFetching } = useFetch({
    fetcher: messageDeliverySettingsApi.changeMessageDeliverySetting,
    handleResponse: () => {
      toast.success(Message.success.updateDeliverySetting);
      reset(getValues());
      reCallApi();
    },
    dependencies: false,
  });

  const handleSubmitForm = (data: { companies: string[] }) => {
    const listCompanyCurrentGroup = companies[valueSelected.value].map(
      item => item.company_id
    );

    let listOff: string[];
    if (data.companies.length === 0) {
      listOff = listCompanyCurrentGroup;
    } else {
      listOff = listCompanyCurrentGroup.filter(
        companyId => !data.companies.includes(companyId)
      );
    }

    const newData = Array.from(
      new Set([...companyOn, ...data.companies].map(item => item))
    ).filter(item => !listOff.includes(item));

    changeProfileInformation({ companies: newData });
    setValue('companies', newData);
  };

  const onCancelEdit = (allowEdit: boolean) => {
    setIsOpenModalConfirm(false);
    setValueSelected(valueSelectedTemp as OptionType);
    if (allowEdit) handleSubmit(handleSubmitForm)();
    else reset();
  };

  return {
    isOpenModalConfirm,
    setIsOpenModalConfirm,
    valueSelected,
    setValueSelected,
    preValueSelected,
    setValueSelectedTemp,
    register,
    handleSubmit,
    isFetching,
    handleSubmitForm,
    onCancelEdit,
    getValues,
    setValue,
  };
};
