// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React, { useState } from 'react';
import { IHistoryItem, IPointHistoriesResponse } from '@/types/point';
import PointHistoryItem from './PointHistoryItem';
import pointApi from '@/services/internal/modules/point';
import { Button } from '@/components/Button';
import { useFetch } from '@/hooks/use-fetch';
import { PointHistoryListError } from './PointHistoryListError';
import { PointHistoryListEmpty } from './PointHistoryListEmpty';
import { PointHistoryListLoading } from './PointHistoryListLoading';
import { Spinner } from '@/components/Spinner';

export const PointHistoryList = () => {
  const [displayHistories, setDisplayHistories] = useState<IHistoryItem[]>([]);
  const [isDisplayButtonShowMore, setIsDisplayButtonShowMore] = useState(true);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const {
    fetch: getPointHistoriesHandler,
    isFetching,
    isErrorFetching,
  } = useFetch<IPointHistoriesResponse>({
    fetcher: () => pointApi.getPointHistories({ page: currentPage, limit: 30 }),
    handleResponse: (res: IPointHistoriesResponse) => {
      setDisplayHistories(prev => [...prev, ...res.point_histories]);
      setIsDisplayButtonShowMore(res.hasMoreResults);
      return res;
    },
    initialValue: {
      hasMoreResults: false,
      current_point_amount: 0,
      point_histories: [],
    },
    dependencies: [currentPage],
  });

  return (
    <>
      {isFetching && currentPage === 1 ? (
        <PointHistoryListLoading />
      ) : isErrorFetching ? (
        <PointHistoryListError retry={getPointHistoriesHandler} />
      ) : displayHistories.length > 0 ? (
        <div className="flex flex-col flex-1 justify-between gap-4">
          <ul className="mt-4">
            {displayHistories.map((history, index) => (
              <PointHistoryItem
                key={index}
                companyLogo={history.company_image}
                companyName={history.company_name}
                gotAt={history.event_datetime}
                pointAmount={history.amount}
                reason={history.event_name}
                event_type={history.event_type}
              />
            ))}
          </ul>
          {/* Footer */}
          {isDisplayButtonShowMore && (
            <Button
              fullSized
              type="button"
              outline
              size="xl"
              color="primary"
              onClick={() => setCurrentPage(prev => prev + 1)}
              disabled={isFetching}
            >
              {isFetching && (
                <div className="mr-2">
                  <Spinner size="md" color="primary" />
                </div>
              )}
              もっとみる
            </Button>
          )}
        </div>
      ) : (
        <PointHistoryListEmpty />
      )}
    </>
  );
};
