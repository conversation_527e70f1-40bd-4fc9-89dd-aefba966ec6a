// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { renderHook, act } from '@testing-library/react';
import { useWindowEvent } from '@/hooks/use-window-event';

describe('hooks/use-window-event/use-window-event.spec.tsx', () => {
  afterAll(() => {
    jest.clearAllMocks();
  });

  describe('User interactions', () => {
    it('should call listener, add and remove event listener with the provided type and listener', () => {
      const addEventListenerSpy = jest.spyOn(window, 'addEventListener');
      const removeEventListenerSpy = jest.spyOn(window, 'removeEventListener');

      const type = 'online';
      const listener = jest.fn();

      const { unmount } = renderHook(() => useWindowEvent(type, listener));

      expect(addEventListenerSpy).toHaveBeenCalledTimes(1);

      act(() => {
        window.dispatchEvent(new Event(type));
      });

      expect(listener).toHaveBeenCalledTimes(1);

      unmount();

      expect(removeEventListenerSpy).toHaveBeenCalledTimes(1);
    });
  });
});
