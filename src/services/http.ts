// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import axios, {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
} from 'axios';
import { USER_ACCESS_TOKEN, USER_ACCESS_TOKEN_EXPIRES } from '@/constants/user';
import {
  parseCookiesApp,
  destroyCookieApp,
  setCookieApp,
} from '@/utils/cookie';
import { routers } from '@/constants/routes';
import {
  MAINTENANCE_MODE,
  SUCCEEDED,
  CREATED,
  UNAUTHENTICATED,
  FORBIDDEN,
} from '@/constants/status-response';
import { redirectWithQueryActiveHistory } from '@/utils/redirect';
import { UNKNOWN } from '@/constants/define';
import authApi from './internal/modules/auth';

let isRefreshing = false;
const URL_VERIFY_MEMBER_API = '/members/verify-member-v2';
const URL_LOGIN_API = '/auth/login';

const handleReLoginLine = () => {
  destroyCookieApp(null, USER_ACCESS_TOKEN);
  localStorage.clear();
  sessionStorage.clear();

  location.pathname === routers.service
    ? location.reload()
    : location.replace(routers.home);
};

export const customAxiosInstance = (baseURL: string): AxiosInstance => {
  const axiosInstance: AxiosInstance = axios.create({
    baseURL,
    timeout: 1000 * 30,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  return axiosInstance;
};
export const customAxiosInterceptors = (axiosInstance: AxiosInstance) => {
  axiosInstance.interceptors.request.use(
    (config: AxiosRequestConfig) => {
      const baseURL = config.baseURL;
      if (baseURL === process.env.INTERNAL_BASE_URL) {
        const { accessToken } = parseCookiesApp();

        // case not have accesstoken we not pass bearer to prevent infinity call api
        if (accessToken && config.url !== URL_VERIFY_MEMBER_API) {
          config.headers = {
            ...config.headers,
            Authorization: `Bearer ${accessToken}`,
          };
        }
      }

      return config;
    },
    (error: AxiosError) => {
      return Promise.reject(error);
    }
  );

  axiosInstance.interceptors.response.use(
    (response: AxiosResponse) => {
      if (response.status === SUCCEEDED || response.status === CREATED) {
        return response.data;
      }
      return response;
    },
    async (error: AxiosError) => {
      const { response, config } = error;

      if (response) {
        const baseURL = response.config.baseURL;
        if (baseURL === process.env.INTERNAL_BASE_URL) {
          // 503
          if (error.response?.status === MAINTENANCE_MODE) {
            await redirectWithQueryActiveHistory(routers.maintenance);
          }
          //403 && not URL_LOGIN_API
          else if (
            error.response?.status === FORBIDDEN &&
            config?.url !== URL_LOGIN_API
          ) {
            await redirectWithQueryActiveHistory(routers.membershipError);
          }
          //401 && URL_VERIFY_MEMBER_API
          else if (
            config?.url === URL_VERIFY_MEMBER_API &&
            error.response?.status === UNAUTHENTICATED
          ) {
            handleReLoginLine();
          }
          //401 && !URL_LOGIN_API && rest api
          else if (
            config?.url !== URL_LOGIN_API &&
            error.response?.status === UNAUTHENTICATED
          ) {
            if (!isRefreshing) {
              isRefreshing = true;
              try {
                const { line_access_token = UNKNOWN, company_id } =
                  parseCookiesApp();

                const result = await authApi.verifyUserV2({
                  line_access_token: line_access_token as string,
                  company_id: company_id as string,
                });

                const { access_token, is_member } = result;
                !is_member && location.replace(routers.membershipOptions);

                access_token &&
                  setCookieApp(null, USER_ACCESS_TOKEN, access_token, {
                    maxAge: USER_ACCESS_TOKEN_EXPIRES,
                  });
                location.reload();
              } catch (e: unknown) {
                const { errors } = e as unknown as {
                  errors: { statusCode: number };
                };
                errors?.statusCode !== UNAUTHENTICATED &&
                  (await redirectWithQueryActiveHistory(
                    routers.friendshipError
                  ));
              } finally {
                isRefreshing = false;
              }
            }
          }
        }

        return Promise.reject(response.data);
      }
      return Promise.reject(error);
    }
  );
};

export const customAxiosService = (axiosInstance: AxiosInstance) => {
  const service = {
    get<T = unknown>(url: string, data?: object): Promise<T> {
      return axiosInstance.get(url, { params: data });
    },

    post<T = unknown>(url: string, data?: object): Promise<T> {
      return axiosInstance.post(url, data);
    },

    put<T = unknown>(url: string, data?: object): Promise<T> {
      return axiosInstance.put(url, data);
    },

    patch<T = unknown>(url: string, data?: object): Promise<T> {
      return axiosInstance.patch(url, data);
    },

    delete<T = unknown>(url: string, data?: object): Promise<T> {
      return axiosInstance.delete(url, data);
    },

    upload<T = unknown>(url: string, file: FormData | File): Promise<T> {
      return axiosInstance.post(url, file, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
    },
  };

  return service;
};
