// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { ForwardedRef, MutableRefObject, useEffect } from 'react';

interface UseGetItemIdType {
  ref: ForwardedRef<HTMLDivElement>;
  asPath: string;
  id: number;
}

export const useItemIdScroll = ({ asPath, ref, id }: UseGetItemIdType) => {
  let item_id = '';
  const asPathList = asPath.includes('?') ? asPath.split('?') : '';

  if (asPathList) {
    const query = asPathList[asPathList.length - 1];

    const { item_id: query_item_id } = JSON.parse(
      '{"' +
        decodeURI(query)
          .replace(/"/g, '\\"')
          .replace(/&/g, '","')
          .replace(/=/g, '":"') +
        '"}'
    );
    item_id = query_item_id;
  }

  useEffect(() => {
    if (item_id && item_id === `new${id}` && ref) {
      (ref as MutableRefObject<HTMLDivElement>).current?.scrollIntoView();
    }
  }, []);

  return { item_id };
};
