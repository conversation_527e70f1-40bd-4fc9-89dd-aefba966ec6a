// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { routers } from '@/constants/routes';
import { TOP_SCREEN_MENU } from '@/constants/mypage';
import MenuItem from './components/MenuItem';
import MypageLayout from './components/MypageLayout';
import { UserCurrentPoint } from './components/UserCurrentPoint';
import PointNavigation from '@/pages/mypage/components/PointNavigation';

const MyPageTopScreen: FC = () => {
  return (
    <>
      <MypageLayout spacingTop="lg" withFooter={false}>
        <div className="space-y-6">
          {/* Header */}
          <UserCurrentPoint
            layout="medium"
            className="pt-6"
            action={{ href: routers.pointHistory }}
            note={{ className: '-mt-1' }}
          />

          {/* Point Navigation */}
          <PointNavigation />

          {/* Menu */}
          <ul>
            {TOP_SCREEN_MENU.map((menuItem, index) => (
              <MenuItem
                key={index}
                label={menuItem.label}
                icon={menuItem.icon}
                to={menuItem.to}
              />
            ))}
          </ul>
        </div>
      </MypageLayout>
    </>
  );
};

export default MyPageTopScreen;
