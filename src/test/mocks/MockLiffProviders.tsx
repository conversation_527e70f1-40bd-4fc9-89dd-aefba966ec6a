// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
/* eslint-disable import/no-extraneous-dependencies */
import { FC, ReactNode } from 'react';
import mockRouter from 'next-router-mock';
import { LiffContext } from '@/contexts/liff';
import { USER_INFO, USER_LINE_INFO } from '@/mocks/users';
import { Liff } from '@line/liff/dist/lib';
import { ImemberStatus, IUserInfo } from '@/types/user';

export interface MockLiffProvidersProps {
  liff?: Liff;
  error?: unknown;
  isLoggedIn?: boolean;
  userInfo?: IUserInfo;
}

interface MockProvidersProps {
  route?: string;

  contextValue?: MockLiffProvidersProps;
  children: ReactNode;
}

const defaultContextValue: MockLiffProvidersProps = {
  error: undefined,
  isLoggedIn: true,
  liff: { getContext: () => ({}) } as Liff,
  userInfo: USER_INFO,
};

export const LiffContextMockProviders: FC<MockProvidersProps> = ({
  route = '/',
  contextValue = defaultContextValue,
  children,
}) => {
  mockRouter.setCurrentUrl(route);

  const defaultLiffContextValue = {
    error: undefined,
    isLoggedIn: true,
    isReady: true,
    liff: { getContext: () => ({}) } as Liff,
    userInfo: USER_INFO,
    setUserInfo: () => {},
    userLineInfo: USER_LINE_INFO,
    setUserLineInfo: () => {},
    setError: () => {},
    memberStatus: {} as ImemberStatus,
    setMemberStatus: () => {},
  };

  return (
    <LiffContext.Provider
      value={{ ...defaultLiffContextValue, ...contextValue }}
    >
      {children}
    </LiffContext.Provider>
  );
};
