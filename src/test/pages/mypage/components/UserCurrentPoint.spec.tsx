// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen, waitFor } from '@testing-library/react';
import pointApi from '@/services/internal/modules/point';
import { UserCurrentPoint } from '@/pages/mypage/components/UserCurrentPoint';

describe('pages/mypage/components/UserCurrentPoint.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<UserCurrentPoint />);
      const loading = screen.getByTestId('loading');
      expect(loading).toBeInTheDocument();
    });
  });

  describe('Fetching', () => {
    describe('Fetching success', () => {
      it('should call api success return response', async () => {
        jest.spyOn(pointApi, 'getCurrentPoint').mockResolvedValue(1000000);

        render(<UserCurrentPoint />);

        await waitFor(() => {
          const heading = screen.getAllByRole('heading');
          expect(heading.length).toEqual(1);
        });
      });
    });

    describe('Fetching failure', () => {
      it('should call api failure return error', async () => {
        jest
          .spyOn(pointApi, 'getCurrentPoint')
          .mockRejectedValue({ statusCode: 500 });

        render(<UserCurrentPoint />);

        await waitFor(async () => {
          const button = screen.getAllByRole('button');
          expect(button.length).toEqual(1);
        });
      });
    });
  });

  describe('Props', () => {
    beforeEach(() => {
      jest.spyOn(pointApi, 'getCurrentPoint').mockResolvedValue(1000000);
    });

    it('should render when `layout={medium}`', async () => {
      render(<UserCurrentPoint layout="medium" />);

      await waitFor(async () => {
        const button = screen.getAllByRole('button');
        expect(button.length).toEqual(1);
      });
    });

    it('should render when `layout={full}`', async () => {
      render(<UserCurrentPoint layout="full" link={{ href: '/lorem' }} />);

      await waitFor(async () => {
        const link = screen.getAllByRole('link');
        expect(link.length).toEqual(1);
      });
    });
  });
});
