// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import type { Liff } from '@line/liff';
import redirectApi from '@/services/internal/modules/redirect';
import RedirectScreen from '@/pages/re/ex/index.page';
import { LiffContextMockProviders } from '@/test/mocks';
import { EBrowserType } from '@/constants/enum';

jest.mock('next/router', () => require('next-router-mock'));
jest.mock('axios', () => {
  return {
    create: jest.fn(() => ({
      get: jest.fn(),
      interceptors: {
        request: { use: jest.fn(), eject: jest.fn() },
        response: { use: jest.fn(), eject: jest.fn() },
      },
    })),
  };
});

interface TestRedirectExScreenProps {
  os?: 'ios' | 'android' | 'web';
}

const TestRedirectExScreen: FC<TestRedirectExScreenProps> = ({
  os = 'web',
}): JSX.Element => {
  const LiffContextValue = {
    liff: { getOS: () => os, getContext: () => ({}) } as Liff,
  };

  return (
    <LiffContextMockProviders contextValue={LiffContextValue}>
      <RedirectScreen />
    </LiffContextMockProviders>
  );
};

describe('pages/re/ex/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<TestRedirectExScreen />);

      await waitFor(() => {
        expect(screen.getByRole('status')).toBeInTheDocument();
      });
    });
  });

  describe('Fetching', () => {
    it('should call api return response with url_type=app and os=android', async () => {
      const spy = jest.spyOn(redirectApi, 'verifyRedirectExternalUrl');
      spy.mockResolvedValue({
        withPoint: true,
        redirect: 'https://example.com',
        url_type: 'app',
        browser_type: EBrowserType.external,
      });

      render(<TestRedirectExScreen os="android" />);

      await waitFor(() => {
        expect(screen.getByRole('status')).toBeInTheDocument();
      });
    });

    it('should call api return response with url_type=app and os=ios', async () => {
      const spy = jest.spyOn(redirectApi, 'verifyRedirectExternalUrl');
      spy.mockResolvedValue({
        withPoint: true,
        redirect: 'https://example.com',
        url_type: 'app',
        browser_type: EBrowserType.external,
      });

      render(<TestRedirectExScreen os="ios" />);

      await waitFor(() => {
        expect(screen.getByRole('status')).toBeInTheDocument();
      });
    });

    it('should call api return response with url_type=link and os=android', async () => {
      const spy = jest.spyOn(redirectApi, 'verifyRedirectExternalUrl');
      spy.mockResolvedValue({
        withPoint: true,
        redirect: 'https://example.com',
        url_type: 'link',
        browser_type: EBrowserType.liff,
      });

      render(<TestRedirectExScreen os="android" />);

      await waitFor(() => {
        expect(screen.getByRole('status')).toBeInTheDocument();
      });
    });

    it('should call api return response with url_type=web and os=web', async () => {
      const spy = jest.spyOn(redirectApi, 'verifyRedirectExternalUrl');
      spy.mockResolvedValue({
        withPoint: true,
        redirect: 'https://example.com',
        url_type: 'app',
        browser_type: EBrowserType.liff,
      });

      render(<TestRedirectExScreen os="web" />);

      await waitFor(() => {
        expect(screen.getByRole('status')).toBeInTheDocument();
      });
    });
  });
});
