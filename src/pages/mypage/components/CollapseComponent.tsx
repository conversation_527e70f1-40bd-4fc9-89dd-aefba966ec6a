// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC, useState } from 'react';
import { SvgIcon } from '@/components/SvgIcon';
import { ICollapse } from '@/constants/mypage';

const CollapseComponent: FC<ICollapse> = ({ categogy, content }) => {
  const [isHiddenQuestion, setIsHiddenQuestion] = useState(true);
  const [isHiddenAnswer, setIsHiddenAnswer] = useState<{
    [x: string]: boolean;
  }>({});

  return (
    <>
      <ul>
        <li
          className="pt-6 pb-[17px] border-b border-gray-10 flex items-center w-full text-left text-theme-primary font-bold"
          onClick={() => {
            setIsHiddenQuestion(!isHiddenQuestion);
            setIsHiddenAnswer({});
          }}
        >
          {categogy}
          <span className="ml-[15px]">
            <SvgIcon name={isHiddenQuestion ? 'Plus' : 'Minus'}></SvgIcon>
          </span>
        </li>
        {content.map((item, index) => (
          <li
            key={index}
            className={`py-[17px] pl-1.5 border-b border-gray-10 text-sm font-normal ${
              !!isHiddenQuestion && 'hidden'
            }`}
          >
            <ul className="text--indent">
              <li
                className="text--indent__faq break-all"
                onClick={() => {
                  setIsHiddenAnswer({
                    ...isHiddenAnswer,
                    [index]: !isHiddenAnswer[index],
                  });
                }}
              >
                <div className="relative flex items-center cursor-pointer">
                  <span className="text-sm font-normal max-w-[90%]">
                    <span className="text-theme-primary mr-2">Q. </span>
                    {item.question}
                  </span>
                  <div className="absolute right-0">
                    <SvgIcon
                      name={!isHiddenAnswer[index] ? 'Plus' : 'Minus'}
                    ></SvgIcon>
                  </div>
                </div>
              </li>
              <li
                className={`pt-[25px] pb-[7px] text--indent__faq ${
                  !isHiddenAnswer[index] && 'hidden'
                }`}
              >
                <span className="text-theme-primary mr-2">A. </span>
                <span dangerouslySetInnerHTML={{ __html: item.answer }}></span>
              </li>
            </ul>
          </li>
        ))}
      </ul>
    </>
  );
};

export default CollapseComponent;
