// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { BoxContent } from '@/components/BoxContent';
import { Button } from '@/components/Button';
import { SvgIcon } from '@/components/SvgIcon';
import { useLiff } from '@/contexts/liff';

const LotteryOutOfPeriod = () => {
  const { liff } = useLiff();

  return (
    <>
      <div className="container mx-auto">
        <BoxContent>
          <BoxContent.Body className="pt-[160px]">
            <div className="text-center text-base space-y-4">
              <div className="flex justify-center">
                <SvgIcon name={'OutOfPeriodLotteryIcon'} />
              </div>
              <h3 className="text-base font-bold">チャレンジ期間外です</h3>
              <p className="text-base">
                次回のチャレンジを
                <br />
                お待ちください。
              </p>
            </div>
          </BoxContent.Body>
          <BoxContent.Footer>
            <Button
              fullSized
              type="submit"
              size="xl"
              color="primary"
              onClick={() => liff.closeWindow()}
            >
              トーク画面に戻る
            </Button>
          </BoxContent.Footer>
        </BoxContent>
      </div>
    </>
  );
};

export default LotteryOutOfPeriod;
