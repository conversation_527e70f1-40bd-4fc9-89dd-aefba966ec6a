// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { act, render, screen } from '@testing-library/react';
import type { Liff } from '@line/liff';
import NotFoundScreen from '@/pages/404.page';
import userEvent from '@testing-library/user-event';
import { LiffContextMockProviders } from '@/test/mocks';

jest.mock('next/router', () => require('next-router-mock'));

const TestAuthErrorScreen = (): JSX.Element => {
  const liffContextValue = {
    liff: { openWindow: () => ({}) } as unknown as Liff,
  };

  return (
    <LiffContextMockProviders contextValue={liffContextValue}>
      <NotFoundScreen />
    </LiffContextMockProviders>
  );
};

describe('pages/404/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<TestAuthErrorScreen />);

      expect(screen.getByRole('img')).toBeInTheDocument();
    });
  });

  describe('User interactions', () => {
    it('should trigger `onClick` button', async () => {
      const user = userEvent.setup();

      render(<TestAuthErrorScreen />);
      const button = screen.getByRole('button');
      await act(() => user.click(button));

      expect(button).toHaveFocus();
    });
  });
});
