// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen, fireEvent } from '@testing-library/react';
import AccountSetting from '@/pages/mypage/account-settings/index.page';
import mockRouter from 'next-router-mock';

jest.mock('next/router', () => require('next-router-mock'));

describe('pages/mypage/account-settings/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('Should render in document', () => {
      render(<AccountSetting />);

      const listItem = screen.getAllByRole('listitem');
      expect(listItem.length).toEqual(3);
    });
  });

  describe('User interactions', () => {
    it('should trigger `onClick` button', () => {
      mockRouter.push('/example-path');

      render(<AccountSetting />);

      const redirectLink = screen.getByRole('link', {
        name: '＼マイページTOPに戻る／',
      });

      fireEvent.click(redirectLink);

      expect(mockRouter).toMatchObject({
        asPath: '/example-path',
        pathname: '/example-path',
      });
    });
  });
});
