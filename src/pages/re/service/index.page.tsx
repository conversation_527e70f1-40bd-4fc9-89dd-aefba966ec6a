// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import Loading from '@/components/Spinner/Loading';
import { routers } from '@/constants/routes';
import { useFetch } from '@/hooks/use-fetch';
import peerConneServiceApi from '@/services/internal/modules/service-linkage';
import { IServiceLinkageResponse } from '@/types/linkage';
import { useRouter } from 'next/router';
import React from 'react';

const RedirectExternalServiceScreen = () => {
  const router = useRouter();
  const { service_id, url } = router.query;

  useFetch({
    fetcher: () =>
      peerConneServiceApi.getPeerConneToken({
        service_id: service_id as string,
      }),
    handleResponse: (data: IServiceLinkageResponse) => {
      if (!service_id || !url) {
        router.push(routers.notFound);
        return;
      }

      const redirectUrl = `${url}?service_id=${service_id}&ott=${data.token}`;
      router.push(redirectUrl);
    },
    handleError(err) {
      console.log(err);
    },
    dependencies: [service_id, url],
  });

  return <Loading />;
};

export default RedirectExternalServiceScreen;
