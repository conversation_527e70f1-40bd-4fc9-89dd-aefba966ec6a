// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { twMerge } from 'tailwind-merge';
import React, { ComponentProps, FC } from 'react';
import { styles } from './GiftAction.styles';

export type GiftActionPosition = 'left' | 'center' | 'right' | 'between';

export interface GiftActionTheme {
  base: string;
  position: Record<GiftActionPosition, string>;
}

export interface GiftActionProps extends ComponentProps<'div'> {
  position?: GiftActionPosition;
}

export const GiftAction: FC<GiftActionProps> = ({
  position = 'between',
  className,
  children,
  ...props
}) => {
  return (
    <div
      className={twMerge(styles.base, styles.position[position], className)}
      {...props}
    >
      {children}
    </div>
  );
};
