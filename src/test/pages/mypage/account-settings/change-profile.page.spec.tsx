// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen } from '@testing-library/react';
import ChangeProfileInformationScreen from '@/pages/mypage/account-settings/change-profile.page';

describe('pages/mypage/account-settings/change-profile.page.spec.tsx', () => {
  describe('A11y', () => {
    it('Should render in document', async () => {
      render(<ChangeProfileInformationScreen />);

      const button = screen.getByRole('button', {
        name: '変更する',
      });

      expect(button).toBeInTheDocument();
    });
  });
});
