// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { render, screen } from '@testing-library/react';
import type { Liff } from '@line/liff';
import { useRedirect } from '@/hooks/use-redirect';
import { LiffContextMockProviders } from '@/test/mocks';
import redirectApi from '@/services/internal/modules/redirect';

jest.mock('next/router', () => require('next-router-mock'));
// eslint-disable-next-line @typescript-eslint/no-var-requires
const useRouter = jest.spyOn(require('next/router'), 'useRouter');

const useMockRouter = (query: Record<string, string | string[]> = {}) => {
  return useRouter.mockReturnValue({
    query,
    push: jest.fn(),
    replace: jest.fn(),
  });
};

interface TestUseRedirectComponentProps {
  isLoggedIn?: boolean;
  type?: string;
  withExternalBrowser?: boolean;
}

interface TestUseRedirectProps {
  withExternalBrowser?: boolean;
}

const TargetComponent: FC<TestUseRedirectProps> = ({ withExternalBrowser }) => {
  useRedirect(async ({ query: { lotteryId }, redirect, sendMessage }) => {
    const { redirect: redirectUrl } = await redirectApi.verifyRedirectGifteeUrl(
      {
        lotteryId,
      }
    );

    sendMessage();

    redirect(redirectUrl, { withExternalBrowser });
  });

  return <div data-testid="test-use-fetch"></div>;
};

const TestComponent: FC<TestUseRedirectComponentProps> = ({
  isLoggedIn = true,
  type = 'utou',
  withExternalBrowser,
}) => {
  const liffContextValue = {
    isLoggedIn,
    liff: {
      closeWindow: () => ({}),
      sendMessages: () => ({}),
      getContext: () => ({
        type,
      }),
      openWindow: () => ({}),
    } as unknown as Liff,
  };
  return (
    <LiffContextMockProviders contextValue={liffContextValue}>
      <TargetComponent withExternalBrowser={withExternalBrowser} />
    </LiffContextMockProviders>
  );
};

describe('hooks/use-redirect/use-redirect.spec.tsx', () => {
  describe('A11y', () => {
    it('should call handle when isLoggedIn=false', async () => {
      render(<TestComponent isLoggedIn={false} />);

      expect(screen.getByTestId('test-use-fetch')).toBeInTheDocument();
    });
  });

  describe('Fetching', () => {
    describe('Fetching success', () => {
      beforeEach(() => {
        jest.spyOn(redirectApi, 'verifyRedirectGifteeUrl').mockResolvedValue({
          isWinner: true,
          redirect: 'https://example.com',
        });
      });

      it('should redirectToLineChatRoomOAWithCheckUtou when isActive=true', async () => {
        useMockRouter({ is_active: 'true' });
        render(<TestComponent />);

        expect(screen.getByTestId('test-use-fetch')).toBeInTheDocument();
      });

      it('should handle before redirect when isActive=fasle', async () => {
        useMockRouter();
        render(<TestComponent />);

        expect(screen.getByTestId('test-use-fetch')).toBeInTheDocument();
      });

      it('should handle when lineContext.type !== utou', async () => {
        useMockRouter();
        render(<TestComponent type="external" />);

        expect(screen.getByTestId('test-use-fetch')).toBeInTheDocument();
      });

      it('should handle when withExternalBrowser=true', async () => {
        useMockRouter();
        render(<TestComponent withExternalBrowser={true} />);

        expect(screen.getByTestId('test-use-fetch')).toBeInTheDocument();
      });
    });

    describe('Fetching failure', () => {
      beforeEach(() => {
        jest.spyOn(redirectApi, 'verifyRedirectGifteeUrl').mockRejectedValue({
          statusCode: 500,
        });
      });

      it('should replace to 404 page when call api failure', async () => {
        useMockRouter();
        render(<TestComponent />);

        expect(screen.getByTestId('test-use-fetch')).toBeInTheDocument();
      });
    });
  });
});
