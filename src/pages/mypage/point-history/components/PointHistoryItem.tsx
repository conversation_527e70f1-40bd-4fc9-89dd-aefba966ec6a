// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { MinusPointType } from '@/constants/enum';
import { convertToSlashSeparatedFormat } from '@/utils/time';
import classNames from 'classnames';
import Image from 'next/image';
import { FC } from 'react';

export interface IPointHistoryItemProps {
  companyName: string;
  companyLogo: string;
  reason: string;
  gotAt: string;
  pointAmount: number;
  event_type: string;
}

const PointHistoryItem: FC<IPointHistoryItemProps> = ({
  companyName,
  companyLogo,
  reason,
  gotAt,
  pointAmount,
  event_type,
}) => {
  const isMinusPoint = () =>
    Object.values(MinusPointType).includes(event_type as MinusPointType);

  return (
    <>
      <li className="rounded border border-gray-10 py-[7px] mb-2">
        <div className="flex justify-between items-center px-3">
          <div className="partner-company__logo mr-3">
            <Image src={companyLogo} alt={companyName} width={40} height={40} />
          </div>
          <div className="flex-grow">
            <div className="partner-company flex justify-between">
              <div className="font-bold">{companyName}</div>
              <div className="text-gray-30 text-xs">
                {convertToSlashSeparatedFormat(gotAt)}
              </div>
            </div>
            <div className="point flex justify-between items-center">
              <div className="point__reason text-[11px]">{reason}</div>
              <div
                className={classNames(
                  'text-theme-secondary text-sm font-bold leading-4',
                  {
                    'text-theme-error-primary': isMinusPoint(),
                  }
                )}
              >
                {isMinusPoint() && '-'}
                {pointAmount}pt
              </div>
            </div>
          </div>
        </div>
      </li>
    </>
  );
};

export default PointHistoryItem;
