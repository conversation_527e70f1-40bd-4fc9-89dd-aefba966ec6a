// GKC: 01JPKNE8A7Z8AFWC717C0GBAA5
import { BaseComponent } from '../../../base';
import { MbscCalendarEvent, MbscCalendarEventData, MbscResource, MbscSlot } from '../../../shared/calendar-view/calendar-view.types';
import { MbscEventClickEvent } from '../eventcalendar.types';
import { ICalendarEventDragArgs, IDailyData, IDayData, IEventPosData, ISTOptions, ISTState } from './schedule-timeline-base.types';
/** @hidden */
export declare class STBase<PropType extends ISTOptions = ISTOptions, StateType extends ISTState = ISTState> extends BaseComponent<PropType, StateType> {
    _calcRowHeights?: boolean;
    _colIndexMap: {
        [key: string]: number;
    };
    _colors: {
        [key: string]: {
            [key: string]: {
                [key: string]: IDailyData;
            };
        };
    };
    /** Array containing the columns to display */
    _cols: IDayData[];
    _colsNr: number;
    /** Map containing the day index for each day; timestamp ->  dayIndex */
    _dayIndexMap: {
        [key: string]: number;
    };
    _dayNames: string[];
    /** Array containing the days to display */
    _days: IDayData[];
    /** Number of displayed days */
    _daysNr: number;
    _displayTime?: boolean;
    _dragTime?: number;
    _endCellStyle?: {
        height?: string;
        width?: string;
    };
    /** Displayed end time as milliseconds since midnight */
    _endTime: number;
    _eventDropped: boolean;
    _eventHeight: number;
    _eventHeights: {
        [key: string]: number;
    };
    _eventMap: {
        [key: string]: MbscCalendarEventData;
    };
    _events: {
        [key: string]: {
            [key: string]: {
                [key: string]: IDailyData;
            };
        };
    };
    _firstDay: Date;
    _firstDayTz: Date;
    _fixedResourceTops: {
        [key: string]: number;
    };
    _gridWidth: number;
    _groupByResource?: boolean;
    _gridHeight: number;
    _hasHierarchy?: boolean;
    _hasResources?: boolean;
    _hasResY?: boolean;
    _hasSlots?: boolean;
    _hasSideSticky?: boolean;
    _hasSticky?: boolean;
    _headerDays: IDayData[];
    _invalids: {
        [key: string]: {
            [key: string]: {
                [key: string]: IDailyData;
            };
        };
    };
    _isDailyResolution?: boolean;
    _isMulti?: boolean;
    _isSingleResource: boolean;
    _isTimeline?: boolean;
    _lastDay: Date;
    _lastDayTz: Date;
    /** Contains the resources flatten out into one level */
    _resources: MbscResource[];
    /** Contains the map of resources flatten out into one level */
    _resourcesMap: {
        [key: string]: MbscResource;
    };
    _rowHeights: {
        [key: string]: string | undefined;
    };
    _rowHeightsReal: {
        [key: string]: number;
    };
    _selectedDay?: number;
    _setRowHeight?: boolean;
    _showTimeIndicator?: boolean;
    _showCursorTime?: boolean;
    _slots: MbscSlot[];
    _startCellStyle?: {
        height?: string;
        width?: string;
    };
    /** Displayed start time as milliseconds since midnight */
    _startTime: number;
    _stepCell: number;
    _stepLabel: number;
    /** Displayed time as milliseconds */
    _time: number;
    /** Array containing the hours to display */
    _timeLabels: {
        [key: number]: string;
    };
    _times: number[];
    _timesBetween: number[];
    _variableEventHeight: boolean;
    _virtualRows: Array<{
        day?: IDayData;
        hidden?: boolean;
        rows: MbscResource[];
    }>;
    protected _calcConnections?: boolean;
    protected _createEventMaps?: boolean;
    protected _cursorTimeCont?: HTMLElement | null;
    /** Number of event rows for a resource, used for row height calculation */
    protected _eventRows: {
        [key: string]: number;
    };
    protected _fixedHeight: number;
    protected _fixedResources: Array<{
        index: number;
        height: number;
        key: string;
        resource: MbscResource;
    }>;
    protected _gridCont?: HTMLElement | null;
    protected _footerCont?: HTMLElement | null;
    protected _headerCont?: HTMLElement | null;
    protected _isParentClick?: boolean;
    protected _isScrolling?: (newX?: number, newY?: number) => void;
    protected _resCont?: HTMLElement | null;
    protected _resourcesCopy: MbscResource[];
    protected _resourceTops: {
        [key: string]: number;
    };
    protected _rows: Array<{
        dayIndex: number;
        key: string;
        resource: MbscResource;
        top: number;
    }>;
    protected _scrollCont?: HTMLElement | null;
    protected _shouldAnimateScroll?: boolean;
    protected _shouldCheckSize?: boolean;
    protected _shouldScroll?: boolean;
    protected _sidebarCont?: HTMLElement | null;
    protected _stickyFooter?: HTMLElement | null;
    protected _viewChanged?: boolean;
    protected _visibleResources: MbscResource[];
    private _allDayTop;
    private _body;
    private _clone;
    private _cursorX?;
    private _cursorY?;
    private _colHeight;
    private _colWidth;
    private _dragDayDelta;
    private _dragDelta?;
    private _gridContBottom;
    private _gridContLeft;
    private _gridContRight;
    private _gridContTop;
    private _gridLeft;
    private _gridRight;
    private _gridTop;
    private _isCursorTimeVisible?;
    private _isTouch?;
    private _maxEventStack?;
    private _onCalendar?;
    private _reloadEvents?;
    private _scrollAfterResize?;
    private _scrollTimer;
    private _scrollX;
    private _scrollY;
    private _startRow?;
    private _startSlotIndex?;
    private _tempAllDay?;
    private _tempEnd?;
    private _tempEvent?;
    private _tempResource?;
    private _tempSlot?;
    private _tempStart?;
    private _touchTimer;
    private _unlisten?;
    private _unsubscribe?;
    _isToday(d: number): boolean;
    _formatTime(v: number, timezone?: string): string;
    _onScroll: () => void;
    _onCellClick: (type: string, timestamp: number, v: number, domEvent: any, resource: string | number, slot?: string | number) => void;
    _onMouseLeave: (ev?: any, force?: boolean) => void;
    _onMouseMove: (ev?: any) => void;
    _onEventClick: (args: MbscEventClickEvent) => void;
    _onEventDragModeOn: (args: ICalendarEventDragArgs) => void;
    _onEventDragModeOff: (args: ICalendarEventDragArgs) => void;
    _onEventDragStart: (args: ICalendarEventDragArgs) => boolean;
    _onEventDragMove: (args: ICalendarEventDragArgs) => boolean;
    _onEventDragEnd: (args: ICalendarEventDragArgs) => void;
    _onExternalDrag: (args: ICalendarEventDragArgs) => void;
    protected _getEventPos(event: MbscCalendarEventData, day: Date, dateKey: string, displayedMap: Map<MbscCalendarEvent, boolean>): IEventPosData | undefined;
    protected _getEventData(event: MbscCalendarEvent, d: Date, resource?: MbscResource, slot?: MbscSlot, skipLabels?: boolean): MbscCalendarEventData;
    protected _getEvents(eventMap: {
        [key: string]: MbscCalendarEvent[];
    }): {
        [key: string]: {
            [key: string]: {
                [key: string]: IDailyData;
            };
        };
    };
    protected _getInvalids(invalidMap: {
        [key: string]: MbscCalendarEvent[];
    }): {
        [key: string]: {
            [key: string]: {
                [key: string]: IDailyData;
            };
        };
    };
    protected _getColors(colorMap: {
        [key: string]: MbscCalendarEvent[];
    }): {
        [key: string]: {
            [key: string]: {
                [key: string]: IDailyData;
            };
        };
    };
    protected _flattenResources(resources: MbscResource[] | null | undefined, flat: MbscResource[], depth: number, copy?: MbscResource[], fixed?: boolean): MbscResource[];
    protected _render(s: ISTOptions, state: ISTState): void;
    protected _mounted(): void;
    protected _updated(): void;
    protected _destroy(): void;
    private _calcGridSizes;
    private _getDragDates;
    /**
     * Returns a date with the time based on the coordinates on the grid.
     * @param day We're on this day.
     * @param posX X coord - for timeline.
     * @param posY Y coord - for schedule.
     * @param dayIndex Index of the day on the timeline.
     * @param timeStep Time step in minutes.
     */
    private _getGridTime;
    private _scrollToTime;
}
