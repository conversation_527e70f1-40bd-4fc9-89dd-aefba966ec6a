// GKC: 01JPKNE8A7Z8AFWC717C0GBAA5
import { IBaseProps } from '../../base';
export interface MbscCheckboxOptions extends IBaseProps {
    /**
     * Specifies the predefined color of the checkbox.
     * @defaultValue undefined
     */
    color?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'dark' | 'light';
    /**
     * Specifies the description text of the checkbox.
     * @defaultValue undefined
     */
    description?: string;
    /**
     * Specifies the disabled state of the checkbox.
     * @defaultValue false
     */
    disabled?: boolean;
    /**
     * Specifies the label of the checkbox.
     * @defaultValue undefined
     */
    label?: string;
    /**
     * Sets the position of the checkbox depending on the [rtl](#localization-rtl) option.
     * When in left-to-right mode, `'start'` will render the checkbox to the left, and `'end'` will render it to the right.
     * In right-to-left mode, `'start'` will render the checkbox to the right, and `'end'` will render it to the left.
     * @defaultValue 'end'
     */
    position?: 'start' | 'end';
    /** @hidden */
    checked?: boolean;
    /** @hidden */
    defaultChecked?: boolean;
    /** @hidden */
    inputStyle?: 'underline' | 'box' | 'outline';
    /** @hidden */
    modelValue?: boolean;
    /** @hidden */
    responsive?: any;
    /** @hidden */
    onChange?: any;
}
