// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { UNKNOWN } from '@/constants/define';
import {
  FILE_TYPE_PDF,
  IMAGE_TYPE_BMP,
  IMAGE_TYPE_GIF,
  IMAGE_TYPE_HEIC,
  IMAGE_TYPE_JPEG,
  IMAGE_TYPE_PNG,
  MIME_HEADER_FILE_PDF,
  MIME_HEADER_IMAGE_BMP,
  MIME_HEADER_IMAGE_GIF,
  MIME_HEADER_IMAGE_HEIC,
  MIME_HEADER_IMAGE_JPEG,
  MIME_HEADER_IMAGE_PNG,
} from '@/constants/file';
import imageCompression from 'browser-image-compression';

export const compressImage = async (file: File) => {
  const options = {
    maxSizeMB: 5,
    alwaysKeepResolution: true,
    maxWidthOrHeight: 1920,
  };

  try {
    const newFile = await imageCompression(file, options);
    return newFile;
  } catch {
    return file;
  }
};

export const mimeType = (header: string) => {
  let retv = '';
  switch (true) {
    case header.startsWith(MIME_HEADER_IMAGE_PNG):
      retv = IMAGE_TYPE_PNG;
      break;
    case header.startsWith(MIME_HEADER_IMAGE_BMP):
      retv = IMAGE_TYPE_BMP;
      break;
    case header.startsWith(MIME_HEADER_IMAGE_GIF):
      retv = IMAGE_TYPE_GIF;
      break;
    case header.startsWith(MIME_HEADER_IMAGE_JPEG):
      retv = IMAGE_TYPE_JPEG;
      break;
    case header.startsWith(MIME_HEADER_IMAGE_HEIC):
      retv = IMAGE_TYPE_HEIC;
      break;
    case header.startsWith(MIME_HEADER_FILE_PDF):
      retv = FILE_TYPE_PDF;
      break;
    default:
      retv = UNKNOWN;
      break;
  }
  return retv;
};

export const getMimeType = (file: File): Promise<string> => {
  let originalType = '';

  return new Promise((resolve, reject) => {
    const fileReader = new FileReader();
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    fileReader.onload = function (e: any) {
      const arr = new Uint8Array(e.target.result).subarray(0, 150);

      let header = '';
      for (let i = 0; i < arr.length; i++) {
        header += arr[i].toString(16);
      }

      originalType = mimeType(header);

      resolve(originalType);
    };
    fileReader.onerror = reject;
    fileReader.readAsArrayBuffer(file);
  });
};
