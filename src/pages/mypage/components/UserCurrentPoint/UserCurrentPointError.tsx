// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React, { FC } from 'react';
import { MessageBox } from '@/components/MessageBox';

interface UserCurrentPointErrorProps {
  retry?: () => void;
}

export const UserCurrentPointError: FC<UserCurrentPointErrorProps> = ({
  retry,
}) => {
  return (
    <div className="w-full flex justify-center bg-theme-primary/10 rounded p-4">
      <MessageBox className="gap-5">
        <MessageBox.Body>
          <MessageBox.Icon name="AccessDenied" />
          <MessageBox.Content className="text-sm">
            現在のポイントの呼び出しが失敗しました。
          </MessageBox.Content>
        </MessageBox.Body>
        <MessageBox.Action size="xs" onClick={retry}>
          リロード
        </MessageBox.Action>
      </MessageBox>
    </div>
  );
};
