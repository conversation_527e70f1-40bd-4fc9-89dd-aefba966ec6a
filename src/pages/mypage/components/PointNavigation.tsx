// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { Button } from '@/components/Button';
import { TAB_EXCHANGED_GIFT } from '@/constants/mypage';
import { routers } from '@/constants/routes';
import { FC } from 'react';
import { SvgIcon } from '@/components/SvgIcon';
import Link from 'next/link';

const PointNavigation: FC = () => {
  return (
    <div className="flex flex-col items-center gap-3">
      <Link href={routers.exchangePoint}>
        <Button
          color="primary"
          className="w-[267px] min-h-[48px]"
          customSpan="text-[15px] px-5"
          align="left"
        >
          <SvgIcon name={'Gift'} className="mr-4" />
          ギフトに交換する
        </Button>
      </Link>

      <Link
        href={{
          pathname: routers.pointHistory,
          query: {
            tab: TAB_EXCHANGED_GIFT,
          },
        }}
      >
        <Button
          color="primary"
          className="w-[267px] min-h-[48px]"
          customSpan="text-[15px] px-5"
          align="left"
        >
          <SvgIcon name={'ExchangeGift'} className="mr-4" />
          交換したギフトをみる
        </Button>
      </Link>
    </div>
  );
};

export default PointNavigation;
