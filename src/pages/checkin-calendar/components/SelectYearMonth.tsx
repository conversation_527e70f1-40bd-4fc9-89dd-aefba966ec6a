// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import classNames from 'classnames';
import { useClickOutside } from '@/hooks/use-click-outside';
import { SvgIcon } from '@/components/SvgIcon';
import {
  Datepicker,
  localeJa,
} from '@/lib/mobiscroll/react/esm5/mobiscroll.react.min';
import '@/lib/mobiscroll/react/css/mobiscroll.scss';
import { DatepickerBase } from '@/lib/mobiscroll/react/src/core/components/datepicker/datepicker';
import dayjs from 'dayjs';
import {
  TypeSwipeDirections,
  TypeSwipeDirectionsWithoutNull,
} from '@/types/checkin-calendar';

interface DropdownProps {
  id: string;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  style?: string;
  registeredDate: string;
  onSelectPicker?: (year: number, month: number) => void;
  swipeDirection?: TypeSwipeDirections;
  onClickSubmit: (year: number, month: number) => void;
}

const SelectYearMonth = ({
  id,
  position = 'bottom-left',
  style,
  registeredDate,
  onSelectPicker,
  swipeDirection,
  onClickSubmit,
}: DropdownProps) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [dateTimeValue, setDateTimeValue] = useState(new Date());
  const [originDateTimeValue, setOriginDateTimeValue] = useState(new Date());
  const minSelectDate = useMemo(
    () => dayjs(registeredDate).format('YYYY-MM-DD'),
    [registeredDate]
  );
  const maxSelectDate = useMemo(
    () => dayjs().add(1, 'year').format('YYYY-MM-DD'),
    []
  );

  const handleChangeDateTime = useCallback((e: DatepickerBase) => {
    setDateTimeValue(new Date(e.value));
  }, []);

  const handleSubmitPicker = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    onClickSubmit(year, month);
    setOriginDateTimeValue(date);
    setIsOpen(false);
  };

  const handleCancelPicker = () => {
    setDateTimeValue(originDateTimeValue);
    setIsOpen(false);
  };

  const dropdownRef = useClickOutside(() => {
    handleCancelPicker();
  });

  const dropdownClass = classNames(
    'absolute bg-white w-full overflow-y-hidden rounded-md shadow-md z-10',
    {
      'top-full right-0 mt-1': position === 'bottom-right',
      'top-full left-0 mt-1': position === 'bottom-left',
      'bottom-full right-0 mb-1': position === 'top-right',
      'bottom-full left-0 mb-1': position === 'top-left',
    }
  );

  const adjustMonth = useCallback(
    (direction: TypeSwipeDirectionsWithoutNull) => {
      const increment = direction === 'left' ? 1 : -1;
      const newMonth = originDateTimeValue.getMonth() + increment;
      const newDate = new Date(originDateTimeValue.getFullYear(), newMonth, 1);
      setDateTimeValue(newDate);

      const year = newDate.getFullYear();
      const month = newDate.getMonth() + 1;
      onSelectPicker && onSelectPicker(year, month);
      setOriginDateTimeValue(newDate);
    },
    [originDateTimeValue]
  );

  useEffect(() => {
    if (swipeDirection) adjustMonth(swipeDirection);
  }, [swipeDirection, adjustMonth]);

  return (
    <div ref={dropdownRef} className="relative w-[160px] mx-auto">
      <button
        id={id}
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className={classNames(
          'flex justify-center items-center rounded py-2 px-4 bg-calendar-orange text-white w-full',
          style
        )}
      >
        <span className="text-white text-center font-bold leading-5 basis-[117px]">
          {`${originDateTimeValue.getFullYear()}年${(
            originDateTimeValue.getMonth() + 1
          ).toString()}月`}
        </span>
        <SvgIcon name={'BoldDownArrow'} />
      </button>

      {isOpen && (
        <div
          className={classNames(
            'flex items-center justify-center flex-col [box-shadow:0px_4px_25px_-4px_rgba(0,0,0,1)]',
            { hidden: !isOpen },
            dropdownClass
          )}
        >
          <Datepicker
            cssClass="custom-checkin-calendar-picker"
            controls={['date']}
            dateFormat="YYYY-MM"
            locale={localeJa}
            display="inline"
            themeVariant="light"
            min={minSelectDate}
            max={maxSelectDate}
            onChange={handleChangeDateTime}
            value={dateTimeValue}
            touchUi={false}
            theme="ios"
            itemHeight={42}
            rows={5}
          />
          <div className="mt-2 flex w-full p-[6px] gap-[6px]">
            <button
              className="w-full text-xs border-[#515357] border border-solid text-[#515357] font-medium rounded-[4px] leading-[32px] hover:bg-[#5D5D5D] hover:text-white focus:bg-[#5D5D5D] focus:text-white"
              onClick={handleCancelPicker}
            >
              キャンセル
            </button>
            <button
              className="w-full text-xs border-[#515357] border border-solid text-[#515357] font-medium rounded-[4px] leading-[32px] hover:bg-calendar-orange hover:text-white focus:bg-calendar-orange focus:text-white"
              onClick={() => handleSubmitPicker(dateTimeValue)}
            >
              OK
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SelectYearMonth;
