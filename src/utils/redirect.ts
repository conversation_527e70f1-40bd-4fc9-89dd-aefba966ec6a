// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import Router from 'next/router';
import type { Liff } from '@line/liff';
import { NextPageContext } from 'next';
import { IConfigLoginLine } from '@/types/app';
import { windowExists, parseQueryString } from '.';

export const redirectUser = (ctx: NextPageContext, location: string) => {
  if (ctx.req && ctx.res) {
    typeof ctx.res.writeHead === 'function' &&
      ctx.res.writeHead(302, { Location: location });
    typeof ctx.res.end === 'function' && ctx.res.end();
  } else {
    Router.push(location);
  }
};

export const redirectToLineLogin = (config?: IConfigLoginLine) => {
  const configLineLoginDefaul: IConfigLoginLine = {
    response_type: 'code',
    client_id: `${process.env.LIFF_ID?.split('-')[0]}`,
    redirect_uri: `${process.env.CLIENT_BASE_URL}`,
    state: Math.random().toString(36).slice(2),
    scope: 'profile openid email chat_message.write',
    prompt: 'consent',
    bot_prompt: 'aggressive',
  };
  const configLineLogin = {
    ...configLineLoginDefaul,
    ...config,
  };
  const queryString = new URLSearchParams(configLineLogin).toString();

  window.location.href = `${process.env.LINE_LOGIN_URL}?${queryString}`;
};

export const redirectToLineLoginWithoutCompany = (query?: {
  [key: string]: string;
}) => {
  const queryString = new URLSearchParams({
    ...query,
    is_without_companyId: 'true',
  }).toString();
  const redirectUri = windowExists()
    ? `${window.location.origin}?${queryString}`
    : `${process.env.CLIENT_BASE_URL}?${queryString}`;
  redirectToLineLogin({ redirect_uri: redirectUri });
};

export const redirectToLineChatRoomOA = (liff: Liff) => {
  liff.openWindow({
    url: `${process.env.LINE_OA_URL}`,
    external: false,
  });
};

export const redirectToLineChatRoomOAWithCheckUtou = (liff: Liff) => {
  const lineContext = liff.getContext();
  const isUtou = lineContext?.type === 'utou';

  return isUtou ? liff.closeWindow() : redirectToLineChatRoomOA(liff);
};

export const redirectWithQueryActiveHistory = async (
  redirect: string,
  queryParam: {
    [key: string]: string | boolean;
  } = {}
) => {
  // redirect url format: https://liff.line.me/LIFF_ID/checkin?company_id=ABC&agency_token=ABC&agency_id=ABC or https://liff.line.me/LIFF_ID/mypage/top
  const isRedirectContainQuery = redirect.includes('?');
  const splitRedirect = redirect.split('?');
  const redirectPathname = isRedirectContainQuery ? splitRedirect[0] : redirect;
  const redirectQuery = isRedirectContainQuery
    ? parseQueryString(splitRedirect[1])
    : {};

  const splitCurrentPathName = Router.asPath.split('?');

  await Router.replace({
    pathname: splitCurrentPathName[0],
    query: { ...Router.query, is_active: true },
  });

  await Router.push({
    pathname: redirectPathname,
    query: { ...redirectQuery, ...queryParam },
  });
};

export const redirectWithoutEmptyQuery = async (
  params: Record<string, string | string[]>
) => {
  /** Clone deep current router and params additional */
  const updateQuery = JSON.parse(
    JSON.stringify({ ...Router.query, ...params })
  );
  /** Remove key in object when not have value
   * @example updateQuery = { company_id: '123', show: '' } convert to updateQuery = { company_id: '123' }
   */
  Object.keys(updateQuery).forEach((key: string) => {
    if (!updateQuery[key]) {
      delete updateQuery[key];
    }
  });
  /** Await router redirect */
  await Router.replace({
    pathname: Router.pathname,
    query: updateQuery,
  });
};
