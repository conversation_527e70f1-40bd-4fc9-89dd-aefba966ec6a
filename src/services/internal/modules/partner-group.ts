// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import {
  IRegisteredPartnerGroupResponse,
  IRegisteredPartnerGroupPayload,
  IJoinPartnerGroupResponse,
} from '@/types/partner-group';
import service from '..';

const basePath = '/group';

const partnerGroupApi = {
  getRegiserableGroup: (payload: IRegisteredPartnerGroupPayload) =>
    service.get<IRegisteredPartnerGroupResponse>(
      `${basePath}/registrable/${payload.company_id}`
    ),
  joinPartnerGroup: (payload: IRegisteredPartnerGroupPayload) =>
    service.post<IJoinPartnerGroupResponse>(`${basePath}/join`, payload),
};

export default partnerGroupApi;
