// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { Button } from '@/components/Button';
import { ScrollLink } from '@/components/ScrollLink';
import { SvgIcon } from '@/components/SvgIcon';
import { IExchangeableGiftDetailResponse } from '@/types/exchange-points';
import { transformText } from '@/utils/text';
import Image from 'next/image';
import { FC } from 'react';
import { styles } from './styles';
interface ExchangeableGiftDetailProps {
  gift: IExchangeableGiftDetailResponse;
  submit: () => void;
  isEnoughPointExchange: boolean;
}

export const ExchangeableGiftDetail: FC<ExchangeableGiftDetailProps> = ({
  gift,
  submit,
  isEnoughPointExchange,
}) => {
  return (
    <>
      <div className="flex flex-grow flex-col justify-between h-full">
        <div className="main">
          {gift.gift_image ? (
            <figure className="relative pt-[100%]">
              <Image
                fill
                quality={100}
                src={gift.gift_image}
                alt={gift.gift_name}
                className="w-full object-cover rounded"
              />
            </figure>
          ) : (
            <div className="relative w-full pt-[100%] bg-[#E2E2E2] rounded">
              <div className="flex flex-col justify-center items-center gap-3 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                <SvgIcon name="ImageXLarge" />
                <p className="text-sm text-gray-3 leading-[22px] font-bold min-w-[200px] text-center">
                  画像が表示できませんでした。
                </p>
              </div>
            </div>
          )}

          <div className="space-y-4 mt-6">
            <div className="space-y-1.5">
              <h5
                title={gift.gift_company_name}
                className="text-sm text-theme-main mb-1.5 line-clamp-2"
              >
                {gift.gift_company_name}
              </h5>
              <h3
                title={gift.gift_name}
                className="text-base text-theme-main font-bold line-clamp-3"
              >
                {gift.gift_name}
              </h3>
            </div>
            <div className="inline-block relative">
              <span className="text-[13px] text-theme-main leading-6">
                必要ポイント数
              </span>
              <span className="text-lg text-theme-secondary font-semibold font-hind leading-6 absolute bottom-[-2px] left-[calc(100%+16px)]">
                {`${gift.point_amount}pt`}
              </span>
            </div>

            <div>
              <ul className="inline-flex flex-col">
                <li>
                  <ScrollLink
                    href="#about"
                    className="text-sm text-theme-info-primary underline"
                  >
                    ギフトについて
                  </ScrollLink>
                </li>
                <li>
                  <ScrollLink
                    href="#how-to-use"
                    className="text-sm text-theme-info-primary underline"
                  >
                    ギフトの使い方
                  </ScrollLink>
                </li>
                <li>
                  <ScrollLink
                    href="#note"
                    className="text-sm text-theme-info-primary underline"
                  >
                    注意事項
                  </ScrollLink>
                </li>
              </ul>
            </div>

            <div className="space-y-2.5 !my-8">
              <Button
                fullSized
                size="xl"
                type="submit"
                disabled={!isEnoughPointExchange}
                color={!isEnoughPointExchange ? 'gray-10' : 'primary'}
                onClick={submit}
              >
                このギフトとポイントを交換する
              </Button>
              {!isEnoughPointExchange && (
                <p className="text-xs text-theme-error-third leading-[15px]">
                  ポイントが不足しています。
                </p>
              )}
            </div>

            <div className="space-y-1.5">
              <h3 id="about" className="text-sm text-theme-sub">
                ギフトについて
              </h3>

              <pre
                dangerouslySetInnerHTML={{
                  __html: transformText(gift.gift_about, styles),
                }}
                className={styles.base}
              />
            </div>

            <div className="space-y-1.5 !mt-8">
              <h3 id="how-to-use" className="text-sm text-theme-sub">
                ギフトの使い方
              </h3>

              <pre
                dangerouslySetInnerHTML={{
                  __html: transformText(gift.gift_how_to_use, styles),
                }}
                className={styles.base}
              />
            </div>

            <div className="space-y-1.5 !mt-8">
              <h3 id="note" className="text-sm text-theme-sub">
                注意事項
              </h3>

              <pre
                dangerouslySetInnerHTML={{
                  __html: transformText(gift.gift_notes, styles),
                }}
                className={styles.base}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
