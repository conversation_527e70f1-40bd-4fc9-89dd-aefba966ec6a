// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { routers } from '@/constants/routes';
import { MessageBox } from '@/components/MessageBox';

export const PointHistoryListEmpty = () => {
  return (
    <MessageBox className="pt-[70px]">
      <MessageBox.Body className="max-w-[75vw]">
        <MessageBox.Icon name="File" />
        <MessageBox.Content>
          現在獲得したポイントがありません。
          <br />
          履歴反映に時間がかかっている場合があるので再度お試しください。
        </MessageBox.Content>
      </MessageBox.Body>
      <MessageBox.Action href={routers.howToGetPoints}>
        ポイント獲得一覧
      </MessageBox.Action>
    </MessageBox>
  );
};
