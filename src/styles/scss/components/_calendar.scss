.custom-stamp-number {
  background: white;
  position: relative;
  min-width: 88px;
  width: 88px;
  height: 88px;
  border-radius: 50%;
  border: 4px solid;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  font-weight: 700;
  margin-left: -14px;
  font-family: $font-family-serif;

  .text {
    width: 22px;
    height: auto;
    position: absolute;
    right: 0;
    bottom: 2px;
  }
}

.custom-checkin-calendar-picker {
  &.mbsc-datepicker-inline {
    border: none !important;
  }

  .mbsc-datepicker-tab-wrapper {
    .mbsc-scroller-wheel-wrapper {
      position: relative;
      z-index: 20;

      &.mbsc-scroller-wheel-wrapper-0 {
        width: 72px;
        max-width: 72px;

        &::before {
          z-index: 1;
          height: 34px;
          background-color: $calendar-main-orange;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 0;
          content: '';
          width: 100%;
        }
      }

      &.mbsc-scroller-wheel-wrapper-1 {
        max-width: 51px !important;
        min-width: 51px !important;
        width: 51px;

        &::before {
          z-index: 1;
          height: 34px;
          background-color: $calendar-main-orange;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 0;
          content: '';
          width: 100%;
        }
      }
    }

    .mbsc-scroller-pointer {
      .mbsc-scroller-wheel-group {
        z-index: 10;
        position: relative;
        gap: 14px;
      }

      .mbsc-scroller-wheel-line {
        margin: 0;
        z-index: 1;
        background: unset;
        border-radius: 0;
      }

      .mbsc-scroller-wheel-item {
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: $font-family-primary !important;
        font-size: 16px;
        font-weight: 500;
        line-height: 20px !important;
        letter-spacing: 0.01em;
        text-align: center;
        border-radius: 0;
        margin: 0;
        padding: 8px;
        color: #515357;

        &.mbsc-hover {
          background: unset;
        }

        &.mbsc-selected {
          text-align: center;
          color: white;
        }
      }
    }

    .mbsc-scroller-bar-cont {
      display: none;
    }
  }
}

.mobiscroll-datepicker.mbsc-datepicker {
  border: none !important;

  .mbsc-calendar-day-outer {
    pointer-events: none;

    .stamp-placeholder,
    .stamp-box {
      visibility: hidden !important;
    }

    .day-item {
      color: white !important;
      background-color: #e8e8e8 !important;
    }

    .has-memo {
      &::after {
        display: none !important;
      }
    }
  }

  .mbsc-calendar {
    padding-bottom: 0;

    .mbsc-ios.mbsc-calendar-wrapper {
      border: none;
    }
  }

  .mbsc-calendar .mbsc-calendar-header {
    display: none;
  }

  .mbsc-calendar-slide {
    padding: 0;
    border: 6px solid $calendar-main-yellow !important;
    overflow: hidden;
  }

  .mbsc-calendar-table {
    margin-left: -1px;
  }

  .mbsc-calendar-week-days {
    .mbsc-calendar-week-day {
      font-family: $font-family-primary;
      font-weight: 500;
      font-size: 13px;
      height: 30px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #515357;

      &:first-child {
        color: #ff3d32;
      }

      &:last-child {
        color: #128fd0;
      }
    }
  }

  .mbsc-calendar-cell {
    border: none;
  }

  .mbsc-calendar-cell-inner {
    border: none;

    .normal-day {
      display: flex;
      flex-direction: column;
      position: relative;
      border-left: 1px solid $bg-gray-10 !important;
      border-top: 1px solid $bg-gray-10 !important;
      border: none;
      padding: 0;
      align-items: flex-start;
      justify-content: space-between;

      &.has-memo {
        &::after {
          content: '';
          display: block;
          position: absolute;
          top: 0;
          right: 0;
          width: 0;
          height: 0;
          border-style: solid;
          border-width: 0 10px 10px 0;
          border-color: transparent $calendar-main-orange transparent
            transparent;
          transform: rotate(0deg);
        }
      }

      &::before {
        content: '';
        display: block;
        padding-top: 100%;
      }

      &.last-date-in-month {
        border-right: 1px solid $bg-gray-10 !important;
      }

      .day-item {
        position: absolute;
        top: 0;
        left: 0;
        width: 50%;
        height: 50%;
        font-size: 12px;
        font-family: $font-family-primary;
        background: #fffade;
        font-weight: 500;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      &.other-month {
        .day-item {
          &.sunday,
          &.holiday {
            color: #515357;
          }

          &.saturday {
            color: #515357;
          }
        }
      }

      &.today {
        background-color: #fff79a;
      }

      .stamp-box {
        position: absolute;
        left: 0;
        bottom: 0;
      }

      .stamp-placeholder {
        position: relative;
        font-family: $font-family-primary;

        &::before {
          content: '';
          display: block;
          border: 1px solid white;
          border-radius: 50%;
          width: calc(100% - 4px);
          height: calc(100% - 4px);
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }
      }

      .sunday,
      .holiday {
        color: #ff3d32;
        background: #fff3f2;
      }

      .saturday {
        color: #128fd0;
        background: #f1f6f8;
      }
    }
  }
}
