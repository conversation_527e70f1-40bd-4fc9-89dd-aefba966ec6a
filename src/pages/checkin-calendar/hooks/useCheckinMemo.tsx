// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { useState, useRef, Dispatch, SetStateAction } from 'react';
import dayjs from 'dayjs';
import SwiperCore from 'swiper';
import { ICalendarData, IDateMemo } from '@/types/checkin-calendar';
import { useFetch } from '@/hooks/use-fetch';
import checkinCalendarApi from '@/services/internal/modules/checkin-calendar';
import { MbscDatepickerCellClickEvent } from '@/lib/mobiscroll/react/esm5/mobiscroll.react.min';

interface UseCalendarDataParams {
  monthCall: string[];
  currentMonthData: ICalendarData;
  setMonthCall: Dispatch<SetStateAction<string[]>>;
  setRecall: Dispatch<SetStateAction<boolean | undefined>>;
  setSpecifyMonthReCall: Dispatch<SetStateAction<string>>;
}

const useCheckinMemo = ({
  currentMonthData,
  monthCall,
  setMonth<PERSON>all,
  setRecall,
  setSpecifyMonthReCall,
}: UseCalendarDataParams) => {
  const { fetch: editCheckinCalendarMemo } = useFetch({
    fetcher: checkinCalendarApi.editCheckinCalendarMemo,
    dependencies: false,
  });

  const [dates, setDates] = useState<IDateMemo[]>([]);
  const [isOpenModal, setIsOpenModal] = useState<boolean>(false);
  const [isOpenModalConfirm, setIsOpenModalConfirm] = useState<boolean>(false);
  const [activeDateMemo, setActiveDateMemo] = useState<string>('');
  const swiperRef = useRef<SwiperCore | null>(null);
  const [lockSwipe, setLockSwipe] = useState(false);
  const [isEditable, setIsEditable] = useState<boolean>(false);
  const [memoValue, setMemoValue] = useState('');

  const generateDatasForMonth = (date: string) => {
    const startOfMonth = dayjs(date).startOf('month');
    const endOfMonth = dayjs(date).endOf('month');
    const daysArray = [];

    for (let i = startOfMonth; i <= endOfMonth; i = i.add(1, 'day')) {
      const formattedDate = i.format('YYYY/MM/DD');

      // Tìm activity tương ứng với ngày hiện tại
      const activity = currentMonthData.activities.find(
        item => item.date === formattedDate
      );

      const dayInfo = {
        date: formattedDate,
        memo: activity?.memo || '',
        agencyName: activity?.agencyName || [],
      };
      daysArray.push(dayInfo);
    }

    return daysArray;
  };

  const updateDates = (newDates: IDateMemo[]) => {
    if (JSON.stringify(newDates) !== JSON.stringify(dates)) {
      setDates(newDates);
    }
  };

  const scrollToSlide = async (
    datesInMonth: IDateMemo[],
    selectedDate: string
  ) => {
    const selectedDateIndex = await datesInMonth.findIndex(date =>
      dayjs(date.date).isSame(selectedDate, 'day')
    );

    if (
      swiperRef.current &&
      selectedDateIndex &&
      swiperRef.current.activeIndex !== selectedDateIndex
    ) {
      swiperRef.current.slideTo(selectedDateIndex, 0, false);
    }
  };

  const handleGetCurrentMemoValue = (
    data: { date: string; memo: string; agencyName: [] }[],
    compareDate: string
  ) => {
    const selectedDateData = data.find(item => item.date === compareDate);
    setMemoValue(selectedDateData?.memo || '');
  };

  const handleSlideChange = (swiper: SwiperCore) => {
    const currentDate = dayjs(dates[swiper.activeIndex]?.date).format(
      'YYYY/MM/DD'
    );
    const nextDay = dayjs(currentDate).add(1, 'day');
    const previousDay = dayjs(currentDate).subtract(1, 'day');

    const startDate = dayjs(currentMonthData.registeredDate)
      .startOf('month')
      .format('YYYY/MM/DD');
    const endDate = dayjs().endOf('month').add(1, 'year').format('YYYY/MM/DD');
    setActiveDateMemo(dayjs(currentDate).format('YYYY/MM/DD'));

    // call next & previous month when near last in range month data
    if (
      dayjs(currentDate).format('YYYY/MM') <=
      dayjs(monthCall[1]).format('YYYY/MM')
    ) {
      setMonthCall([
        dayjs(monthCall[1] as string)
          .subtract(2, 'month')
          .format('YYYY/MM'),
        ...monthCall,
      ]);
    } else if (
      dayjs(currentDate).format('YYYY/MM') >=
      dayjs(monthCall[monthCall.length - 2]).format('YYYY/MM')
    ) {
      setMonthCall([
        ...monthCall,
        dayjs(monthCall[monthCall.length - 2] as string)
          .add(2, 'month')
          .format('YYYY/MM'),
      ]);
    }
    // end call next & previous month when near last in range month data

    if (
      dayjs(currentDate).isBefore(startDate) ||
      dayjs(currentDate).isAfter(endDate)
    ) {
      swiper.slideTo(swiper.previousIndex);
      return;
    }

    if (swiper.swipeDirection === 'prev') {
      if (
        (dayjs(currentDate).month() > previousDay.month() &&
          dayjs(dates[0]?.date).month() !== previousDay.month()) ||
        dayjs(currentDate).year() > previousDay.year()
      ) {
        const newDates = [
          ...generateDatasForMonth(previousDay.format('YYYY/MM/DD')),
          ...dates,
        ];
        if (!dayjs(newDates[0]?.date).isSame(dates[0]?.date, 'day')) {
          updateDates(newDates);
          scrollToSlide(newDates, currentDate);
        }
      }
    } else if (swiper.swipeDirection === 'next') {
      if (
        (dayjs(currentDate).month() < nextDay.month() &&
          dayjs(dates[dates.length - 1]?.date).month() !== nextDay.month()) ||
        dayjs(currentDate).year() < nextDay.year()
      ) {
        const newDates = [
          ...dates,
          ...generateDatasForMonth(nextDay.format('YYYY/MM/DD')),
        ];
        if (
          !dayjs(newDates[newDates.length - 1]?.date).isSame(
            dates[dates.length - 1]?.date,
            'day'
          )
        ) {
          updateDates(newDates);
          scrollToSlide(newDates, currentDate);
        }
      }
    }
  };

  const handleClickDate = (e: MbscDatepickerCellClickEvent) => {
    const selectedDate = dayjs(e.date).format('YYYY/MM/DD');
    const nextDay = dayjs(selectedDate).add(1, 'day');
    const previousDay = dayjs(selectedDate).subtract(1, 'day');
    const datasInMonth = generateDatasForMonth(selectedDate);

    // call next & previous month when near last in month range
    if (
      dayjs(selectedDate).format('YYYY/MM') <=
      dayjs(monthCall[1]).format('YYYY/MM')
    ) {
      setMonthCall([
        dayjs(monthCall[1] as string)
          .subtract(2, 'month')
          .format('YYYY/MM'),
        ...monthCall,
      ]);
    } else if (
      dayjs(selectedDate).format('YYYY/MM') >=
      dayjs(monthCall[monthCall.length - 2]).format('YYYY/MM')
    ) {
      setMonthCall([
        ...monthCall,
        dayjs(monthCall[monthCall.length - 2] as string)
          .add(2, 'month')
          .format('YYYY/MM'),
      ]);
    }
    // end call next & previous month when near last in month range

    if (
      dayjs(selectedDate).month() > previousDay.month() ||
      dayjs(selectedDate).year() > previousDay.year()
    ) {
      const newDates = [
        ...generateDatasForMonth(previousDay.format('YYYY/MM/DD')),
        ...generateDatasForMonth(selectedDate),
      ];
      updateDates(newDates);
      scrollToSlide(newDates, selectedDate);
    } else if (
      dayjs(selectedDate).month() < nextDay.month() ||
      dayjs(selectedDate).year() < nextDay.year()
    ) {
      const newDates = [
        ...generateDatasForMonth(selectedDate),
        ...generateDatasForMonth(nextDay.format('YYYY/MM/DD')),
      ];
      updateDates(newDates);
      scrollToSlide(newDates, selectedDate);
    } else {
      updateDates(datasInMonth);

      scrollToSlide(datasInMonth, selectedDate);
    }
  };

  const onCloseModal = () => {
    setIsEditable(false);
    setIsOpenModal(false);
    updateDates([]);
    setActiveDateMemo('');
    setLockSwipe(false);
    if (swiperRef.current) {
      swiperRef.current.allowTouchMove = true;
    }
  };

  const onSubmitMemo = async () => {
    const convertedDate = dayjs(activeDateMemo).format('YYYY-MM-DD');
    const data = {
      company_id: 'C0003',
      date: convertedDate,
      memo: memoValue,
    };

    try {
      await editCheckinCalendarMemo(data);

      const newData = dates.map(item => {
        if (item.date === activeDateMemo) {
          return {
            ...item,
            memo: memoValue,
          };
        }
        return item;
      });
      updateDates([...newData]);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setRecall(prev => !prev);
      setSpecifyMonthReCall(dayjs(activeDateMemo).format('YYYY/MM'));
    }
  };

  const onEditable = (allowEdit: boolean) => {
    setIsEditable(!isEditable);
    const datasInMonth = generateDatasForMonth(activeDateMemo);

    if (swiperRef.current) {
      swiperRef.current.allowTouchMove =
        swiperRef.current.allowTouchMove === true ? false : true;
      setLockSwipe(!lockSwipe);
    }

    if (allowEdit) onSubmitMemo();
    if (!allowEdit) handleGetCurrentMemoValue(datasInMonth, activeDateMemo);
  };

  const onChangeMemoValue = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (e.target.value.length <= 1000) {
      setMemoValue(e.target.value);
    }
  };

  const onCancelEdit = (allowEdit: boolean) => {
    setIsOpenModalConfirm(false);
    setIsEditable(false);

    if (swiperRef.current) {
      swiperRef.current.allowTouchMove =
        swiperRef.current.allowTouchMove === true ? false : true;
      setLockSwipe(!lockSwipe);
    }

    if (allowEdit) onSubmitMemo();
  };

  return {
    dates,
    activeDateMemo,
    isEditable,
    lockSwipe,
    swiperRef,
    memoValue,
    setMemoValue,
    handleClickDate,
    handleSlideChange,
    onCloseModal,
    onEditable,
    onCancelEdit,
    onChangeMemoValue,
    isOpenModal,
    isOpenModalConfirm,
    setIsOpenModal,
    setIsOpenModalConfirm,
    setActiveDateMemo,
  };
};

export default useCheckinMemo;
