// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import classNames from 'classnames';
import { FC, ReactNode } from 'react';
import Link, { LinkProps } from 'next/link';
import { styles } from './CurrentPoint.styles';

export interface CurrentPointLinkProps extends LinkProps {
  className?: string;
  children?: ReactNode;
}

export const CurrentPointLink: FC<CurrentPointLinkProps> = ({
  className,
  children,
  ...props
}) => {
  return (
    <Link className={classNames(styles.link.base, className)} {...props}>
      {children}
    </Link>
  );
};
