// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen } from '@testing-library/react';
import MyPageScreen from '@/pages/mypage/index.page';

jest.mock('next/router', () => require('next-router-mock'));

describe('pages/mypage/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<MyPageScreen />);
      const loading = screen.getByRole('status');

      expect(loading).toBeInTheDocument();
    });
  });
});
