// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen, waitFor } from '@testing-library/react';
import redirectApi from '@/services/internal/modules/redirect';
import RedirectGoogleFormScreen from '@/pages/re/q/[questionnaireId].page';
import { LiffContextMockProviders } from '@/test/mocks';

jest.mock('next/router', () => require('next-router-mock'));
jest.mock('axios', () => {
  return {
    create: jest.fn(() => ({
      get: jest.fn(),
      interceptors: {
        request: { use: jest.fn(), eject: jest.fn() },
        response: { use: jest.fn(), eject: jest.fn() },
      },
    })),
  };
});

const TestRedirectGoogleFormScreen = (): JSX.Element => {
  return (
    <LiffContextMockProviders>
      <RedirectGoogleFormScreen />
    </LiffContextMockProviders>
  );
};

describe('pages/re/q/[questionnaireId].page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<TestRedirectGoogleFormScreen />);

      await waitFor(() => {
        expect(screen.getByRole('status')).toBeInTheDocument();
      });
    });
  });

  describe('Fetching', () => {
    it('should call api return response with redirect_url', async () => {
      const spy = jest.spyOn(redirectApi, 'verifyRedirectGoogleFormUrl');
      spy.mockResolvedValue({
        redirect_url: 'https://example.com',
      });

      render(<TestRedirectGoogleFormScreen />);

      await waitFor(() => {
        expect(screen.getByRole('status')).toBeInTheDocument();
      });
    });

    it('should call api return response with not have redirect_url', async () => {
      const spy = jest.spyOn(redirectApi, 'verifyRedirectGoogleFormUrl');
      spy.mockResolvedValue({
        redirect_url: '',
      });

      render(<TestRedirectGoogleFormScreen />);

      await waitFor(() => {
        expect(screen.getByRole('status')).toBeInTheDocument();
      });
    });
  });
});
