// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import Link from 'next/link';
import { twMerge } from 'tailwind-merge';
import { isUndefined } from 'lodash';
import React, { ComponentProps, FC } from 'react';
import { styles } from './GiftBody.styles';
import { IThemeSizes } from '@/types/theme';
import { SvgIcon } from '@/components/SvgIcon';

export type GiftBodyType = 'normal' | 'medium' | 'complex';

export type GiftBodyPosition = 'top' | 'center' | 'end' | 'between';

export type GiftBodyGap = Pick<IThemeSizes, 'xs' | 'sm' | 'md' | 'lg'>;

export interface GiftBodyTheme {
  base: string;
  type: Record<GiftBodyType, string>;
  body: GiftBodyBodyTheme;
}

export interface GiftBodyBodyTheme {
  base: string;
  position: Record<GiftBodyPosition, string>;
  gap: GiftBodyGap;
  type: Record<GiftBodyType, string>;
}

export interface GiftBodyProps extends ComponentProps<'div'> {
  href?: string;
  type?: GiftBodyType;
  position?: GiftBodyPosition;
  gap?: keyof GiftBodyGap;
  showChevron?: boolean;
  contentClassName?: string;
}

export const GiftBody: FC<GiftBodyProps> = ({
  href,
  type = 'normal',
  position = 'between',
  gap = 'md',
  showChevron = false,
  children,
  className,
  contentClassName,
  ...props
}) => {
  const Component = isUndefined(href) ? 'div' : Link;

  return (
    <Component
      href={href as string}
      className={twMerge(styles.type[type], className)}
    >
      <div
        className={twMerge(
          styles.body.base,
          styles.body.position[position],
          styles.body.gap[gap],
          styles.body.type[type],
          contentClassName
        )}
        {...props}
      >
        {children}

        {showChevron && (
          <div>
            <SvgIcon name="ChevronRight" />
          </div>
        )}
      </div>
    </Component>
  );
};
