// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { Button } from '@/components/Button';
import { BoxContent } from '@/components/BoxContent';
import { SvgIcon } from '@/components/SvgIcon';
import { redirectToLineChatRoomOA } from '@/utils/redirect';
import { useLiff } from '@/contexts/liff';

const MaintenanceModeScreen: FC = () => {
  const { liff } = useLiff();

  return (
    <>
      <div className="container mx-auto">
        <BoxContent>
          <BoxContent.Body>
            <div className="pt-[160px]">
              <div className="w-fit mx-auto mb-8">
                <SvgIcon name="MaintenanceMode" />
              </div>
              <div className="text-center mb-4">
                <h3 className="text-base font-bold">メンテナンスのお知らせ</h3>
              </div>
              <div className="w-fit mx-auto">
                <p className="text-sm leading-6">
                  いつもピアコネをご利用いただきまして、
                  <br />
                  誠にありがとうございます。
                  <br />
                  LINE公式アカウントのメンテナンス（5時
                  <br />
                  頃～6時頃）に伴い、サービスを停止して
                  <br />
                  おります。
                  <br />
                  ご不便・ご迷惑をお掛けいたしますこと、
                  <br />
                  深くお詫び申し上げます。
                </p>
              </div>
            </div>
          </BoxContent.Body>
          <BoxContent.Footer>
            <Button
              fullSized
              type="submit"
              size="xl"
              color="primary"
              onClick={() => redirectToLineChatRoomOA(liff)}
            >
              トーク画面へ戻る
            </Button>
          </BoxContent.Footer>
        </BoxContent>
      </div>
    </>
  );
};

export default MaintenanceModeScreen;
