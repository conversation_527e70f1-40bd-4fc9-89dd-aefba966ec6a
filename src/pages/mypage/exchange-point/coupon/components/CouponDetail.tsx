// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { Button } from '@/components/Button';
import { SvgIcon } from '@/components/SvgIcon';
import { IGetCouponDetailResponse } from '@/types/exchange-points';
import { transformText } from '@/utils/text';
import Image from 'next/image';
import { FC, useState } from 'react';
import { styles } from '../../[giftId]/components/styles';
import { formatTime } from '@/utils/time';
import { ClipboardDocumentCheckIcon } from '@heroicons/react/24/solid';
import classNames from 'classnames';

interface CouponDetailProps {
  coupon: IGetCouponDetailResponse;
}

export const CouponDetail: FC<CouponDetailProps> = ({ coupon }) => {
  const [isCopied, setIsCopied] = useState(false);

  return (
    <>
      <div className="flex flex-grow flex-col justify-between h-full">
        <div className="main">
          {coupon.gift_image ? (
            <figure className="relative pt-[100%]">
              <Image
                fill
                quality={100}
                src={coupon.gift_image}
                alt={coupon.gift_name}
                className="w-full object-cover rounded"
              />
            </figure>
          ) : (
            <div className="relative w-full pt-[100%] bg-[#E2E2E2] rounded">
              <div className="flex flex-col justify-center items-center gap-3 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                <SvgIcon name="ImageXLarge" />
                <p className="text-sm text-gray-3 leading-[22px] font-bold min-w-[200px] text-center">
                  画像が表示できませんでした。
                </p>
              </div>
            </div>
          )}

          <div className="space-y-3.5 mt-6">
            <div className="space-y-1.5">
              <h5
                title={coupon.gift_company_name}
                className="text-sm text-theme-main mb-1.5 line-clamp-2"
              >
                {coupon.gift_company_name}
              </h5>
              <h3
                title={coupon.gift_name}
                className="text-base text-theme-main font-bold line-clamp-3"
              >
                {coupon.gift_name}
              </h3>
            </div>

            <div className="inline-block relative">
              <span className="text-[13px] text-theme-main leading-6">
                クーポンコード
              </span>
            </div>

            <div>
              <div className="flex items-center">
                <div className="rounded p-3 bg-white border border-[#DBDBDB] font-bold text-base w-full text-center overflow-hidden text-ellipsis whitespace-nowrap">
                  {coupon.code}
                </div>
                <div
                  className={classNames(
                    'rounded-full w-11 h-11 bg-theme-secondary flex items-center justify-center cursor-pointer ml-6 flex-shrink-0',
                    { 'pointer-events-none': isCopied }
                  )}
                  onClick={async () => {
                    try {
                      if (
                        navigator.clipboard &&
                        navigator.clipboard.writeText
                      ) {
                        await navigator.clipboard.writeText(coupon.code);
                      } else {
                        // Fallback for older browsers
                        const textArea = document.createElement('textarea');
                        textArea.value = coupon.code;
                        textArea.style.position = 'fixed';
                        textArea.style.opacity = '0';
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                      }

                      setIsCopied(true);

                      setTimeout(() => {
                        setIsCopied(false);
                      }, 2000);
                    } catch (err) {
                      console.error('Failed to copy text: ', err);
                    }
                  }}
                >
                  {isCopied ? (
                    <ClipboardDocumentCheckIcon className="h-6 w-6 text-white" />
                  ) : (
                    <SvgIcon name="CopyOutlined" />
                  )}
                </div>
              </div>
              <p className="text-sm mt-2.5">
                有効期限 :{' '}
                <span className="font-bold">
                  {formatTime({
                    format: 'YYYY年MM月DD日',
                    time: coupon.expired_at,
                  })}
                </span>
              </p>
            </div>

            <div className="space-y-1.5 !mt-10 !mb-7">
              <pre
                dangerouslySetInnerHTML={{
                  __html: transformText(coupon.coupon_about || '', styles),
                }}
                className={styles.base}
              />
            </div>

            {coupon.service_url && (
              <div>
                <Button
                  fullSized
                  size="xl"
                  color="primary"
                  onClick={() => (window.location.href = coupon.service_url)}
                >
                  {coupon.button_text || 'クーポンを利用する'}
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};
