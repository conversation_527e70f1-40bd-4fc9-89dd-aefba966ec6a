// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import Image from 'next/image';
import React from 'react';
import LogoMain from '@/assets/images/Logo.png';

const GuideCalendar: React.FC = () => {
  return (
    <div className="px-4">
      <div className="py-3 px-4 flex justify-between items-center mt-[30px] mb-5 rounded-[4px] bg-white">
        <p className="text-sm font-medium">入館カレンダーについて</p>
        <Image src={LogoMain} alt="Logo" className="w-[100px]" />
      </div>
      <ul
        className="list-disc text-sm font-medium space-y-1.5"
        style={{ marginInlineStart: '20px' }}
      >
        <li>入館した日と回数を月ごとに一目で確認ができるカレンダーです</li>
        <li>
          ゴールドジム店舗に設置してある専用のQRコードを読み込むとポイントとは別にカレンダーにスタンプが自動的に付与されます
        </li>
        <li>
          スタンプは1日最大2個まで付与されます
          <ul className="space-y-1.5">
            <li className="mt-1.5">
              <p className="flex">
                <span className="mr-0.5">※</span>
                <span>
                  2個目のスタンプは1個目の付与から3時間以上経過しないと付与されません
                </span>
              </p>
            </li>
            <li>
              <p className="flex">
                <span className="mr-0.5">※</span>
                <span>ポイントの付与は1日1回限りです</span>
              </p>
            </li>
          </ul>
        </li>
        <li>
          スタンプの数に応じてステータスが変わります
          <ul className="space-y-1.5">
            <li className="mt-1.5">
              ※ <span className="inline-block w-[58px]">20個以上</span>
              <span className="inline-block mx-1">...</span>
              GOLD
            </li>
            <li>
              ※ <span className="inline-block w-[58px]">12個以上</span>
              <span className="inline-block mx-1">...</span>
              SILVER
            </li>
            <li>
              ※ <span className="inline-block w-[58px]">4個以上</span>
              <span className="inline-block mx-1">...</span>
              BRONZE
            </li>
            <li>
              ※ <span className="inline-block w-[58px]">4個未満</span>
              <span className="inline-block mx-1">...</span>
              FIGHT
            </li>
          </ul>
        </li>
      </ul>
    </div>
  );
};

export default GuideCalendar;
