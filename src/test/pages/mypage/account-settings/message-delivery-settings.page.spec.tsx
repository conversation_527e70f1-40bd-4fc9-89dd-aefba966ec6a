// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React from 'react';
import { render, screen } from '@testing-library/react';
import MessageDeliverySettingsScreen from '@/pages/mypage/account-settings/message-delivery-settings.page';
import messageDeliverySettingsApi from '@/services/internal/modules/message-delivery-settings';
import { IGroupedCompanies } from '@/types/message-delivery-settings';

describe('pages/mypage/account-settings/message-delivery-settings.page.spec.tsx', () => {
  const companies: IGroupedCompanies = {
    G0001: [
      {
        company_id: 'C0002',
        company_name: 'カロミル',
        groups: ['G0001'],
        status: 'on',
      },
      {
        company_id: 'C0074',
        company_name: 'ThaoNTT0811',
        groups: ['G0001'],
        status: 'off',
      },
    ],
    G0002: [
      {
        company_id: 'C0002',
        company_name: 'カロミル',
        groups: ['G0001'],
        status: 'on',
      },
      {
        company_id: 'C0074',
        company_name: 'ThaoNTT0811',
        groups: ['G0001'],
        status: 'off',
      },
    ],
  };

  beforeAll(() => {
    const state = jest.spyOn(React, 'useState');
    state.mockReturnValue([companies, jest.fn()]);
  });

  describe('A11y', () => {
    it('Should render in the document', async () => {
      const response = jest.spyOn(
        messageDeliverySettingsApi,
        'getMessageDeliverySettingV2'
      );

      response.mockResolvedValue(companies);
      render(<MessageDeliverySettingsScreen />);

      const checkbox = screen.getByRole('checkbox', {
        name: 'カロミル',
      });

      expect(checkbox).toBeInTheDocument();
    });
  });
});
