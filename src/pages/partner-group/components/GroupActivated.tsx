// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React from 'react';

interface GroupActivatedProps {
  className: string;
  companyName?: string;
  partnerGroup: string[];
}
const GroupActivated: React.FC<GroupActivatedProps> = ({
  companyName,
  className,
  partnerGroup,
}) => {
  return (
    <div className={`flex flex-wrap ${className}`}>
      {partnerGroup.includes('G0001') ? (
        <h3 className="font-normal">
          ピアコネの参加グループにヘルスケア・育児・働き方グループを追加しました。
          <br />
          <br />
          トーク画面に戻ると、{companyName}
          のサービスメニューが表示されているのでご確認ください。
          <br />
          <br />
          また、メニュー左下［サービス変更］より「ペット」のタブを選択すると、「ペット」グループが表示されます。「ペット」を選択すると、ペットのサービスメニューに変更できます。
        </h3>
      ) : (
        <h3 className="font-normal">
          ピアコネの参加グループにペットグループを追加しました。
          <br />
          <br />
          トーク画面に戻ると、{companyName}
          を含むサービスメニューが表示されているのでご確認ください。
          <br />
          <br />
          また、メニュー左下［サービス変更］より「ヘルスケア・育児・働き方」のタブを選択すると、「ヘルスケア・育児・働き方」グループの企業のサービスが一覧で表示されます。ご利用中のサービスを選択すると、そのサービスのメニューに変更できます。
        </h3>
      )}
    </div>
  );
};

export default GroupActivated;
