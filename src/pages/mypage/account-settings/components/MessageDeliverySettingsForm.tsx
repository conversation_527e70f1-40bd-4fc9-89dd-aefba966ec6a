// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { Button } from '@/components/Button';
import { Label } from '@/components/Label';
import { OptionType, Select2 } from '@/components/Select2';
import { Spinner } from '@/components/Spinner';
import { SvgIcon } from '@/components/SvgIcon';
import { ICompany, IGroupedCompanies } from '@/types/message-delivery-settings';
import { FC } from 'react';
import ModalSubmit from './ModalSubmit';
import { dirtyCheck } from '@/utils';
import { useMessageDeliverySettingsChange } from '../hooks/useMessageDeliverySettingsChange';

const MessageDeliverySettingsForm: FC<{
  companies: IGroupedCompanies;
  myPartnerGroup: OptionType[];
  companyOn: string[];
  reCallApi: () => void;
}> = ({ companies, myPartnerGroup, companyOn = [], reCallApi }) => {
  const {
    isOpenModalConfirm,
    setIsOpenModalConfirm,
    valueSelected,
    setValueSelected,
    preValueSelected,
    setValueSelectedTemp,
    register,
    handleSubmit,
    isFetching,
    handleSubmitForm,
    onCancelEdit,
    getValues,
    setValue,
  } = useMessageDeliverySettingsChange({
    companies,
    myPartnerGroup,
    companyOn,
    reCallApi,
  });

  const filteredOptions = myPartnerGroup.filter(option => {
    if (!valueSelected) return true;
    return option.value !== valueSelected.value;
  });

  return (
    <>
      {myPartnerGroup.length && Object.keys(companies).length > 0 && (
        <form
          noValidate
          className="flex flex-grow flex-col justify-between h-full"
        >
          <div className="main">
            {myPartnerGroup.length > 1 && (
              <Select2
                options={filteredOptions}
                placeholder="Placeholder"
                onChange={(value: OptionType | undefined) => {
                  if (value) {
                    setValueSelectedTemp(value);
                    if (
                      dirtyCheck(companyOn, getValues('companies')) &&
                      value.value !== valueSelected.value
                    ) {
                      setIsOpenModalConfirm(true);
                      setValueSelectedTemp(value);
                    } else {
                      setValueSelected(value);
                    }
                  }
                }}
                value={valueSelected}
                className="mb-8"
              />
            )}

            <div className="pb-6">
              <h2 className="font-bold text-base">
                配信設定はこちらから変更可能です
                <br />
                チェックが付いているサービスからお得な情報を受け取ることができます
              </h2>
            </div>

            <div className="space-y-6 flex flex-col">
              {Array.isArray(
                companies[valueSelected.value as keyof typeof companies]
              ) &&
                (
                  companies[
                    valueSelected.value as keyof typeof companies
                  ] as unknown as ICompany[]
                ).map(company => (
                  <Label
                    key={company.company_id}
                    htmlFor={company.company_id}
                    className="flex items-center"
                  >
                    <div>
                      <SvgIcon
                        name={
                          getValues('companies').includes(company.company_id)
                            ? 'CheckboxChecked'
                            : 'Checkbox'
                        }
                      />
                    </div>
                    <input
                      id={company.company_id}
                      type="checkbox"
                      {...register('companies')}
                      name="companies"
                      value={company.company_id}
                      className="hidden"
                      onChange={e => {
                        const { checked, value } = e.target;
                        checked
                          ? setValue('companies', [
                              ...getValues('companies'),
                              value,
                            ])
                          : setValue(
                              'companies',
                              companyOn.length > 0
                                ? getValues('companies').filter(
                                    id => id !== value
                                  )
                                : []
                            );
                      }}
                    />
                    <span className="ml-3">{company.company_name}</span>
                  </Label>
                ))}
            </div>
          </div>
          {/* Open modal submit */}
          <div className="pt-8">
            <Button
              fullSized
              size="lg"
              color={
                !dirtyCheck(companyOn, getValues('companies'))
                  ? 'gray-10'
                  : 'primary'
              }
              disabled={!dirtyCheck(companyOn, getValues('companies'))}
              onClick={handleSubmit(handleSubmitForm)}
            >
              {isFetching && (
                <div className="mr-3">
                  <Spinner size="lg" color="primary" />
                </div>
              )}
              変更する
            </Button>
          </div>
        </form>
      )}

      <ModalSubmit
        isOpenModalConfirm={isOpenModalConfirm}
        setIsOpenModalConfirm={e => {
          setIsOpenModalConfirm(e);
          preValueSelected?.value && setValueSelected(preValueSelected);
        }}
        onCancelEdit={onCancelEdit}
      />
    </>
  );
};

export default MessageDeliverySettingsForm;
