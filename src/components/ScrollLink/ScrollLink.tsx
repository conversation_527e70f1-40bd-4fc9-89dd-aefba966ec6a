// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import Link, { LinkProps } from 'next/link';
import React, { FC, ReactNode } from 'react';

export interface ScrollLinkProps extends LinkProps {
  className?: string;
  children?: ReactNode;
}

export const ScrollLink: FC<ScrollLinkProps> = ({
  href,
  onClick,
  className,
  children,
  ...props
}) => {
  const handleScroll = (e: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
    e.preventDefault();
    //remove everything before the hash
    const targetId = e.currentTarget.href.replace(/.*\#/, '');
    const elem = document.getElementById(targetId);
    elem?.scrollIntoView({ behavior: 'smooth', block: 'start' });
  };

  return (
    <Link href={href} className={className} onClick={handleScroll} {...props}>
      {children}
    </Link>
  );
};
