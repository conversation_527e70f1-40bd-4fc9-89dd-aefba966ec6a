import { DefaultSeoProps } from 'next-seo';

// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
export interface IAppConfig {
  liffId: string;
  companyId: string;
  cookies?: { [key: string]: string };
}

export interface IConfigLoginLine {
  response_type?: string;
  client_id?: string;
  redirect_uri?: string;
  state?: string;
  scope?: string;
  nonce?: string;
  prompt?: string;
  max_age?: string;
  ui_locales?: string;
  bot_prompt?: string;
  initial_amr_display?: string;
  switch_amr?: string;
  disable_auto_login?: string;
  disable_ios_auto_login?: string;
  code_challenge?: string;
  code_challenge_method?: string;
}

export type IsMaintenanceType = { is_maintenance: boolean };

export interface ROUTERS {
  home: string;
  register: string;
  registered: string;
  registerConfirm: string;
  authError: string;
  friendshipError: string;
  blockedError: string;
  changeRm: string;
  checkinCalendar: string;
  checkinReadQR: string;
  checkinWrongQR: string;
  checkinResult: string;
  lottery: string;
  lotteryAlreadyApplied: string;
  lotteryOutOfPeriod: string;
  myPage: string;
  myPageTop: string;
  newsList: string;
  new: string;
  accountSetting: string;
  changeProfile: string;
  notificationSetting: string;
  pointHistory: string;
  pointHistoryExchanged: string;
  exchangePoint: string;
  exchangePointDetail: string;
  howToGetPoints: string;
  partnerList: string;
  help: string;
  faq: string;
  inquiry: string;
  withdrawal: string;
  notFound: string;
  checkCookie: string;
  membershipError: string;
  maintenance: string;
  serviceOverview: string;
  redirect: string;
  redirectGift: string;
  redirectGoogleForm: string;
  redirectKyosaiId: string;
  service: string;
  healthCheck: string;
  joinPartnerGroup: string;
  imageForm: string;
  expiredError: string;
  couponDetail: string;
  membershipOptions: string;
  login: string;
  webLogin: string;
}

export type MetaSeoDetail = DefaultSeoProps;

export type MetaSeo = Record<keyof ROUTERS, MetaSeoDetail>;
