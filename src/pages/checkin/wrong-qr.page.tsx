// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { useRouter } from 'next/router';
import { routers } from '@/constants/routes';
import { Button } from '@/components/Button';
import { BoxContent } from '@/components/BoxContent';

const CheckinWrongQrScreen = () => {
  const router = useRouter();

  return (
    <>
      <div className="container mx-auto">
        <BoxContent>
          <BoxContent.Body className="pt-[96px]">
            <div className="text-center">
              <h3 className="text-base font-bold mb-4">
                このQRコードは「チェックインdeポイント」
                <br />
                専用QRコードではないようです
              </h3>
              <h3 className="text-base font-bold mx-auto">
                各店舗設置の「チェックインdeポイント」
                <br />
                専用QRコードを読み取ってください
              </h3>
            </div>
          </BoxContent.Body>
          <BoxContent.Footer>
            <Button
              fullSized
              type="submit"
              size="xl"
              color="primary"
              onClick={() => router.push(routers.checkinReadQR)}
            >
              QRコード読み取り
            </Button>
          </BoxContent.Footer>
        </BoxContent>
      </div>
    </>
  );
};

export default CheckinWrongQrScreen;
