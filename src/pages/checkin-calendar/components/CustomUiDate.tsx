// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import GOLD_GYM_IMG from '@/assets/images/GoldGym.png';
import { IActivity, ICalendarData } from '@/types/checkin-calendar';
import * as holiday_jp from '@holiday-jp/holiday_jp';
import dayjs from 'dayjs';
import Image from 'next/image';
import React from 'react';

interface CustomUiDateProps {
  args: { date: Date };
  currentMonthData: ICalendarData;
}

const CustomUiDate: React.FC<CustomUiDateProps> = ({
  args,
  currentMonthData,
}) => {
  let className = '';
  const date = dayjs(args.date);
  const stampDate: IActivity | undefined = currentMonthData.activities.find(
    obj =>
      dayjs(obj.date).format('YYYY/MM/DD') ===
      dayjs(args.date).format('YYYY/MM/DD')
  );
  const day = dayjs(args.date).format('DD');
  const dateStr = date.format('YYYY/MM/DD');
  const isToday =
    dayjs(args.date).format('YYYY/MM/DD') === dayjs().format('YYYY/MM/DD');
  const hasMemo = stampDate && stampDate.memo && stampDate.memo.length > 0;

  if (args.date.getDay() === 0) {
    className = 'sunday';
  } else if (args.date.getDay() === 6) {
    className = 'saturday';
  }

  if (holiday_jp.isHoliday(new Date(dateStr))) {
    className = 'holiday';
  }

  return (
    <div
      className={`normal-day ${hasMemo ? 'has-memo' : ''} ${
        isToday ? 'today' : ''
      }`}
    >
      <div className={`day-item ${className}`}>{Number(day)}</div>
      <div className="stamp-box flex w-full h-1/2 justify-center">
        {stampDate && stampDate.stamps > 0 ? (
          <div className="w-1/2 h-full p-0.5">
            <Image
              className="w-full h-full text-white bg-[#D9D9D9] rounded-[50%] flex items-center justify-center"
              width={20}
              height={20}
              alt=""
              src={GOLD_GYM_IMG}
            />
          </div>
        ) : (
          <div className="w-1/2 h-full p-0.5">
            <div className="stamp-placeholder w-full h-full text-white bg-[#D9D9D9] rounded-[50%] flex items-center justify-center text-[75%]">
              1
            </div>
          </div>
        )}
        {stampDate && stampDate.stamps > 1 ? (
          <div className="w-1/2 h-full p-0.5">
            <Image
              className="w-full h-full text-white bg-[#D9D9D9] rounded-[50%] flex items-center justify-center"
              width={20}
              height={20}
              alt=""
              src={GOLD_GYM_IMG}
            />
          </div>
        ) : (
          <div className="w-1/2 h-full p-0.5">
            <div className="stamp-placeholder w-full h-full text-white bg-[#D9D9D9] rounded-[50%] flex items-center justify-center text-[75%]">
              2
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomUiDate;
