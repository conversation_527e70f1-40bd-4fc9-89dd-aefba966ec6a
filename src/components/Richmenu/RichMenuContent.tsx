// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { ICompany } from '@/types/company';
import { ChevronRightIcon } from '@heroicons/react/24/solid';
import Image from 'next/image';
import { ComponentProps, FC } from 'react';
import { Spinner } from '../Spinner';
import { IRichMenu } from '@/types/richmenu';

export interface RichMenuContentProps extends ComponentProps<'div'> {
  loading: boolean;
  richMenuIdSelected: string;
  richmenus: ICompany[];
  onSelectRichMenu: (richmenu: IRichMenu) => Promise<void>;
}

export const RichMenuContent: FC<RichMenuContentProps> = ({
  loading,
  richmenus,
  richMenuIdSelected,
  onSelectRichMenu,
  ...props
}) => {
  return (
    <div className="space-y-5">
      {richmenus?.length > 0 &&
        richmenus.map(item => (
          <div key={item.company_id} className="space-y-1" {...props}>
            {item.category && !item.is_repeated && (
              <div className="px-2.5 py-1 rounded-lg bg-gray-9 inline-block text-gray-80 text-xs text-center leading-5 font-normal max-w-full min-w-[182px]">
                {item.category}
              </div>
            )}

            {item.description && (
              <div className="text-gray-80 text-[11px] font-normal leading-[18px]">
                {item.description}
              </div>
            )}

            <div className="space-y-2">
              <div
                data-testid={item.company_id}
                className="w-full min-h-[64px] border-solid border rounded border-gray-10 p-3 flex justify-between items-center cursor-pointer"
                onClick={() => onSelectRichMenu(item)}
              >
                <div>
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded">
                      <Image
                        width={40}
                        height={40}
                        className={
                          item.company_id === 'C0005'
                            ? 'object-contain h-full'
                            : 'object-cover'
                        }
                        alt={item.company_name}
                        src={item.company_image}
                      />
                    </div>
                    <div className="text-gray-80 text-xs font-normal truncate leading-5">
                      {item.company_name}
                    </div>
                  </div>
                </div>
                {loading && richMenuIdSelected === item.company_id ? (
                  <Spinner size="xl" />
                ) : (
                  <ChevronRightIcon className="w-4 h-4 stroke-[2px] stroke-gray-80" />
                )}
              </div>
            </div>
          </div>
        ))}
    </div>
  );
};
