// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import service from '..';
import {
  ILinkRichMenuWithCompanyIdPayload,
  ILinkRichMenuWithCompanyIdResponse,
} from '@/types/richmenu';

const basePath = '/rich-menus';

const richMenuApi = {
  linkRichMenuWithCompanyId: (payload: ILinkRichMenuWithCompanyIdPayload) =>
    service.post<ILinkRichMenuWithCompanyIdResponse>(
      `${basePath}/link-rich-menu-to-company`,
      payload
    ),
};

export default richMenuApi;
