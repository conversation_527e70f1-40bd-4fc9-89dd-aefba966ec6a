// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC, useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import punycode from 'punycode';
import isEmail from 'validator/lib/isEmail';
import { yupResolver } from '@hookform/resolvers/yup';
import { Button } from '@/components/Button';
import { Label } from '@/components/Label';
import { Select } from '@/components/Select';
import { SvgIcon } from '@/components/SvgIcon';
import { TextInput } from '@/components/TextInput';
import { HelperText } from '@/components/HelperText';
import { useSessionStorage } from '@/hooks/use-storage';
import { IUserRegisterForm, IUserGender } from '@/types/user';
import { routers } from '@/constants/routes';
import {
  PREFECTURE_SELECTED_DEFAULT,
  USER_INFO_REGISTER,
  USER_PREFECTURE_SELECTED,
  USER_REGISTER_ERRORS,
  USER_GENDERS_LIST,
  EMAIL,
  PASSWORD,
  BIRTH_MONTH_YEAR,
  PREFECTURE,
  GENDER,
  POLICY,
  TERM,
  BIRTH_YEARS,
  BIRTH_MONTHS,
} from '@/constants/user';
import { IIErrorDetailResponse } from '@/types/errors';
import { Logo } from '@/components/Logo';
import TermsModal from '@/components/Modal/TermsModal';
import PoliciesModal from '@/components/Modal/PoliciesModal';
import { ShowHiddenPassword } from '@/components/Layout/ShowHiddenPassword';
import { encrypt } from '@/utils/crypto';
import { PREFECTURES } from '@/constants/prefectures';
import { templateString } from '@/utils/text';
import { redirectWithoutEmptyQuery } from '@/utils/redirect';
import { Message } from '@/constants/message';
import {
  EMAIL_EXCLUDE_NON_ASCII_REGEX,
  PASSWORD_MUST_CONTAIN_LETTER_NUMBER,
} from '@/constants/regex';
import { SHOW_PRIVACY_MODAL, SHOW_TERMS_MODAL } from '@/constants/common';
import { useLiff } from '@/contexts/liff';

const schema = yup.object({
  email: yup
    .string()
    .required(templateString(Message.validation.required, { field: EMAIL }))
    .test('email', Message.validation.emailFormatInvalid, (value?: string) => {
      return isEmail(value || '');
    })
    .test('email', Message.validation.emailFormatInvalid, (value?: string) => {
      return EMAIL_EXCLUDE_NON_ASCII_REGEX.test(
        // resolve bug https://stackoverflow.com/questions/32117497/input-type-email-value-in-chrome-with-accented-characters-wrong
        punycode.toUnicode(value || '')
      );
    }),
  password: yup
    .string()
    .required(templateString(Message.validation.required, { field: PASSWORD }))
    .min(8, Message.validation.passwordLengthInvalid)
    .max(20, Message.validation.passwordLengthInvalid)
    .matches(
      PASSWORD_MUST_CONTAIN_LETTER_NUMBER,
      Message.validation.passwordLengthInvalid
    ),
  confirmPassword: yup
    .string()
    .required(Message.validation.passwordConfirmRequired)
    .oneOf([yup.ref('password')], Message.validation.passwordConfirmNotSame),
  birthYear: yup
    .string()
    .required(
      templateString(Message.validation.required, { field: BIRTH_MONTH_YEAR })
    ),
  birthMonth: yup
    .string()
    .required(
      templateString(Message.validation.required, { field: BIRTH_MONTH_YEAR })
    ),
  prefecture: yup
    .string()
    .required(
      templateString(Message.validation.required, { field: PREFECTURE })
    ),
  gender: yup
    .string()
    .required(templateString(Message.validation.required, { field: GENDER })),
  isAcceptPolicies: yup
    .boolean()
    .oneOf(
      [true],
      templateString(Message.validation.required, { field: POLICY })
    ),
  isAcceptTerms: yup
    .boolean()
    .oneOf(
      [true],
      templateString(Message.validation.required, { field: TERM })
    ),
});

const RegisterForm: FC = () => {
  const { userInfo, memberStatus, setError: setLiffError } = useLiff();
  const router = useRouter();
  const { show: showQuery } = router.query;
  const [isEmailUnique, setIsEmailUnique] = useState(true);
  const [isShowPoliciesModal, setIsShowPoliciesModal] = useState(false);
  const [isShowTermsModal, setIsShowTermsModal] = useState(false);
  const [isShowPassword, setIsShowPassword] = useState(false);
  const [isShowConfirmPassword, setIsShowConfirmPassword] = useState(false);
  const [userInfoRegister, setUserInfoRegister] =
    useSessionStorage<IUserRegisterForm>({
      key: USER_INFO_REGISTER,
    });
  const [, setUserPrefectureSelected] = useSessionStorage<string>({
    key: USER_PREFECTURE_SELECTED,
  });
  const [userRegisterErrors, , removeUserRegisterErrors] = useSessionStorage<
    IIErrorDetailResponse[]
  >({
    key: USER_REGISTER_ERRORS,
  });

  const {
    register,
    watch,
    trigger,
    handleSubmit,
    getValues,
    setValue,
    setError,
    formState: { errors, isValid },
  } = useForm<IUserRegisterForm>({
    mode: 'onChange',
    resolver: yupResolver(schema),
    defaultValues: {
      isAcceptPolicies: false,
      isAcceptTerms: false,
    },
  });

  const password = watch('password');
  const confirmPassword = watch('confirmPassword');
  watch(['gender', 'isAcceptTerms', 'isAcceptPolicies']);

  useEffect(() => {
    if (password && confirmPassword) {
      trigger('password');
      trigger('confirmPassword');
    }
  }, [password, confirmPassword]);

  const registerHandler = (data: IUserRegisterForm) => {
    const passwordHash = encrypt(data.password);
    const confirmPasswordHash = encrypt(data.confirmPassword);
    const dataAfterHash: IUserRegisterForm = {
      ...data,
      password: passwordHash,
      confirmPassword: confirmPasswordHash,
    };

    setUserInfoRegister(dataAfterHash);
    setUserPrefectureSelected(data.prefecture ?? PREFECTURE_SELECTED_DEFAULT);
    removeUserRegisterErrors();
    router.push(routers.registerConfirm);
  };

  useEffect(() => {
    if (userInfoRegister) {
      setValue('email', userInfoRegister.email);
      setValue('birthYear', userInfoRegister.birthYear);
      setValue('birthMonth', userInfoRegister.birthMonth);
      setValue('prefecture', userInfoRegister.prefecture);
      setValue('gender', userInfoRegister.gender);
      setValue('isAcceptPolicies', userInfoRegister.isAcceptPolicies);
      setValue('isAcceptTerms', userInfoRegister.isAcceptTerms);
    }
  }, [setValue, userInfoRegister]);

  useEffect(() => {
    if (userRegisterErrors && userRegisterErrors.length > 0) {
      userRegisterErrors.forEach((err: IIErrorDetailResponse) => {
        if (err.property === 'email') {
          const errConstraintsEmail = err.constraints;
          if (errConstraintsEmail.IsValidEmail) {
            setIsEmailUnique(false);
            setError('email', {
              type: 'server',
              message: Message.validation.emailExisted,
            });
          }
        }
      });
    }
  }, [setError, userRegisterErrors]);

  useEffect(() => {
    if (Object.keys(userInfo).length) {
      router.push(routers.registered);
    }
  }, [router, userInfo]);

  const handleShowModal = () => {
    const arrShow: string[] = [SHOW_PRIVACY_MODAL, SHOW_TERMS_MODAL];

    /** showQuery is Array */
    if (Array.isArray(showQuery)) {
      /** Filter all value not match in arrShow
       * @example showQuery = ['terms', 'lorem'] convert to showQuery = ['terms']
       */
      const arrShowFilter = showQuery.filter(item => arrShow.includes(item));

      if (arrShowFilter.includes(SHOW_PRIVACY_MODAL)) {
        setIsShowPoliciesModal(true);
      }

      if (arrShowFilter.includes(SHOW_TERMS_MODAL)) {
        setIsShowTermsModal(true);
      }

      if (JSON.stringify(arrShowFilter) !== JSON.stringify(showQuery)) {
        redirectWithoutEmptyQuery({ show: arrShowFilter });
      }
    } else {
      /** showQuery is String */
      if (showQuery === SHOW_PRIVACY_MODAL) {
        setIsShowPoliciesModal(true);
      } else if (showQuery === SHOW_TERMS_MODAL) {
        setIsShowTermsModal(true);
      } else {
        redirectWithoutEmptyQuery({ show: '' });
      }
    }
  };

  useEffect(() => {
    if (showQuery) {
      handleShowModal();
    }
  }, [showQuery]);

  useEffect(() => {
    if (
      memberStatus &&
      !memberStatus.is_member &&
      !memberStatus.is_user_linked_with_inflow_company
    ) {
      setLiffError(Message.error.friendNotAdd);
      router.replace(routers.friendshipError);
    }
  }, [memberStatus, router]);

  const onAcceptHandler = (type: 'policies' | 'terms') => {
    if (type === 'policies') {
      setValue('isAcceptPolicies', true);
      setIsShowPoliciesModal(false);
    } else if (type === 'terms') {
      setValue('isAcceptTerms', true);
      setIsShowTermsModal(false);
    }
  };

  return (
    <>
      <Logo />

      <p className="font-medium text-base">
        すでにアカウントをお持ちの方は
        <span
          className="text-theme-primary cursor-pointer underline  underline-offset-[3px]"
          onClick={() => {
            const emailValue = getValues('email');
            if (emailValue) {
              router.push({
                pathname: routers.login,
                query: { email: emailValue },
              });
            } else {
              router.push(routers.login);
            }
          }}
        >
          こちらのページ
        </span>
        からログインしてください。
      </p>

      <form
        noValidate
        className="space-y-6"
        onSubmit={handleSubmit(registerHandler)}
      >
        {/* Email */}
        <div>
          <Label htmlFor="email" className="block mb-2">
            メールアドレス
          </Label>
          <TextInput
            type="email"
            id="email"
            placeholder="（例）<EMAIL>"
            {...register('email', {
              onChange: () => setIsEmailUnique(true),
            })}
            color={errors.email ? 'failure' : 'default'}
            helperText={errors.email?.message}
          />
        </div>
        {/* Password */}
        <div>
          <Label htmlFor="password" className="block mb-2">
            パスワード（半角英数字8文字以上20文字以内）
          </Label>
          <TextInput
            type={isShowPassword ? 'text' : 'password'}
            id="password"
            {...register('password')}
            color={errors.password ? 'failure' : 'default'}
            helperText={errors.password?.message}
            interactableIcon={
              <ShowHiddenPassword
                isShow={isShowPassword}
                setIsShow={setIsShowPassword}
              />
            }
          />
        </div>
        {/* Confirm password */}
        <div>
          <Label htmlFor="confirmPassword" className="block mb-2">
            パスワード再入力
          </Label>
          <TextInput
            type={isShowConfirmPassword ? 'text' : 'password'}
            id="confirmPassword"
            {...register('confirmPassword')}
            color={errors.confirmPassword ? 'failure' : 'default'}
            helperText={errors.confirmPassword?.message}
            interactableIcon={
              <ShowHiddenPassword
                isShow={isShowConfirmPassword}
                setIsShow={setIsShowConfirmPassword}
              />
            }
          />
        </div>
        {/* Birthday */}
        <div>
          <Label className="block mb-2">生まれた年・月</Label>
          <div className="grid grid-cols-12 gap-2">
            <div className="col-span-6">
              <Select
                id="birthYear"
                required
                defaultValue=""
                {...register('birthYear')}
                color={errors.birthYear ? 'failure' : 'default'}
              >
                <option value="" disabled>
                  年
                </option>

                {BIRTH_YEARS.map(year => (
                  <option key={year} value={year}>
                    {year}年
                  </option>
                ))}
              </Select>
            </div>
            <div className="col-span-6">
              <Select
                id="birthMonth"
                required
                defaultValue=""
                {...register('birthMonth')}
                color={errors.birthMonth ? 'failure' : 'default'}
              >
                <option value="" disabled>
                  月
                </option>
                {BIRTH_MONTHS.map(month => (
                  <option key={month} value={month}>
                    {month}月
                  </option>
                ))}
              </Select>
            </div>
          </div>
          {(errors.birthYear?.message || errors.birthMonth?.message) && (
            <HelperText
              value={templateString(Message.validation.required, {
                field: BIRTH_MONTH_YEAR,
              })}
              color="failure"
            />
          )}
        </div>
        {/* Prefecture */}
        <div>
          <Label htmlFor="prefecture" className="block mb-2">
            お住いの都道府県
          </Label>
          <Select
            required
            id="prefecture"
            defaultValue=""
            {...register('prefecture')}
            color={errors.prefecture ? 'failure' : 'default'}
            helperText={errors.prefecture?.message}
          >
            <option value="" disabled>
              都道府県を選択してください
            </option>
            {PREFECTURES.map((prefecture: string) => (
              <option key={prefecture} value={prefecture}>
                {prefecture}
              </option>
            ))}
          </Select>
        </div>
        {/* Gender */}
        <div>
          <Label className="block mb-2">性別</Label>
          <div className="grid grid-cols-12 gap-1">
            {USER_GENDERS_LIST.map((gender: IUserGender) => (
              <div className="col-span-3" key={gender.id}>
                <Button
                  outline={getValues('gender') !== gender.value}
                  fullSized
                  size="xl"
                  color="primary-gray"
                  {...register('gender')}
                  onClick={() =>
                    setValue('gender', gender.value, { shouldValidate: true })
                  }
                  customSpan="!px-0"
                >
                  <span
                    className={
                      getValues('gender') === gender.value
                        ? 'font-bold'
                        : 'font-normal'
                    }
                  >
                    {gender.label}
                  </span>
                </Button>
              </div>
            ))}
          </div>
        </div>
        {/* Policies & Terms of service */}
        <div>
          <div className="space-y-2">
            <div>
              <Button
                className="hover:!bg-theme-secondary/10"
                outline={!getValues('isAcceptPolicies')}
                fullSized
                size="lg"
                align="left"
                color="gray-accent"
                onClick={() => setIsShowPoliciesModal(true)}
              >
                <SvgIcon
                  name={
                    !getValues('isAcceptPolicies')
                      ? 'CheckGrayIcon'
                      : 'CheckAccentIcon'
                  }
                />
                <span className="font-normal ml-3">
                  プライバシーポリシーに同意する
                </span>
              </Button>
              {errors.isAcceptPolicies?.message && (
                <HelperText
                  value={templateString(Message.validation.required, {
                    field: POLICY,
                  })}
                  color="failure"
                />
              )}
            </div>

            <div>
              <Button
                className="hover:!bg-theme-secondary/10"
                outline={!getValues('isAcceptTerms')}
                fullSized
                size="lg"
                align="left"
                color="gray-accent"
                onClick={() => setIsShowTermsModal(true)}
              >
                <SvgIcon
                  name={
                    !getValues('isAcceptTerms')
                      ? 'CheckGrayIcon'
                      : 'CheckAccentIcon'
                  }
                />
                <span className="font-normal ml-3">会員規約に同意する</span>
              </Button>
              {errors.isAcceptTerms?.message && (
                <HelperText
                  value={templateString(Message.validation.required, {
                    field: TERM,
                  })}
                  color="failure"
                />
              )}
            </div>
          </div>
        </div>
        {/* Submit */}
        <div>
          <Button
            fullSized
            type="submit"
            size="xl"
            color={isValid && isEmailUnique ? 'primary' : 'gray-10'}
            disabled={!isValid || !isEmailUnique}
          >
            確認画面に進む
          </Button>
        </div>
      </form>
      <PoliciesModal
        isShowModal={isShowPoliciesModal}
        setIsShowModal={setIsShowPoliciesModal}
        onAcceptHandler={onAcceptHandler}
        isShowNotice={true}
      />
      <TermsModal
        isShowModal={isShowTermsModal}
        setIsShowModal={setIsShowTermsModal}
        onAcceptHandler={onAcceptHandler}
        isShowNotice={true}
      />
    </>
  );
};

export default RegisterForm;
