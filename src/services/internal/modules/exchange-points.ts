// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import service from '..';
import {
  IExchangedGiftDetailResponse,
  IConfirmExchangeGiftPayload,
  IExchangeableGiftDetailResponse,
  IExchangeableGiftListResponse,
  IConfirmExchangeGiftResponse,
  IToggleGiftUsagePayload,
  IExchangedGiftListParams,
  IGetCouponDetailParams,
  IGetCouponDetailResponse,
  IToggleGiftUsageResponse,
} from '@/types/exchange-points';

const basePath = '/exchange-points';

const exchangePointsApi = {
  getExchangedGiftList: (params: IExchangedGiftListParams) =>
    service.get<IExchangedGiftDetailResponse[]>(`${basePath}/history`, params),
  getExchangeableGiftList: () =>
    service.get<IExchangeableGiftListResponse[]>(`${basePath}/exchangeable`),
  getExchangeableGiftDetail: (giftId: string) =>
    service.get<IExchangeableGiftDetailResponse>(
      `${basePath}/exchangeable/${giftId}`
    ),
  confirmExchangeGift: (payload: IConfirmExchangeGiftPayload) =>
    service.post<IConfirmExchangeGiftResponse>(
      `${basePath}/gift-card`,
      payload
    ),
  toggleGiftUsage: (exchangeId: string, payload: IToggleGiftUsagePayload) =>
    service.patch<IToggleGiftUsageResponse>(
      `${basePath}/gift/${exchangeId}`,
      payload
    ),
  getCouponDetail: (params: IGetCouponDetailParams) =>
    service.get<IGetCouponDetailResponse>(
      `${basePath}/${params.id}/coupon/${params.code}`
    ),
};

export default exchangePointsApi;
