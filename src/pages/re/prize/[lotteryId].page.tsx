// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import Loading from '@/components/Spinner/Loading';
import { useRedirect } from '@/hooks/use-redirect';
import redirectApi from '@/services/internal/modules/redirect';
import { useRouter } from 'next/router';
import { routers } from '@/constants/routes';

const RedirectGifteeScreen: FC = () => {
  const router = useRouter();
  useRedirect(async ({ query: { lotteryId }, redirect }) => {
    const { isWinner, redirect: redirectUrl } =
      await redirectApi.verifyRedirectGifteeUrl({
        lotteryId,
      });

    if (!isWinner) {
      router.push(routers.notFound, router.asPath);
      return;
    }

    redirect(redirectUrl);
  });

  return <Loading />;
};

export default RedirectGifteeScreen;
