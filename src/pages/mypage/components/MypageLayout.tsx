// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { Button } from '@/components/Button';
import { routers } from '@/constants/routes';
import { IThemeBoolean, IThemeSizes } from '@/types/theme';
import classNames from 'classnames';
import React, { ComponentProps, FC } from 'react';

interface ILayoutSpacing
  extends Pick<
    IThemeSizes,
    'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl'
  > {
  [key: string]: string;
  none: string;
}

interface IMypageLayoutTheme {
  withContainer: IThemeBoolean;
  base: string;
  spacingTop: ILayoutSpacing;
  spacingBottom: ILayoutSpacing;
}

const styles: IMypageLayoutTheme = {
  withContainer: { on: 'container mx-auto', off: '' },
  base: 'w-full md:max-w-md mx-auto min-h-screen flex flex-col justify-between',
  spacingTop: {
    none: '',
    xs: 'pt-1',
    sm: 'pt-2',
    md: 'pt-3',
    lg: 'pt-4',
    xl: 'pt-5',
    '2xl': 'pt-6',
    '3xl': 'pt-7',
    '4xl': 'pt-8',
  },
  spacingBottom: {
    none: '',
    xs: 'pt-1',
    sm: 'pt-2',
    md: 'pt-3',
    lg: 'pt-4',
    xl: 'pt-5',
    '2xl': 'pt-6',
    '3xl': 'pt-7',
    '4xl': 'pt-8',
  },
};

interface IMypageLayout extends ComponentProps<'div'> {
  withFooter?: boolean;
  withContainer?: boolean;
  spacingTop?: keyof ILayoutSpacing;
  spacingBottom?: keyof ILayoutSpacing;
}

const MypageLayout: FC<IMypageLayout> = ({
  withFooter = true,
  withContainer = true,
  spacingTop = '2xl',
  spacingBottom = 'xs',
  children,
  className,
}) => {
  return (
    <div
      className={classNames(styles.withContainer[withContainer ? 'on' : 'off'])}
    >
      <div
        className={classNames(
          styles.base,
          styles.spacingTop[spacingTop],
          className
        )}
      >
        {children}
        {withFooter && (
          <div className={classNames(styles.spacingBottom[spacingBottom])}>
            <Button
              fullSized
              size="xl"
              color="link-primary"
              customSpan="leading-6"
              href={routers.myPageTop}
            >
              ＼マイページTOPに戻る／
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default MypageLayout;
