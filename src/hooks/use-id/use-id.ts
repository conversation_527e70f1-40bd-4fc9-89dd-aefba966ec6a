// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { useState } from 'react';
import { randomId } from '@/utils/random';
import { useReactId } from './use-react-id';
import { useIsomorphicEffect } from '../use-isomorphic-effect';

export function useId(staticId?: string) {
  const reactId = useReactId();
  const [uuid, setUuid] = useState(reactId);

  useIsomorphicEffect(() => {
    setUuid(randomId());
  }, []);

  if (typeof staticId === 'string') {
    return staticId;
  }

  if (typeof window === 'undefined') {
    return reactId;
  }

  return uuid;
}
