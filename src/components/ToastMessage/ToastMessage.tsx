// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { ToastContainer, cssTransition } from 'react-toastify';

const ToastMessage: FC = () => {
  const Fade = cssTransition({
    enter: 'fadeIn',
    exit: 'fadeOut',
  });

  return (
    <ToastContainer
      draggable={false}
      pauseOnHover
      closeOnClick
      pauseOnFocusLoss
      rtl={false}
      autoClose={3000}
      newestOnTop={false}
      hideProgressBar={true}
      theme="colored"
      position="top-center"
      limit={1}
      className="absolute top-1/2 -translate-y-1/2 h-[56px] px-2 mt-[-28px] text-center"
      transition={Fade}
      icon={false}
      closeButton={false}
    />
  );
};

export default ToastMessage;
