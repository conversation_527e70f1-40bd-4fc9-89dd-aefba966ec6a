# gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
name: Verify build
on: [pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - uses: actions/setup-node@v3
        with:
          node-version: 16
          cache: yarn
      - run: yarn install
      - run: npx commitlint --from ${{ github.event.pull_request.base.sha }} --to ${{ github.event.pull_request.head.sha }} --verbose
      - run: yarn check:type && yarn check:format && yarn check:style && yarn check:lint
      - run: yarn build
