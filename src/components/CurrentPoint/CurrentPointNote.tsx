// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { twMerge } from 'tailwind-merge';
import React, { ComponentProps, FC } from 'react';
import { styles } from './CurrentPoint.styles';

export type CurrentPointNoteProps = ComponentProps<'p'>;

export const CurrentPointNote: FC<CurrentPointNoteProps> = ({
  className,
  children,
  ...props
}) => {
  return (
    <p className={twMerge(styles.note.base, className)} {...props}>
      {children}
    </p>
  );
};
