// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import Image from 'next/image';
import classNames from 'classnames';
import { ComponentProps, FC } from 'react';
import LogoMain from '@/assets/images/Logo.png';
import styles from './Logo.styles';
import { IThemeBoolean } from '@/types/theme';

export interface ILogoTheme {
  base: string;
  image: string;
  withBox: IThemeBoolean;
}

export interface ILogoProps extends ComponentProps<'div'> {
  box?: boolean;
}

export const Logo: FC<ILogoProps> = ({ box, className, ...props }) => {
  const theme = styles;

  return (
    <div
      className={classNames(
        theme.base,
        theme.withBox[box ? 'on' : 'off'],
        className
      )}
      {...props}
    >
      <Image src={LogoMain} alt="Logo" className={classNames(theme.image)} />
    </div>
  );
};
