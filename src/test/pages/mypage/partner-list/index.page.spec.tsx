// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { LiffContextMockProviders } from '@/test/mocks';
import PartnerListScreen from '@/pages/mypage/partner-list.page';

jest.mock('next/router', () => require('next-router-mock'));

interface TestPartnerListScreenProps {
  isLoggedIn?: boolean;
}

const TestPartnerListScreen: FC<TestPartnerListScreenProps> = ({
  isLoggedIn = true,
}) => {
  const contextValue = { isLoggedIn };

  return (
    <LiffContextMockProviders contextValue={contextValue}>
      <PartnerListScreen />
    </LiffContextMockProviders>
  );
};

describe('pages/mypage/partner-list/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document when signed in', async () => {
      render(<TestPartnerListScreen />);
      const href = screen.getByRole('link');

      expect(href).toBeInTheDocument();
    });

    it('should render in the document when not sign in', async () => {
      render(<TestPartnerListScreen isLoggedIn={false} />);
      const loading = screen.getByRole('status');

      expect(loading).toBeInTheDocument();
    });
  });

  describe('User interactions', () => {
    it('should trigger select partner', async () => {
      render(<TestPartnerListScreen />);
      const partner = screen.getAllByTestId('C0001');
      fireEvent.click(partner[0]);
      // when you select a partner, it should redirect to the partner page and show loading icon
      const loading = screen.getByRole('status');
      expect(loading).toBeInTheDocument();
    });
  });
});
