// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { MessageBox } from '@/components/MessageBox';

interface ExchangeableGiftDetailErrorProps {
  retry?: () => void;
}

export const ExchangeableGiftDetailError: FC<
  ExchangeableGiftDetailErrorProps
> = ({ retry }) => {
  return (
    <MessageBox className="pt-[100px]">
      <MessageBox.Body className="max-w-[75vw]">
        <MessageBox.Icon name="AccessDenied" />
        <MessageBox.Content>
          現在ギフト詳細を読み込めないか、ギフトの準備中です。
          <br />
          時間を空けて再度お試しください。
        </MessageBox.Content>
      </MessageBox.Body>
      <MessageBox.Action onClick={retry} customSpan="!font-bold">
        再度読み込む
      </MessageBox.Action>
    </MessageBox>
  );
};
