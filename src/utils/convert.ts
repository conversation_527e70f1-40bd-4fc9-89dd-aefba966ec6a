// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
/* eslint-disable @typescript-eslint/no-explicit-any */
import _isPlainObject from 'lodash/isPlainObject';
import { warning } from './debug';

export const convertObjectToFormData = (
  obj: Record<string, any> | Array<Record<string, any>>,
  formData: FormData = new FormData(),
  parentKey?: string
): FormData => {
  if (!(_isPlainObject(obj) || Array.isArray(obj))) {
    warning('The first parameter must be plain object or array of objects.');
    return new FormData();
  }

  if (!(formData instanceof FormData)) {
    warning('The second parameter must be instance of FormData.');
    return new FormData();
  }

  Object.entries(obj).forEach(([key, value]) => {
    const propName = parentKey ? `${parentKey}[${key}]` : key;

    if (Array.isArray(value)) {
      if (value.length > 0) convertObjectToFormData(value, formData, propName);
    } else if (
      _isPlainObject(value) &&
      !(value instanceof File) &&
      !(value instanceof Blob)
    ) {
      convertObjectToFormData(value, formData, propName);
    } else {
      formData.append(propName, value);
    }
  });

  return formData;
};

export const formatCurrentPoint = (currentPoint: number) => {
  return currentPoint.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};
