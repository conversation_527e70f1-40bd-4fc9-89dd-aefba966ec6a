// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC, useEffect } from 'react';
import Loading from '@/components/Spinner/Loading';
import { useRouter } from 'next/router';
import { routers } from '@/constants/routes';

const MyPageScreen: FC = () => {
  const router = useRouter();

  useEffect(() => {
    router.push(routers.myPageTop);
  }, []);

  return <Loading />;
};

export default MyPageScreen;
