// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { ComponentProps, FC } from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { MessageBox } from '@/components/MessageBox';

interface MessageBoxTestProps extends ComponentProps<'div'> {
  message?: string;
  action?: () => void;
}

const MessageBoxTest: FC<MessageBoxTestProps> = ({
  message = 'Lorem ipsum',
  action,
  ...props
}) => {
  return (
    <MessageBox {...props}>
      <MessageBox.Icon name="AccessDenied" />
      <MessageBox.Content data-testid="message">{message}</MessageBox.Content>
      <MessageBox.Action onClick={action}>Click here</MessageBox.Action>
    </MessageBox>
  );
};

describe('components/MessageBox/MessageBox.spec.tsx', () => {
  describe('A11y', () => {
    it('should component render ui', () => {
      render(<MessageBoxTest data-testid="message-box" />);
      const messageBox = screen.getByTestId('message-box');

      expect(messageBox).toBeInTheDocument();
    });
  });

  describe('Props', () => {
    it('should render message prop', () => {
      const message = 'Call api failure';
      render(<MessageBoxTest message={message} />);
      const content = screen.getByTestId('message');

      expect(content).toHaveTextContent(message);
    });
  });

  describe('User interactions', () => {
    it('should handle click', () => {
      const spy = jest.fn();

      render(<MessageBoxTest action={spy} />);
      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(spy).toHaveBeenCalledTimes(1);
    });
  });
});
