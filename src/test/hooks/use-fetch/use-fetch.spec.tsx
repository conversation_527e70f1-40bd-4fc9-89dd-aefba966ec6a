// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { DependencyList, FC } from 'react';
import { waitFor, render, screen } from '@testing-library/react';
import { useFetch } from '@/hooks/use-fetch';

interface TypeData {
  company_id: string;
  company_name: string;
  status: string;
}

const mockValue: TypeData = {
  company_id: 'C0001',
  company_name: 'test company',
  status: 'active',
};

interface TestComponentProps {
  fetcher: (payload: unknown) => Promise<TypeData[]>;
  handleError?: () => void;
  initialValue?: TypeData[];
  dependencies?: DependencyList | false;
}

const TestComponent: FC<TestComponentProps> = ({
  fetcher,
  handleError,
  initialValue = [],
  dependencies = [],
}) => {
  const { data, isErrorFetching } = useFetch<TypeData[]>({
    fetcher,
    handleError,
    dependencies,
    initialValue,
  });

  return (
    <div>
      <div data-testid="use-fetch">
        {data.map(item => (
          <div key={item.company_id}>{item.company_name}</div>
        ))}
      </div>
      <div data-testid="fetch-status">
        {isErrorFetching ? 'is fetching error' : 'has not error'}
      </div>
    </div>
  );
};

describe('hooks/use-fetch/use-fetch.spec.tsx', () => {
  describe('A11y', () => {
    it('should render right response in document when fetch success', async () => {
      const mockFunction = jest.fn();
      mockFunction.mockReturnValue([mockValue]);

      render(<TestComponent fetcher={mockFunction} />);

      await waitFor(() => {
        expect(screen.getByTestId('use-fetch')).toHaveTextContent(
          'test company'
        );
      });
    });

    it('should render handle error when fetch error', async () => {
      const mockErrorFunction = jest.fn();
      mockErrorFunction.mockRejectedValue([mockValue]);

      render(<TestComponent fetcher={mockErrorFunction} />);

      await waitFor(() => {
        expect(screen.getByTestId('fetch-status')).toHaveTextContent(
          'is fetching error'
        );
      });
    });

    it('should return if dependencies is falsy', async () => {
      const mockFunction = jest.fn();
      mockFunction.mockReturnValue([mockValue]);

      render(<TestComponent fetcher={mockFunction} dependencies={false} />);

      await waitFor(() => {
        expect(screen.getByTestId('fetch-status')).toHaveTextContent(
          'has not error'
        );
      });
    });
  });
});
