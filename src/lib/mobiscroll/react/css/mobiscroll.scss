// Theme variables for download builder
$mbsc-ios-theme: 'true' !default;
$mbsc-material-theme: 'true' !default;
$mbsc-windows-theme: 'true' !default;

// Component conditional loading variables
$mbsc-datepicker: true !default;
$mbsc-eventcalendar: true !default;
$mbsc-forms: true !default;
$mbsc-grid-layout: true !default;
$mbsc-popup: true !default;
$mbsc-select: true !default;

// Sub-components and Dependencies
$mbsc-button: false !default;
$mbsc-input: false !default;
$mbsc-scroller: false !default;
$mbsc-segmented: false !default;
$mbsc-calendar-view: false !default;

@if $mbsc-datepicker or $mbsc-eventcalendar {
  $mbsc-calendar-view: true;
}

@if $mbsc-datepicker or $mbsc-select {
  // pickers
  $mbsc-button: true;
  $mbsc-input: true;
  $mbsc-popup: true;
  $mbsc-scroller: true;
}

@if $mbsc-forms {
  $mbsc-button: true;
  $mbsc-input: true;
  $mbsc-segmented: true;
}

@if $mbsc-datepicker {
  $mbsc-segmented: true;
}

@if $mbsc-popup or $mbsc-eventcalendar {
  $mbsc-button: true;
}

// Page color variables

$mbsc-page-background-light: null !default;
$mbsc-page-background-dark: null !default;
$mbsc-page-text-light: null !default;
$mbsc-page-text-dark: null !default;

$mbsc-button-color-light: null !default;
$mbsc-button-color-dark: null !default;
$mbsc-button-text-light: null !default;
$mbsc-button-text-dark: null !default;

$mbsc-form-background-light: null !default;
$mbsc-form-background-dark: null !default;
$mbsc-form-accent-light: null !default;
$mbsc-form-accent-dark: null !default;
$mbsc-form-text-light: null !default;
$mbsc-form-text-dark: null !default;
$mbsc-form-error-light: null !default;
$mbsc-form-error-dark: null !default;

$mbsc-input-background-light: null !default;
$mbsc-input-background-dark: null !default;
$mbsc-input-text-light: null !default;
$mbsc-input-text-dark: null !default;
$mbsc-input-accent-light: null !default;
$mbsc-input-accent-dark: null !default;
$mbsc-input-border-light: null !default;
$mbsc-input-border-dark: null !default;

$mbsc-calendar-background-light: null !default;
$mbsc-calendar-background-dark: null !default;
$mbsc-calendar-text-light: null !default;
$mbsc-calendar-text-dark: null !default;
$mbsc-calendar-accent-light: null !default;
$mbsc-calendar-accent-dark: null !default;
$mbsc-calendar-border-light: null !default;
$mbsc-calendar-border-dark: null !default;
$mbsc-calendar-mark-light: null !default;
$mbsc-calendar-mark-dark: null !default;
$mbsc-calendar-event-light: null !default;
$mbsc-calendar-event-dark: null !default;

@function get-contrast-color($color) {
  @if (lightness($color) > 67%) {
    @return #000;
  } @else {
    @return #fff;
  }
}


@mixin mbsc-grid-sm() {
  .mbsc-col-sm {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }

  .mbsc-col-sm-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }

  .mbsc-col-sm-1 {
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }

  .mbsc-col-sm-2 {
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .mbsc-col-sm-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }

  .mbsc-col-sm-4 {
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .mbsc-col-sm-5 {
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }

  .mbsc-col-sm-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .mbsc-col-sm-7 {
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }

  .mbsc-col-sm-8 {
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }

  .mbsc-col-sm-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }

  .mbsc-col-sm-10 {
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }

  .mbsc-col-sm-11 {
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }

  .mbsc-col-sm-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .mbsc-offset-sm-0 {
    margin-left: 0;
  }

  .mbsc-offset-sm-1 {
    margin-left: 8.333333%;
  }

  .mbsc-offset-sm-2 {
    margin-left: 16.666667%;
  }

  .mbsc-offset-sm-3 {
    margin-left: 25%;
  }

  .mbsc-offset-sm-4 {
    margin-left: 33.333333%;
  }

  .mbsc-offset-sm-5 {
    margin-left: 41.666667%;
  }

  .mbsc-offset-sm-6 {
    margin-left: 50%;
  }

  .mbsc-offset-sm-7 {
    margin-left: 58.333333%;
  }

  .mbsc-offset-sm-8 {
    margin-left: 66.666667%;
  }

  .mbsc-offset-sm-9 {
    margin-left: 75%;
  }

  .mbsc-offset-sm-10 {
    margin-left: 83.333333%;
  }

  .mbsc-offset-sm-11 {
    margin-left: 91.666667%;
  }

  .mbsc-push-sm-0 {
    left: auto;
  }

  .mbsc-push-sm-1 {
    left: 8.33333333%;
  }

  .mbsc-push-sm-2 {
    left: 16.66666667%;
  }

  .mbsc-push-sm-3 {
    left: 25%;
  }

  .mbsc-push-sm-4 {
    left: 33.33333333%;
  }

  .mbsc-push-sm-5 {
    left: 41.66666667%;
  }

  .mbsc-push-sm-6 {
    left: 50%;
  }

  .mbsc-push-sm-7 {
    left: 58.33333333%;
  }

  .mbsc-push-sm-8 {
    left: 66.66666667%;
  }

  .mbsc-push-sm-9 {
    left: 75%;
  }

  .mbsc-push-sm-10 {
    left: 83.33333333%;
  }

  .mbsc-push-sm-11 {
    left: 91.66666667%;
  }

  .mbsc-push-sm-12 {
    left: 100%;
  }

  .mbsc-pull-sm-0 {
    right: auto;
  }

  .mbsc-pull-sm-1 {
    right: 8.33333333%;
  }

  .mbsc-pull-sm-2 {
    right: 16.66666667%;
  }

  .mbsc-pull-sm-3 {
    right: 25%;
  }

  .mbsc-pull-sm-4 {
    right: 33.33333333%;
  }

  .mbsc-pull-sm-5 {
    right: 41.66666667%;
  }

  .mbsc-pull-sm-6 {
    right: 50%;
  }

  .mbsc-pull-sm-7 {
    right: 58.33333333%;
  }

  .mbsc-pull-sm-8 {
    right: 66.66666667%;
  }

  .mbsc-pull-sm-9 {
    right: 75%;
  }

  .mbsc-pull-sm-10 {
    right: 83.33333333%;
  }

  .mbsc-pull-sm-11 {
    right: 91.66666667%;
  }

  .mbsc-pull-sm-12 {
    right: 100%;
  }
}

@mixin mbsc-grid-md() {
  .mbsc-col-md {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }

  .mbsc-col-md-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }

  .mbsc-col-md-1 {
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }

  .mbsc-col-md-2 {
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .mbsc-col-md-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }

  .mbsc-col-md-4 {
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .mbsc-col-md-5 {
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }

  .mbsc-col-md-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .mbsc-col-md-7 {
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }

  .mbsc-col-md-8 {
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }

  .mbsc-col-md-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }

  .mbsc-col-md-10 {
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }

  .mbsc-col-md-11 {
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }

  .mbsc-col-md-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .mbsc-offset-md-0 {
    margin-left: 0;
  }

  .mbsc-offset-md-1 {
    margin-left: 8.333333%;
  }

  .mbsc-offset-md-2 {
    margin-left: 16.666667%;
  }

  .mbsc-offset-md-3 {
    margin-left: 25%;
  }

  .mbsc-offset-md-4 {
    margin-left: 33.333333%;
  }

  .mbsc-offset-md-5 {
    margin-left: 41.666667%;
  }

  .mbsc-offset-md-6 {
    margin-left: 50%;
  }

  .mbsc-offset-md-7 {
    margin-left: 58.333333%;
  }

  .mbsc-offset-md-8 {
    margin-left: 66.666667%;
  }

  .mbsc-offset-md-9 {
    margin-left: 75%;
  }

  .mbsc-offset-md-10 {
    margin-left: 83.333333%;
  }

  .mbsc-offset-md-11 {
    margin-left: 91.666667%;
  }

  .mbsc-push-md-0 {
    left: auto;
  }

  .mbsc-push-md-1 {
    left: 8.33333333%;
  }

  .mbsc-push-md-2 {
    left: 16.66666667%;
  }

  .mbsc-push-md-3 {
    left: 25%;
  }

  .mbsc-push-md-4 {
    left: 33.33333333%;
  }

  .mbsc-push-md-5 {
    left: 41.66666667%;
  }

  .mbsc-push-md-6 {
    left: 50%;
  }

  .mbsc-push-md-7 {
    left: 58.33333333%;
  }

  .mbsc-push-md-8 {
    left: 66.66666667%;
  }

  .mbsc-push-md-9 {
    left: 75%;
  }

  .mbsc-push-md-10 {
    left: 83.33333333%;
  }

  .mbsc-push-md-11 {
    left: 91.66666667%;
  }

  .mbsc-push-md-12 {
    left: 100%;
  }

  .mbsc-pull-md-0 {
    right: auto;
  }

  .mbsc-pull-md-1 {
    right: 8.33333333%;
  }

  .mbsc-pull-md-2 {
    right: 16.66666667%;
  }

  .mbsc-pull-md-3 {
    right: 25%;
  }

  .mbsc-pull-md-4 {
    right: 33.33333333%;
  }

  .mbsc-pull-md-5 {
    right: 41.66666667%;
  }

  .mbsc-pull-md-6 {
    right: 50%;
  }

  .mbsc-pull-md-7 {
    right: 58.33333333%;
  }

  .mbsc-pull-md-8 {
    right: 66.66666667%;
  }

  .mbsc-pull-md-9 {
    right: 75%;
  }

  .mbsc-pull-md-10 {
    right: 83.33333333%;
  }

  .mbsc-pull-md-11 {
    right: 91.66666667%;
  }

  .mbsc-pull-md-12 {
    right: 100%;
  }
}

@mixin mbsc-grid-lg() {
  .mbsc-col-lg {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }

  .mbsc-col-lg-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }

  .mbsc-col-lg-1 {
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }

  .mbsc-col-lg-2 {
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .mbsc-col-lg-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }

  .mbsc-col-lg-4 {
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .mbsc-col-lg-5 {
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }

  .mbsc-col-lg-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .mbsc-col-lg-7 {
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }

  .mbsc-col-lg-8 {
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }

  .mbsc-col-lg-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }

  .mbsc-col-lg-10 {
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }

  .mbsc-col-lg-11 {
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }

  .mbsc-col-lg-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .mbsc-offset-lg-0 {
    margin-left: 0;
  }

  .mbsc-offset-lg-1 {
    margin-left: 8.333333%;
  }

  .mbsc-offset-lg-2 {
    margin-left: 16.666667%;
  }

  .mbsc-offset-lg-3 {
    margin-left: 25%;
  }

  .mbsc-offset-lg-4 {
    margin-left: 33.333333%;
  }

  .mbsc-offset-lg-5 {
    margin-left: 41.666667%;
  }

  .mbsc-offset-lg-6 {
    margin-left: 50%;
  }

  .mbsc-offset-lg-7 {
    margin-left: 58.333333%;
  }

  .mbsc-offset-lg-8 {
    margin-left: 66.666667%;
  }

  .mbsc-offset-lg-9 {
    margin-left: 75%;
  }

  .mbsc-offset-lg-10 {
    margin-left: 83.333333%;
  }

  .mbsc-offset-lg-11 {
    margin-left: 91.666667%;
  }

  .mbsc-push-lg-0 {
    left: auto;
  }

  .mbsc-push-lg-1 {
    left: 8.33333333%;
  }

  .mbsc-push-lg-2 {
    left: 16.66666667%;
  }

  .mbsc-push-lg-3 {
    left: 25%;
  }

  .mbsc-push-lg-4 {
    left: 33.33333333%;
  }

  .mbsc-push-lg-5 {
    left: 41.66666667%;
  }

  .mbsc-push-lg-6 {
    left: 50%;
  }

  .mbsc-push-lg-7 {
    left: 58.33333333%;
  }

  .mbsc-push-lg-8 {
    left: 66.66666667%;
  }

  .mbsc-push-lg-9 {
    left: 75%;
  }

  .mbsc-push-lg-10 {
    left: 83.33333333%;
  }

  .mbsc-push-lg-11 {
    left: 91.66666667%;
  }

  .mbsc-push-lg-12 {
    left: 100%;
  }

  .mbsc-pull-lg-0 {
    right: auto;
  }

  .mbsc-pull-lg-1 {
    right: 8.33333333%;
  }

  .mbsc-pull-lg-2 {
    right: 16.66666667%;
  }

  .mbsc-pull-lg-3 {
    right: 25%;
  }

  .mbsc-pull-lg-4 {
    right: 33.33333333%;
  }

  .mbsc-pull-lg-5 {
    right: 41.66666667%;
  }

  .mbsc-pull-lg-6 {
    right: 50%;
  }

  .mbsc-pull-lg-7 {
    right: 58.33333333%;
  }

  .mbsc-pull-lg-8 {
    right: 66.66666667%;
  }

  .mbsc-pull-lg-9 {
    right: 75%;
  }

  .mbsc-pull-lg-10 {
    right: 83.33333333%;
  }

  .mbsc-pull-lg-11 {
    right: 91.66666667%;
  }

  .mbsc-pull-lg-12 {
    right: 100%;
  }
}

@mixin mbsc-grid-xl() {
  .mbsc-col-xl {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }

  .mbsc-col-xl-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }

  .mbsc-col-xl-1 {
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }

  .mbsc-col-xl-2 {
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .mbsc-col-xl-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }

  .mbsc-col-xl-4 {
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .mbsc-col-xl-5 {
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }

  .mbsc-col-xl-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .mbsc-col-xl-7 {
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }

  .mbsc-col-xl-8 {
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }

  .mbsc-col-xl-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }

  .mbsc-col-xl-10 {
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }

  .mbsc-col-xl-11 {
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }

  .mbsc-col-xl-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .mbsc-offset-xl-0 {
    margin-left: 0;
  }

  .mbsc-offset-xl-1 {
    margin-left: 8.333333%;
  }

  .mbsc-offset-xl-2 {
    margin-left: 16.666667%;
  }

  .mbsc-offset-xl-3 {
    margin-left: 25%;
  }

  .mbsc-offset-xl-4 {
    margin-left: 33.333333%;
  }

  .mbsc-offset-xl-5 {
    margin-left: 41.666667%;
  }

  .mbsc-offset-xl-6 {
    margin-left: 50%;
  }

  .mbsc-offset-xl-7 {
    margin-left: 58.333333%;
  }

  .mbsc-offset-xl-8 {
    margin-left: 66.666667%;
  }

  .mbsc-offset-xl-9 {
    margin-left: 75%;
  }

  .mbsc-offset-xl-10 {
    margin-left: 83.333333%;
  }

  .mbsc-offset-xl-11 {
    margin-left: 91.666667%;
  }

  .mbsc-push-xl-0 {
    left: auto;
  }

  .mbsc-push-xl-1 {
    left: 8.33333333%;
  }

  .mbsc-push-xl-2 {
    left: 16.66666667%;
  }

  .mbsc-push-xl-3 {
    left: 25%;
  }

  .mbsc-push-xl-4 {
    left: 33.33333333%;
  }

  .mbsc-push-xl-5 {
    left: 41.66666667%;
  }

  .mbsc-push-xl-6 {
    left: 50%;
  }

  .mbsc-push-xl-7 {
    left: 58.33333333%;
  }

  .mbsc-push-xl-8 {
    left: 66.66666667%;
  }

  .mbsc-push-xl-9 {
    left: 75%;
  }

  .mbsc-push-xl-10 {
    left: 83.33333333%;
  }

  .mbsc-push-xl-11 {
    left: 91.66666667%;
  }

  .mbsc-push-xl-12 {
    left: 100%;
  }

  .mbsc-pull-xl-0 {
    right: auto;
  }

  .mbsc-pull-xl-1 {
    right: 8.33333333%;
  }

  .mbsc-pull-xl-2 {
    right: 16.66666667%;
  }

  .mbsc-pull-xl-3 {
    right: 25%;
  }

  .mbsc-pull-xl-4 {
    right: 33.33333333%;
  }

  .mbsc-pull-xl-5 {
    right: 41.66666667%;
  }

  .mbsc-pull-xl-6 {
    right: 50%;
  }

  .mbsc-pull-xl-7 {
    right: 58.33333333%;
  }

  .mbsc-pull-xl-8 {
    right: 66.66666667%;
  }

  .mbsc-pull-xl-9 {
    right: 75%;
  }

  .mbsc-pull-xl-10 {
    right: 83.33333333%;
  }

  .mbsc-pull-xl-11 {
    right: 91.66666667%;
  }

  .mbsc-pull-xl-12 {
    right: 100%;
  }
}

@if $mbsc-grid-layout {
  .mbsc-grid,
  .mbsc-grid-unresp,
  .mbsc-grid-fixed {
    width: 100%;
    padding-right: 1em;
    padding-left: 1em;
    margin-right: auto;
    margin-left: auto;
    box-sizing: border-box;

    * {
      box-sizing: border-box;
    }
  }

  .mbsc-form-grid {
    margin-top: 0.75em;
    margin-bottom: 0.75em;
  }

  .mbsc-no-padding .mbsc-col,
  .mbsc-no-padding [class*='mbsc-col-'],
  .mbsc-form-grid .mbsc-col,
  .mbsc-form-grid [class*='mbsc-col-'] {
    padding-right: 0;
    padding-left: 0;
  }

  .mbsc-row {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -1em;
    margin-left: -1em;
  }

  .mbsc-col {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }

  .mbsc-col-1,
  .mbsc-col-2,
  .mbsc-col-3,
  .mbsc-col-4,
  .mbsc-col-5,
  .mbsc-col-6,
  .mbsc-col-7,
  .mbsc-col-8,
  .mbsc-col-9,
  .mbsc-col-10,
  .mbsc-col-11,
  .mbsc-col-12,
  .mbsc-col,
  .mbsc-col-auto,
  .mbsc-col-sm-1,
  .mbsc-col-sm-2,
  .mbsc-col-sm-3,
  .mbsc-col-sm-4,
  .mbsc-col-sm-5,
  .mbsc-col-sm-6,
  .mbsc-col-sm-7,
  .mbsc-col-sm-8,
  .mbsc-col-sm-9,
  .mbsc-col-sm-10,
  .mbsc-col-sm-11,
  .mbsc-col-sm-12,
  .mbsc-col-sm,
  .mbsc-col-sm-auto,
  .mbsc-col-md-1,
  .mbsc-col-md-2,
  .mbsc-col-md-3,
  .mbsc-col-md-4,
  .mbsc-col-md-5,
  .mbsc-col-md-6,
  .mbsc-col-md-7,
  .mbsc-col-md-8,
  .mbsc-col-md-9,
  .mbsc-col-md-10,
  .mbsc-col-md-11,
  .mbsc-col-md-12,
  .mbsc-col-md,
  .mbsc-col-md-auto,
  .mbsc-col-lg-1,
  .mbsc-col-lg-2,
  .mbsc-col-lg-3,
  .mbsc-col-lg-4,
  .mbsc-col-lg-5,
  .mbsc-col-lg-6,
  .mbsc-col-lg-7,
  .mbsc-col-lg-8,
  .mbsc-col-lg-9,
  .mbsc-col-lg-10,
  .mbsc-col-lg-11,
  .mbsc-col-lg-12,
  .mbsc-col-lg,
  .mbsc-col-lg-auto,
  .mbsc-col-xl-1,
  .mbsc-col-xl-2,
  .mbsc-col-xl-3,
  .mbsc-col-xl-4,
  .mbsc-col-xl-5,
  .mbsc-col-xl-6,
  .mbsc-col-xl-7,
  .mbsc-col-xl-8,
  .mbsc-col-xl-9,
  .mbsc-col-xl-10,
  .mbsc-col-xl-11,
  .mbsc-col-xl-12,
  .mbsc-col-xl,
  .mbsc-col-xl-auto {
    position: relative;
    width: 100%;
    min-height: 1px;
    padding-right: 1em;
    padding-left: 1em;
  }

  .mbsc-col-1 {
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }

  .mbsc-col-2 {
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .mbsc-col-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }

  .mbsc-col-4 {
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .mbsc-col-5 {
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }

  .mbsc-col-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .mbsc-col-7 {
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }

  .mbsc-col-8 {
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }

  .mbsc-col-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }

  .mbsc-col-10 {
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }

  .mbsc-col-11 {
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }

  .mbsc-col-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .mbsc-col-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }

  .mbsc-offset-1 {
    margin-left: 8.333333%;
  }

  .mbsc-offset-2 {
    margin-left: 16.666667%;
  }

  .mbsc-offset-3 {
    margin-left: 25%;
  }

  .mbsc-offset-4 {
    margin-left: 33.333333%;
  }

  .mbsc-offset-5 {
    margin-left: 41.666667%;
  }

  .mbsc-offset-6 {
    margin-left: 50%;
  }

  .mbsc-offset-7 {
    margin-left: 58.333333%;
  }

  .mbsc-offset-8 {
    margin-left: 66.666667%;
  }

  .mbsc-offset-9 {
    margin-left: 75%;
  }

  .mbsc-offset-10 {
    margin-left: 83.333333%;
  }

  .mbsc-offset-11 {
    margin-left: 91.666667%;
  }

  // .mbsc-grid-sm {
  //   &.mbsc-grid-fixed {
  //     max-width: 540px;
  //   }

  //   @include mbsc-grid-sm();
  // }

  @media (min-width: 576px) {
    .mbsc-grid {
      &.mbsc-grid-fixed {
        max-width: 540px;
      }

      @include mbsc-grid-sm();
    }
  }

  // .mbsc-grid-md {
  //   &.mbsc-grid-fixed {
  //     max-width: 720px;
  //   }

  //   @include mbsc-grid-md();
  // }

  @media (min-width: 768px) {
    .mbsc-grid {
      &.mbsc-grid-fixed {
        max-width: 720px;
      }

      @include mbsc-grid-md();
    }
  }

  // .mbsc-grid-lg {
  //   &.mbsc-grid-fixed {
  //     max-width: 960px;
  //   }

  //   @include mbsc-grid-lg();
  // }

  @media (min-width: 992px) {
    .mbsc-grid {
      &.mbsc-grid-fixed {
        max-width: 960px;
      }

      @include mbsc-grid-lg();
    }
  }

  // .mbsc-grid-xl {
  //   &.mbsc-grid-fixed {
  //     max-width: 1140px;
  //   }

  //   @include mbsc-grid-xl();
  // }

  @media (min-width: 1200px) {
    .mbsc-grid {
      &.mbsc-grid-fixed {
        max-width: 1140px;
      }

      @include mbsc-grid-xl();
    }
  }

  .mbsc-align-items-start {
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }

  .mbsc-align-items-center {
    -ms-flex-align: center !important;
    align-items: center !important;
  }

  .mbsc-align-items-end {
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }

  .mbsc-justify-content-start {
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }

  .mbsc-justify-content-center {
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }

  .mbsc-justify-content-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }

  .mbsc-justify-content-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }

  .mbsc-justify-content-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }
}



$mbsc-ios-accent: #007aff !default;
$mbsc-ios-background: #f7f7f7 !default;
$mbsc-ios-text: #000000 !default;

$mbsc-ios-dark-accent: #ff9f0a !default;
$mbsc-ios-dark-background: #000000 !default;
$mbsc-ios-dark-text: #ffffff !default;

/* Base colors */
$mbsc-ios-primary: #3f97f6 !default;
$mbsc-ios-secondary: #90979e !default;
$mbsc-ios-success: #43be5f !default;
$mbsc-ios-danger: #f5504e !default;
$mbsc-ios-warning: #f8b042 !default;
$mbsc-ios-info: #5bb7c5 !default;
$mbsc-ios-light: #fff !default;
$mbsc-ios-dark: #47494a !default;

$mbsc-ios-error: #d8332a;

/* Form colors */

$mbsc-ios-form-background: $mbsc-form-background-light !default;
$mbsc-ios-dark-form-background: $mbsc-form-background-dark !default;
$mbsc-ios-form-text: $mbsc-form-text-light !default;
$mbsc-ios-dark-form-text: $mbsc-form-text-dark !default;
$mbsc-ios-form-accent: $mbsc-form-accent-light !default;
$mbsc-ios-dark-form-accent: $mbsc-form-accent-dark !default;
$mbsc-ios-form-error: $mbsc-form-error-light !default;
$mbsc-ios-dark-form-error: $mbsc-form-error-dark !default;

/* Calendar colors (will be used by eventcalendar, calendar, range) */

$mbsc-ios-calendar-background: $mbsc-calendar-background-light !default;
$mbsc-ios-calendar-text: $mbsc-calendar-text-light !default;
$mbsc-ios-calendar-accent: $mbsc-calendar-accent-light !default;
$mbsc-ios-calendar-border: $mbsc-calendar-border-light !default;
$mbsc-ios-calendar-mark: $mbsc-calendar-mark-light !default;
$mbsc-ios-calendar-event: $mbsc-calendar-event-light !default;

$mbsc-ios-dark-calendar-background: $mbsc-calendar-background-dark !default;
$mbsc-ios-dark-calendar-text: $mbsc-calendar-text-dark !default;
$mbsc-ios-dark-calendar-accent: $mbsc-calendar-accent-dark !default;
$mbsc-ios-dark-calendar-border: $mbsc-calendar-border-dark !default;
$mbsc-ios-dark-calendar-mark: $mbsc-calendar-mark-dark !default;
$mbsc-ios-dark-calendar-event: $mbsc-calendar-event-dark !default;

$mbsc-ios-colors: (
  // Colors map
  'background': $mbsc-ios-background,
  'text': $mbsc-ios-text,
  'accent': $mbsc-ios-accent,

  'calendar-background': $mbsc-ios-calendar-background,
  'calendar-text': $mbsc-ios-calendar-text,
  'calendar-accent': $mbsc-ios-calendar-accent,
  'calendar-border': $mbsc-ios-calendar-border,
  'calendar-mark': $mbsc-ios-calendar-mark,
  'calendar-event': $mbsc-ios-calendar-event,

  'form-background': $mbsc-ios-form-background,
  'form-text': $mbsc-ios-form-text,
  'form-accent': $mbsc-ios-form-accent,
  'form-error': $mbsc-ios-form-error
);

$mbsc-ios-dark-colors: (
  // Colors map
  'background': $mbsc-ios-dark-background,
  'text': $mbsc-ios-dark-text,
  'accent': $mbsc-ios-dark-accent,

  'calendar-background': $mbsc-ios-dark-calendar-background,
  'calendar-text': $mbsc-ios-dark-calendar-text,
  'calendar-accent': $mbsc-ios-dark-calendar-accent,
  'calendar-border': $mbsc-ios-dark-calendar-border,
  'calendar-mark': $mbsc-ios-dark-calendar-mark,
  'calendar-event': $mbsc-ios-dark-calendar-event,

  'form-background': $mbsc-ios-dark-form-background,
  'form-text': $mbsc-ios-dark-form-text,
  'form-accent': $mbsc-ios-dark-form-accent,
  'form-error': $mbsc-ios-dark-form-error
);

@function mbsc-ios-colors($params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $background-limited: hsl(hue($background), saturation($background), max(lightness($background), 3%));
  $background-alt: lighten($background, 6%);
  $border-color: '';

  @if (lightness($background) > 50%) {
    $border-color: darken($background-limited, 17%);
  } @else {
    $border-color: lighten($background, 20%);
  }

  @return (
    // General colors
    'background-alt': $background-alt,
    'background-limited': $background-limited,
    'border-color': $border-color
  );
}


@mixin mbsc-ios-notifications($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $border-color: '';

  // Light background
  @if (lightness($background) > 50%) {
    $border-color: darken($background, 17%);
  }

  // Dark background
  @else {
    $border-color: lighten($background, 20%);
  }

  .mbsc-#{$theme} {
    .mbsc-toast-background {
      background: rgba($text, 0.8);
      color: get-contrast-color($text);
    }

    &.mbsc-prompt-input {
      border-color: $border-color;
    }

    &.mbsc-color-none .mbsc-snackbar-button.mbsc-button {
      color: $accent;
    }
  }
}


@if ($mbsc-ios-theme and $mbsc-forms) {
  .mbsc-ios {
    .mbsc-toast-message {
      line-height: 1.857143em; // 26px / 14px based
      border-radius: 1.785715em; // 25px / 14px based
    }

    .mbsc-alert-title {
      margin: 0.277778em 0;
      font-size: 1.125em;
      text-align: center;
    }

    .mbsc-alert-message {
      margin: 0.357143em 0;
      font-size: 0.875em;
      text-align: center;
    }

    &.mbsc-prompt-input.mbsc-font {
      margin-top: 1em;
      border-radius: 0.5em;
      border-width: 1px;
      border-style: solid;
    }

    &.mbsc-prompt-input::after,
    &.mbsc-prompt-input::before {
      display: none;
    }

    &.mbsc-prompt-input .mbsc-textfield {
      height: 1.75em;
      line-height: 1.75em;
      padding: 0 0.25em;
    }

    &.mbsc-prompt-input .mbsc-label {
      height: 1.75em;
      line-height: 1.75em;
      padding-left: 0.25em;
    }

    &.mbsc-primary .mbsc-toast-background {
      background: $mbsc-ios-primary;
      color: get-contrast-color($mbsc-ios-primary);
    }

    &.mbsc-secondary .mbsc-toast-background {
      background: $mbsc-ios-secondary;
      color: get-contrast-color($mbsc-ios-secondary);
    }

    &.mbsc-success .mbsc-toast-background {
      background: $mbsc-ios-success;
      color: get-contrast-color($mbsc-ios-success);
    }

    &.mbsc-danger .mbsc-toast-background {
      background: $mbsc-ios-danger;
      color: get-contrast-color($mbsc-ios-danger);
    }

    &.mbsc-warning .mbsc-toast-background {
      background: $mbsc-ios-warning;
      color: get-contrast-color($mbsc-ios-warning);
    }

    &.mbsc-info .mbsc-toast-background {
      background: $mbsc-ios-info;
      color: get-contrast-color($mbsc-ios-info);
    }
  }

  @include mbsc-ios-notifications('ios', $mbsc-ios-colors);
  @include mbsc-ios-notifications('ios-dark', $mbsc-ios-dark-colors);
}



$mbsc-material-accent: #1a73e8 !default;
$mbsc-material-background: #fff !default;
$mbsc-material-text: #303030 !default;

$mbsc-material-dark-accent: #87b0f3 !default;
$mbsc-material-dark-background: #000 !default;
$mbsc-material-dark-text: #fff !default;

/* Base colors */
$mbsc-material-primary: #3f97f6 !default;
$mbsc-material-secondary: #90979e !default;
$mbsc-material-success: #43be5f !default;
$mbsc-material-danger: #f5504e !default;
$mbsc-material-warning: #f8b042 !default;
$mbsc-material-info: #5bb7c5 !default;
$mbsc-material-light: #fff !default;
$mbsc-material-dark: #47494a !default;

$mbsc-material-error: #de3226;

/* Form colors */

$mbsc-material-form-background: $mbsc-form-background-light !default;
$mbsc-material-dark-form-background: $mbsc-form-background-dark !default;
$mbsc-material-form-text: $mbsc-form-text-light !default;
$mbsc-material-dark-form-text: $mbsc-form-text-dark !default;
$mbsc-material-form-accent: $mbsc-form-accent-light !default;
$mbsc-material-dark-form-accent: $mbsc-form-accent-dark !default;
$mbsc-material-form-error: $mbsc-form-error-light !default;
$mbsc-material-dark-form-error: $mbsc-form-error-dark !default;

/* Calendar colors (will be used by eventcalendar, calendar, range) */

$mbsc-material-calendar-background: $mbsc-calendar-background-light !default;
$mbsc-material-calendar-text: $mbsc-calendar-text-light !default;
$mbsc-material-calendar-accent: $mbsc-calendar-accent-light !default;
$mbsc-material-calendar-border: $mbsc-calendar-border-light !default;
$mbsc-material-calendar-mark: $mbsc-calendar-mark-light !default;
$mbsc-material-calendar-event: $mbsc-calendar-event-light !default;

$mbsc-material-dark-calendar-background: $mbsc-calendar-background-dark !default;
$mbsc-material-dark-calendar-text: $mbsc-calendar-text-dark !default;
$mbsc-material-dark-calendar-accent: $mbsc-calendar-accent-dark !default;
$mbsc-material-dark-calendar-border: $mbsc-calendar-border-dark !default;
$mbsc-material-dark-calendar-mark: $mbsc-calendar-mark-dark !default;
$mbsc-material-dark-calendar-event: $mbsc-calendar-event-dark !default;

$mbsc-material-colors: (
  // Colors map
  'background': $mbsc-material-background,
  'text': $mbsc-material-text,
  'accent': $mbsc-material-accent,

  'calendar-background': $mbsc-material-calendar-background,
  'calendar-text': $mbsc-material-calendar-text,
  'calendar-accent': $mbsc-material-calendar-accent,
  'calendar-border': $mbsc-material-calendar-border,
  'calendar-mark': $mbsc-material-calendar-mark,
  'calendar-event': $mbsc-material-calendar-event,

  'form-background': $mbsc-material-form-background,
  'form-text': $mbsc-material-form-text,
  'form-accent': $mbsc-material-form-accent,
  'form-error': $mbsc-material-form-error
);

$mbsc-material-dark-colors: (
  // Colors map
  'background': $mbsc-material-dark-background,
  'text': $mbsc-material-dark-text,
  'accent': $mbsc-material-dark-accent,

  'calendar-background': $mbsc-material-dark-calendar-background,
  'calendar-text': $mbsc-material-dark-calendar-text,
  'calendar-accent': $mbsc-material-dark-calendar-accent,
  'calendar-border': $mbsc-material-dark-calendar-border,
  'calendar-mark': $mbsc-material-dark-calendar-mark,
  'calendar-event': $mbsc-material-dark-calendar-event,

  'form-background': $mbsc-material-dark-form-background,
  'form-text': $mbsc-material-dark-form-text,
  'form-accent': $mbsc-material-dark-form-accent,
  'form-error': $mbsc-material-dark-form-error
);

@function mbsc-material-colors($params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  @return ();
}


@mixin mbsc-material-notifications($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  .mbsc-#{$theme} {
    &.mbsc-color-none .mbsc-snackbar-button.mbsc-button {
      color: lighten($accent, 10%);
    }
  }
}


@if ($mbsc-material-theme and $mbsc-forms) {
  .mbsc-material {
    .mbsc-toast-message {
      border-radius: 1.571429em;
    }

    .mbsc-toast-background {
      background: #444;
      color: #fff;
    }

    &.mbsc-prompt-input.mbsc-font {
      margin: 1.5em 0 0 0;
    }

    &.mbsc-primary .mbsc-toast-background {
      background: $mbsc-material-primary;
      color: get-contrast-color($mbsc-material-primary);
    }

    &.mbsc-secondary .mbsc-toast-background {
      background: $mbsc-material-secondary;
      color: get-contrast-color($mbsc-material-secondary);
    }

    &.mbsc-success .mbsc-toast-background {
      background: $mbsc-material-success;
      color: get-contrast-color($mbsc-material-success);
    }

    &.mbsc-danger .mbsc-toast-background {
      background: $mbsc-material-danger;
      color: get-contrast-color($mbsc-material-danger);
    }

    &.mbsc-warning .mbsc-toast-background {
      background: $mbsc-material-warning;
      color: get-contrast-color($mbsc-material-warning);
    }

    &.mbsc-info .mbsc-toast-background {
      background: $mbsc-material-info;
      color: get-contrast-color($mbsc-material-info);
    }
  }

  @include mbsc-material-notifications('material', $mbsc-material-colors);
  @include mbsc-material-notifications('material-dark', $mbsc-material-dark-colors);
}



$mbsc-windows-accent: #0078d7 !default;
$mbsc-windows-background: #ffffff !default;
$mbsc-windows-text: #333333 !default;

$mbsc-windows-dark-accent: #0078d7 !default;
$mbsc-windows-dark-background: #1a1a1a !default;
$mbsc-windows-dark-text: #ffffff !default;

/* Base colors */
$mbsc-windows-primary: #3f97f6 !default;
$mbsc-windows-secondary: #90979e !default;
$mbsc-windows-success: #43be5f !default;
$mbsc-windows-danger: #f5504e !default;
$mbsc-windows-warning: #f8b042 !default;
$mbsc-windows-info: #5bb7c5 !default;
$mbsc-windows-light: #fff !default;
$mbsc-windows-dark: #47494a !default;

$mbsc-windows-error: #a4262c;

/* Form colors */

$mbsc-windows-form-background: $mbsc-form-background-light !default;
$mbsc-windows-dark-form-background: $mbsc-form-background-dark !default;
$mbsc-windows-form-text: $mbsc-form-text-light !default;
$mbsc-windows-dark-form-text: $mbsc-form-text-dark !default;
$mbsc-windows-form-accent: $mbsc-form-accent-light !default;
$mbsc-windows-dark-form-accent: $mbsc-form-accent-dark !default;
$mbsc-windows-form-error: $mbsc-form-error-light !default;
$mbsc-windows-dark-form-error: $mbsc-form-error-dark !default;

/* Calendar colors (will be used by eventcalendar, calendar, range) */

$mbsc-windows-calendar-background: $mbsc-calendar-background-light !default;
$mbsc-windows-calendar-text: $mbsc-calendar-text-light !default;
$mbsc-windows-calendar-accent: $mbsc-calendar-accent-light !default;
$mbsc-windows-calendar-border: $mbsc-calendar-border-light !default;
$mbsc-windows-calendar-mark: $mbsc-calendar-mark-light !default;
$mbsc-windows-calendar-event: $mbsc-calendar-event-light !default;

$mbsc-windows-dark-calendar-background: $mbsc-calendar-background-dark !default;
$mbsc-windows-dark-calendar-text: $mbsc-calendar-text-dark !default;
$mbsc-windows-dark-calendar-accent: $mbsc-calendar-accent-dark !default;
$mbsc-windows-dark-calendar-border: $mbsc-calendar-border-dark !default;
$mbsc-windows-dark-calendar-mark: $mbsc-calendar-mark-dark !default;
$mbsc-windows-dark-calendar-event: $mbsc-calendar-event-dark !default;

$mbsc-windows-colors: (
  // Colors map
  'background': $mbsc-windows-background,
  'text': $mbsc-windows-text,
  'accent': $mbsc-windows-accent,

  'calendar-background': $mbsc-windows-calendar-background,
  'calendar-text': $mbsc-windows-calendar-text,
  'calendar-accent': $mbsc-windows-calendar-accent,
  'calendar-border': $mbsc-windows-calendar-border,
  'calendar-mark': $mbsc-windows-calendar-mark,
  'calendar-event': $mbsc-windows-calendar-event,

  'form-background': $mbsc-windows-form-background,
  'form-text': $mbsc-windows-form-text,
  'form-accent': $mbsc-windows-form-accent,
  'form-error': $mbsc-windows-form-error
);

$mbsc-windows-dark-colors: (
  // Colors map
  'background': $mbsc-windows-dark-background,
  'text': $mbsc-windows-dark-text,
  'accent': $mbsc-windows-dark-accent,

  'calendar-background': $mbsc-windows-dark-calendar-background,
  'calendar-text': $mbsc-windows-dark-calendar-text,
  'calendar-accent': $mbsc-windows-dark-calendar-accent,
  'calendar-border': $mbsc-windows-dark-calendar-border,
  'calendar-mark': $mbsc-windows-dark-calendar-mark,
  'calendar-event': $mbsc-windows-dark-calendar-event,

  'form-background': $mbsc-windows-dark-form-background,
  'form-text': $mbsc-windows-dark-form-text,
  'form-accent': $mbsc-windows-dark-form-accent,
  'form-error': $mbsc-windows-dark-form-error
);

@function mbsc-windows-colors($params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  @return ();
}


@mixin mbsc-windows-notifications($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  .mbsc-#{$theme} {
    .mbsc-toast-background {
      background: $text;
      color: get-contrast-color($text);
    }

    &.mbsc-color-none .mbsc-snackbar-button.mbsc-button {
      color: lighten($accent, 10%);
    }
  }
}


@if ($mbsc-windows-theme and $mbsc-forms) {
  .mbsc-windows {
    &.mbsc-prompt-input.mbsc-font {
      margin: 1em 0 0 0;
    }

    &.mbsc-primary .mbsc-toast-background {
      background: $mbsc-windows-primary;
      color: get-contrast-color($mbsc-windows-primary);
    }

    &.mbsc-secondary .mbsc-toast-background {
      background: $mbsc-windows-secondary;
      color: get-contrast-color($mbsc-windows-secondary);
    }

    &.mbsc-success .mbsc-toast-background {
      background: $mbsc-windows-success;
      color: get-contrast-color($mbsc-windows-success);
    }

    &.mbsc-danger .mbsc-toast-background {
      background: $mbsc-windows-danger;
      color: get-contrast-color($mbsc-windows-danger);
    }

    &.mbsc-warning .mbsc-toast-background {
      background: $mbsc-windows-warning;
      color: get-contrast-color($mbsc-windows-warning);
    }

    &.mbsc-info .mbsc-toast-background {
      background: $mbsc-windows-info;
      color: get-contrast-color($mbsc-windows-info);
    }
  }

  @include mbsc-windows-notifications('windows', $mbsc-windows-colors);
  @include mbsc-windows-notifications('windows-dark', $mbsc-windows-dark-colors);
}


@if $mbsc-forms {
  /* Toast */

  .mbsc-toast.mbsc-font .mbsc-popup-body,
  .mbsc-toast.mbsc-font .mbsc-popup,
  .mbsc-snackbar.mbsc-font .mbsc-popup-body,
  .mbsc-snackbar.mbsc-font .mbsc-popup {
    background: none;
    box-shadow: none;
    border: 0;
    border-radius: 0;
    margin: 0;
    pointer-events: none;
  }

  .mbsc-toast.mbsc-font .mbsc-popup-content {
    text-align: center;
    padding-bottom: 4em;
  }

  .mbsc-toast-message {
    display: inline-block;
    min-width: 10em;
    max-width: 50em;
    padding: 0.857143em 2em;
    font-size: 0.875em;
    line-height: 1.428572;
  }

  /* Snackbar */

  .mbsc-snackbar-cont {
    align-items: center;
    min-width: 18em;
    max-width: 36em;
    margin: 0 auto;
    padding: 0.5em;
    pointer-events: auto;
  }

  .mbsc-snackbar-message {
    padding: 0.5em 1em;
    font-size: 0.875em; // 14px
    line-height: 1.571429em; // 22px
  }

  .mbsc-snackbar-button.mbsc-button.mbsc-font {
    margin: 0;
    color: #fff;
  }

  /* Alert, Confirm, Prompt*/

  .mbsc-alert-content {
    max-width: 20em;
  }

  .mbsc-alert-title {
    margin: 0 0 1em 0;
    padding: 0;
    font-size: 1.428572em;
    font-weight: bold;
  }

  .mbsc-alert-message {
    margin: 1em 0;
    padding: 0;
    font-size: 1em;
  }
}

// button component


$mbsc-ios-button-color: $mbsc-button-color-light !default;
$mbsc-ios-button-text: $mbsc-button-text-light !default;

$mbsc-ios-dark-button-color: $mbsc-button-color-dark !default;
$mbsc-ios-dark-button-text: $mbsc-button-text-dark !default;

$mbsc-ios-colors: map-merge(
  $mbsc-ios-colors,
  (
    'button-color': $mbsc-ios-button-color,
    'button-text': $mbsc-ios-button-text,
  )
);

$mbsc-ios-dark-colors: map-merge(
  $mbsc-ios-dark-colors,
  (
    'button-color': $mbsc-ios-dark-button-color,
    'button-text': $mbsc-ios-dark-button-text,
  )
);

@mixin mbsc-ios-button($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $button-background-param: map-get($params, 'button-color');
  $button-text-param: map-get($params, 'button-text');

  $white: #fff;
  $black: #000;
  $button-background: '';
  $background-contrast: '';

  @if (lightness($background) > 50%) {
    $button-background: lighten($background, 10%);
    $background-contrast: #000;
  } @else {
    $button-background: lighten($background, 23%);
    $background-contrast: #fff;
  }

  $button-background: if($button-background-param, $button-background-param, $button-background);
  $button-text: if($button-text-param, $button-text-param, $accent);

  $form-background: $background;
  // The $form-background is used for the active text color of outlined buttons.
  // Until we'll have the form component specify this, we'll default to the $background.
  // @if($button-background-param) {
  //   @if(lightness($button-background-param) > 50%) {
  //     $form-background: adjust-hue(darken(saturate($button-background-param, 19%), 12%), 240deg);
  //   }
  //   @else {
  //     $form-background: adjust-hue(lighten(desaturate($button-background-param, 19%), 10%), 240deg);
  //   }
  // }
  // @else {
  //   @if(lightness($background) > 50%) {
  //     $form-background: adjust-hue(darken(saturate($background, 19%), 2%), 240deg);
  //   }
  //   @else {
  //     $form-background: adjust-hue(lighten(desaturate($background, 19%), 10%), 240deg);
  //   }
  // }

  $form-selection: '';

  // Light button
  @if (lightness($accent) > 50%) {
    $form-selection: lighten(saturate($accent, 15%), 3%);
  }

  // Dark button
  @else {
    $form-selection: darken(desaturate($accent, 15%), 3%);
  }

  $form-selection: if($button-background-param, $button-background-param, $form-selection);
  $flat-color: if($button-background-param, $button-background-param, $accent);

  .mbsc-#{$theme} {
    &.mbsc-button-standard {
      background: $button-background;
      color: $button-text;
    }

    // &.mbsc-button:disabled {
    //   background: $disabled-background;
    //   color: $disabled-color;
    // }

    /* Flat buttons */
    &.mbsc-button-flat {
      color: $flat-color;
    }

    /* Outline buttons */
    &.mbsc-button-outline {
      border: 1px solid $form-selection;
      color: $form-selection;

      &.mbsc-active {
        background: $form-selection;
        color: $background;
      }
    }

    &.mbsc-button.mbsc-focus {
      background: rgba($background-contrast, 0.05);
      // box-shadow: 0 0 0 2px rgba($text, .3);
    }

    /* Predefined colors */
    &.mbsc-button-primary.mbsc-button-standard {
      background: $mbsc-ios-primary;
      color: $white;
    }

    &.mbsc-button-secondary.mbsc-button-standard {
      background: $mbsc-ios-secondary;
      color: $white;
    }

    &.mbsc-button-success.mbsc-button-standard {
      background: $mbsc-ios-success;
      color: $white;
    }

    &.mbsc-button-danger.mbsc-button-standard {
      background: $mbsc-ios-danger;
      color: $white;
    }

    &.mbsc-button-warning.mbsc-button-standard {
      background: $mbsc-ios-warning;
      color: $white;
    }

    &.mbsc-button-info.mbsc-button-standard {
      background: $mbsc-ios-info;
      color: $white;
    }

    &.mbsc-button-dark.mbsc-button-standard {
      background: $mbsc-ios-dark;
      color: $white;
    }

    &.mbsc-button-light.mbsc-button-standard {
      background: $mbsc-ios-light;
      color: $black;
    }

    &.mbsc-button-primary.mbsc-button-flat {
      color: $mbsc-ios-primary;
    }

    &.mbsc-button-secondary.mbsc-button-flat {
      color: $mbsc-ios-secondary;
    }

    &.mbsc-button-success.mbsc-button-flat {
      color: $mbsc-ios-success;
    }

    &.mbsc-button-danger.mbsc-button-flat {
      color: $mbsc-ios-danger;
    }

    &.mbsc-button-warning.mbsc-button-flat {
      color: $mbsc-ios-warning;
    }

    &.mbsc-button-info.mbsc-button-flat {
      color: $mbsc-ios-info;
    }

    &.mbsc-button-dark.mbsc-button-flat {
      color: $mbsc-ios-dark;
    }

    &.mbsc-button-light.mbsc-button-flat {
      color: darken($mbsc-ios-light, 20%);
    }

    &.mbsc-button-primary.mbsc-button-outline {
      border-color: $mbsc-ios-primary;
      color: $mbsc-ios-primary;

      &.mbsc-active {
        background: $mbsc-ios-primary;
        color: $mbsc-ios-light;
      }
    }

    &.mbsc-button-secondary.mbsc-button-outline {
      border-color: $mbsc-ios-secondary;
      color: $mbsc-ios-secondary;

      &.mbsc-active {
        background: $mbsc-ios-secondary;
        color: $mbsc-ios-light;
      }
    }

    &.mbsc-button-success.mbsc-button-outline {
      border-color: $mbsc-ios-success;
      color: $mbsc-ios-success;

      &.mbsc-active {
        background: $mbsc-ios-success;
        color: $mbsc-ios-light;
      }
    }

    &.mbsc-button-danger.mbsc-button-outline {
      border-color: $mbsc-ios-danger;
      color: $mbsc-ios-danger;

      &.mbsc-active {
        background: $mbsc-ios-danger;
        color: $mbsc-ios-light;
      }
    }

    &.mbsc-button-warning.mbsc-button-outline {
      border-color: $mbsc-ios-warning;
      color: $mbsc-ios-warning;

      &.mbsc-active {
        background: $mbsc-ios-warning;
        color: $mbsc-ios-light;
      }
    }

    &.mbsc-button-info.mbsc-button-outline {
      border-color: $mbsc-ios-info;
      color: $mbsc-ios-info;

      &.mbsc-active {
        background: $mbsc-ios-info;
        color: $mbsc-ios-light;
      }
    }

    &.mbsc-button-dark.mbsc-button-outline {
      border-color: $mbsc-ios-dark;
      color: $mbsc-ios-dark;

      &.mbsc-active {
        background: $mbsc-ios-dark;
        color: $mbsc-ios-light;
      }
    }

    &.mbsc-button-light.mbsc-button-outline {
      border-color: darken($mbsc-ios-light, 25%);
      color: darken($mbsc-ios-light, 25%);

      &.mbsc-active {
        background: darken($mbsc-ios-light, 25%);
        color: $mbsc-ios-light;
      }
    }
  }
}



$mbsc-material-button-color: $mbsc-button-color-light !default;
$mbsc-material-button-text: $mbsc-button-text-light !default;

$mbsc-material-dark-button-color: $mbsc-button-color-dark !default;
$mbsc-material-dark-button-text: $mbsc-button-text-dark !default;

$mbsc-material-colors: map-merge(
  $mbsc-material-colors,
  (
    'button-color': $mbsc-material-button-color,
    'button-text': $mbsc-material-button-text,
  )
);

$mbsc-material-dark-colors: map-merge(
  $mbsc-material-dark-colors,
  (
    'button-color': $mbsc-material-dark-button-color,
    'button-text': $mbsc-material-dark-button-text,
  )
);

@mixin mbsc-material-button($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $button-background-param: map-get($params, 'button-color');
  $button-text-param: map-get($params, 'button-text');

  $button-active: darken($background, 13%);
  $button-text: '';
  $button-background: '';

  @if (lightness($background) > 50%) {
    $button-text: darken($text, 36%);
    $button-background: darken($background, 19%);
  } @else {
    $button-text: lighten($text, 24%);
    $button-background: lighten($background, 17%);
  }

  $button-background: if($button-background-param, $button-background-param, $button-background);
  $button-text: if($button-text-param, $button-text-param, $button-text);
  $flatout-color: if($button-background-param, $button-background, $button-text);

  .mbsc-#{$theme} {
    &.mbsc-button-standard {
      background: $button-background;
      color: $button-text;
      box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);

      &.mbsc-hover {
        box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
      }

      &.mbsc-focus,
      &.mbsc-active {
        box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
      }
    }

    /* Flat buttons */
    &.mbsc-button-flat,
    &.mbsc-button-outline {
      color: $flatout-color;

      &.mbsc-hover,
      &.mbsc-active {
        background: rgba($flatout-color, 0.2);
      }

      &.mbsc-focus {
        background: rgba($flatout-color, 0.3);
      }
    }

    /* Outline buttons */
    &.mbsc-button-outline {
      border-color: $flatout-color;
    }

    /* Predefined colors */
    &.mbsc-button-primary.mbsc-button-standard {
      background: $mbsc-material-primary;
      color: $background;
    }

    &.mbsc-button-secondary.mbsc-button-standard {
      background: $mbsc-material-secondary;
      color: $background;
    }

    &.mbsc-button-success.mbsc-button-standard {
      background: $mbsc-material-success;
      color: $background;
    }

    &.mbsc-button-danger.mbsc-button-standard {
      background: $mbsc-material-danger;
      color: $background;
    }

    &.mbsc-button-warning.mbsc-button-standard {
      background: $mbsc-material-warning;
      color: $background;
    }

    &.mbsc-button-info.mbsc-button-standard {
      background: $mbsc-material-info;
      color: $background;
    }

    &.mbsc-button-dark.mbsc-button-standard {
      background: $mbsc-material-dark;
      color: $background;
    }

    &.mbsc-button-light.mbsc-button-standard {
      background: $mbsc-material-light;
      color: $text;
    }

    &.mbsc-button-primary.mbsc-button-flat {
      color: $mbsc-material-primary;

      &.mbsc-hover,
      &.mbsc-active {
        background: rgba($mbsc-material-primary, 0.2);
      }

      &.mbsc-focus {
        background: rgba($mbsc-material-primary, 0.3);
      }
    }

    &.mbsc-button-secondary.mbsc-button-flat {
      color: $mbsc-material-secondary;

      &.mbsc-hover,
      &.mbsc-active {
        background: rgba($mbsc-material-secondary, 0.2);
      }

      &.mbsc-focus {
        background: rgba($mbsc-material-secondary, 0.3);
      }
    }

    &.mbsc-button-success.mbsc-button-flat {
      color: $mbsc-material-success;

      &.mbsc-hover,
      &.mbsc-active {
        background: rgba($mbsc-material-success, 0.2);
      }

      &.mbsc-focus {
        background: rgba($mbsc-material-success, 0.3);
      }
    }

    &.mbsc-button-danger.mbsc-button-flat {
      color: $mbsc-material-danger;

      &.mbsc-hover,
      &.mbsc-active {
        background: rgba($mbsc-material-danger, 0.2);
      }

      &.mbsc-focus {
        background: rgba($mbsc-material-danger, 0.3);
      }
    }

    &.mbsc-button-warning.mbsc-button-flat {
      color: $mbsc-material-warning;

      &.mbsc-hover,
      &.mbsc-active {
        background: rgba($mbsc-material-warning, 0.2);
      }

      &.mbsc-focus {
        background: rgba($mbsc-material-warning, 0.3);
      }
    }

    &.mbsc-button-info.mbsc-button-flat {
      color: $mbsc-material-info;

      &.mbsc-hover,
      &.mbsc-active {
        background: rgba($mbsc-material-info, 0.2);
      }

      &.mbsc-focus {
        background: rgba($mbsc-material-info, 0.3);
      }
    }

    &.mbsc-button-dark.mbsc-button-flat {
      color: $mbsc-material-dark;

      &.mbsc-hover,
      &.mbsc-active {
        background: rgba($mbsc-material-dark, 0.2);
      }

      &.mbsc-focus {
        background: rgba($mbsc-material-dark, 0.3);
      }
    }

    &.mbsc-button-light.mbsc-button-flat {
      color: darken($mbsc-material-light, 20%);

      &.mbsc-hover,
      &.mbsc-active {
        background: rgba($mbsc-material-light, 0.2);
      }

      &.mbsc-focus {
        background: rgba($mbsc-material-light, 0.3);
      }
    }

    &.mbsc-button-primary.mbsc-button-outline {
      border-color: $mbsc-material-primary;
      color: $mbsc-material-primary;

      &.mbsc-hover,
      &.mbsc-active {
        background: rgba($mbsc-material-primary, 0.2);
      }

      &.mbsc-focus {
        background: rgba($mbsc-material-primary, 0.3);
      }
    }

    &.mbsc-button-secondary.mbsc-button-outline {
      border-color: $mbsc-material-secondary;
      color: $mbsc-material-secondary;

      &.mbsc-hover,
      &.mbsc-active {
        background: rgba($mbsc-material-secondary, 0.2);
      }

      &.mbsc-focus {
        background: rgba($mbsc-material-secondary, 0.3);
      }
    }

    &.mbsc-button-success.mbsc-button-outline {
      border-color: $mbsc-material-success;
      color: $mbsc-material-success;

      &.mbsc-hover,
      &.mbsc-active {
        background: rgba($mbsc-material-success, 0.2);
      }

      &.mbsc-focus {
        background: rgba($mbsc-material-success, 0.3);
      }
    }

    &.mbsc-button-danger.mbsc-button-outline {
      border-color: $mbsc-material-danger;
      color: $mbsc-material-danger;

      &.mbsc-hover,
      &.mbsc-active {
        background: rgba($mbsc-material-danger, 0.2);
      }

      &.mbsc-focus {
        background: rgba($mbsc-material-danger, 0.3);
      }
    }

    &.mbsc-button-warning.mbsc-button-outline {
      border-color: $mbsc-material-warning;
      color: $mbsc-material-warning;

      &.mbsc-hover,
      &.mbsc-active {
        background: rgba($mbsc-material-warning, 0.2);
      }

      &.mbsc-focus {
        background: rgba($mbsc-material-warning, 0.3);
      }
    }

    &.mbsc-button-info.mbsc-button-outline {
      border-color: $mbsc-material-info;
      color: $mbsc-material-info;

      &.mbsc-hover,
      &.mbsc-active {
        background: rgba($mbsc-material-info, 0.2);
      }

      &.mbsc-focus {
        background: rgba($mbsc-material-info, 0.3);
      }
    }

    &.mbsc-button-dark.mbsc-button-outline {
      border-color: $mbsc-material-dark;
      color: $mbsc-material-dark;

      &.mbsc-hover,
      &.mbsc-active {
        background: rgba($mbsc-material-dark, 0.2);
      }

      &.mbsc-focus {
        background: rgba($mbsc-material-dark, 0.3);
      }
    }

    &.mbsc-button-light.mbsc-button-outline {
      border-color: darken($mbsc-material-light, 20%);
      color: darken($mbsc-material-light, 20%);

      &.mbsc-hover,
      &.mbsc-active {
        background: rgba($mbsc-material-light, 0.2);
      }

      &.mbsc-focus {
        background: rgba($mbsc-material-light, 0.3);
      }
    }
  }
}



$mbsc-windows-button-color: $mbsc-button-color-light !default;
$mbsc-windows-button-text: $mbsc-button-text-light !default;

$mbsc-windows-dark-button-color: $mbsc-button-color-dark !default;
$mbsc-windows-dark-button-text: $mbsc-button-text-dark !default;

$mbsc-windows-colors: map-merge(
  $mbsc-windows-colors,
  (
    'button-color': $mbsc-windows-button-color,
    'button-text': $mbsc-windows-button-text,
  )
);

$mbsc-windows-dark-colors: map-merge(
  $mbsc-windows-dark-colors,
  (
    'button-color': $mbsc-windows-dark-button-color,
    'button-text': $mbsc-windows-dark-button-text,
  )
);

@mixin mbsc-windows-button($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $button-background-param: map-get($params, 'button-color');
  $button-text-param: map-get($params, 'button-text');

  $button-text: if($button-text-param, $button-text-param, $text);

  $button-bg: '';
  $hover: '';
  $button-border: '';
  $button-border-hover: '';
  $dark-text: darken($mbsc-windows-dark, 30%);

  @if (lightness($background) > 50%) {
    $button-bg: darken($background, 20%);
    $hover: lighten($button-text, 70%);
    $button-border: darken($background, 40%);
    $button-border-hover: darken($background, 50%);
  } @else {
    $button-bg: lighten($background, 15%);
    $hover: darken($button-text, 70%);
    $button-border: lighten($background, 35%);
    $button-border-hover: lighten($background, 45%);
  }

  $button-bg: if($button-background-param, $button-background-param, $button-bg);

  @if ($button-background-param) {
    $button-border: $button-background-param;

    @if (lightness($button-background-param) > 50%) {
      $button-border-hover: darken($button-border, 10%);
    } @else {
      $button-border-hover: lighten($button-border, 10%);
    }
  }

  $active-base: if($button-background-param, $button-background-param, $background);
  $button-active: '';

  @if (lightness($active-base) > 50%) {
    $button-active: lighten($button-text, 28%);
  } @else {
    $button-active: darken($button-text, 33%);
  }

  $flat-color: if($button-background-param, $button-bg, $button-text);
  $flat-active: if($button-background-param, $button-text, $button-bg);

  .mbsc-#{$theme} {
    &.mbsc-button-standard {
      background: $button-bg;
      border-color: $button-bg;
      color: $button-text;

      &.mbsc-hover {
        border-color: $button-active;
      }

      &.mbsc-active {
        border-color: $button-active;
        background: $button-active;
      }
    }

    /* Flat buttons */
    &.mbsc-button-flat {
      color: $flat-color;

      &.mbsc-active {
        background: lighten($button-bg, 15%);
        border-color: lighten($button-bg, 15%);
        color: $button-text;
      }
    }

    /* Outline buttons */
    &.mbsc-button-outline {
      border: 0.125em solid $button-border;
      color: $button-border;

      &.mbsc-hover {
        border-color: $button-border-hover;
      }

      &.mbsc-active {
        border-color: $button-border-hover;
        background: $button-border-hover;
        color: get-contrast-color($button-border-hover);
      }
    }

    &.mbsc-button.mbsc-focus {
      // background: rgba($accent, .3);
      box-shadow: 0 0 0 1px $button-text;
    }

    /* Predefined colors */
    &.mbsc-button-primary.mbsc-button-standard {
      background: $mbsc-windows-primary;
      border-color: $mbsc-windows-primary;
      color: get-contrast-color($mbsc-windows-primary);
    }

    &.mbsc-button-secondary.mbsc-button-standard {
      background: $mbsc-windows-secondary;
      border-color: $mbsc-windows-secondary;
      color: get-contrast-color($mbsc-windows-secondary);
    }

    &.mbsc-button-success.mbsc-button-standard {
      background: $mbsc-windows-success;
      border-color: $mbsc-windows-success;
      color: get-contrast-color($mbsc-windows-success);
    }

    &.mbsc-button-danger.mbsc-button-standard {
      background: $mbsc-windows-danger;
      border-color: $mbsc-windows-danger;
      color: get-contrast-color($mbsc-windows-danger);
    }

    &.mbsc-button-warning.mbsc-button-standard {
      background: $mbsc-windows-warning;
      border-color: $mbsc-windows-warning;
      color: get-contrast-color($mbsc-windows-warning);
    }

    &.mbsc-button-info.mbsc-button-standard {
      background: $mbsc-windows-info;
      border-color: $mbsc-windows-info;
      color: get-contrast-color($mbsc-windows-info);
    }

    &.mbsc-button-dark.mbsc-button-standard {
      background: $mbsc-windows-dark;
      border-color: $mbsc-windows-dark;
      color: get-contrast-color($mbsc-windows-dark);
    }

    &.mbsc-button-light.mbsc-button-standard {
      background: $mbsc-windows-light;
      border-color: $mbsc-windows-light;
      color: $dark-text;
    }

    &.mbsc-button-primary.mbsc-button-flat {
      color: $mbsc-windows-primary;

      &.mbsc-active {
        background: lighten($mbsc-windows-primary, 15%);
        border-color: lighten($mbsc-windows-primary, 15%);
      }
    }

    &.mbsc-button-secondary.mbsc-button-flat {
      color: $mbsc-windows-secondary;

      &.mbsc-active {
        background: lighten($mbsc-windows-secondary, 15%);
        border-color: lighten($mbsc-windows-secondary, 15%);
      }
    }

    &.mbsc-button-success.mbsc-button-flat {
      color: $mbsc-windows-success;

      &.mbsc-active {
        background: lighten($mbsc-windows-success, 15%);
        border-color: lighten($mbsc-windows-success, 15%);
      }
    }

    &.mbsc-button-danger.mbsc-button-flat {
      color: $mbsc-windows-danger;

      &.mbsc-active {
        background: lighten($mbsc-windows-danger, 15%);
        border-color: lighten($mbsc-windows-danger, 15%);
      }
    }

    &.mbsc-button-warning.mbsc-button-flat {
      color: $mbsc-windows-warning;

      &.mbsc-active {
        background: lighten($mbsc-windows-warning, 15%);
        border-color: lighten($mbsc-windows-warning, 15%);
      }
    }

    &.mbsc-button-info.mbsc-button-flat {
      color: $mbsc-windows-info;

      &.mbsc-active {
        background: lighten($mbsc-windows-info, 15%);
        border-color: lighten($mbsc-windows-info, 15%);
      }
    }

    &.mbsc-button-dark.mbsc-button-flat {
      color: $mbsc-windows-dark;

      &.mbsc-active {
        background: lighten($mbsc-windows-dark, 15%);
        border-color: lighten($mbsc-windows-dark, 15%);
      }
    }

    &.mbsc-button-light.mbsc-button-flat {
      color: darken($mbsc-windows-light, 20%);

      &.mbsc-active {
        background: lighten($mbsc-windows-light, 15%);
        border-color: lighten($mbsc-windows-light, 15%);
      }
    }

    &.mbsc-button-primary.mbsc-button-outline {
      border-color: $mbsc-windows-primary;
      color: $mbsc-windows-primary;
    }

    &.mbsc-button-secondary.mbsc-button-outline {
      border-color: $mbsc-windows-secondary;
      color: $mbsc-windows-secondary;
    }

    &.mbsc-button-success.mbsc-button-outline {
      border-color: $mbsc-windows-success;
      color: $mbsc-windows-success;
    }

    &.mbsc-button-danger.mbsc-button-outline {
      border-color: $mbsc-windows-danger;
      color: $mbsc-windows-danger;
    }

    &.mbsc-button-warning.mbsc-button-outline {
      border-color: $mbsc-windows-warning;
      color: $mbsc-windows-warning;
    }

    &.mbsc-button-info.mbsc-button-outline {
      border-color: $mbsc-windows-info;
      color: $mbsc-windows-info;
    }

    &.mbsc-button-dark.mbsc-button-outline {
      border-color: $mbsc-windows-dark;
      color: $mbsc-windows-dark;
    }

    &.mbsc-button-light.mbsc-button-outline {
      border-color: darken($mbsc-windows-light, 20%);
      color: darken($mbsc-windows-light, 20%);
    }

    /* Predefined colors - hover for standard and outline buttons */
    &.mbsc-button-primary.mbsc-button-standard,
    &.mbsc-button-primary.mbsc-button-outline {
      &.mbsc-hover {
        border-color: darken($mbsc-windows-primary, 20%);
      }

      &.mbsc-active {
        background: darken($mbsc-windows-primary, 20%);
        color: $mbsc-windows-light;
      }
    }

    &.mbsc-button-secondary.mbsc-button-standard,
    &.mbsc-button-secondary.mbsc-button-outline {
      &.mbsc-hover {
        border-color: darken($mbsc-windows-secondary, 20%);
      }

      &.mbsc-active {
        background: darken($mbsc-windows-secondary, 20%);
        color: $mbsc-windows-light;
      }
    }

    &.mbsc-button-success.mbsc-button-standard,
    &.mbsc-button-success.mbsc-button-outline {
      &.mbsc-hover {
        border-color: darken($mbsc-windows-success, 20%);
      }

      &.mbsc-active {
        background: darken($mbsc-windows-success, 20%);
        color: $mbsc-windows-light;
      }
    }

    &.mbsc-button-danger.mbsc-button-standard,
    &.mbsc-button-danger.mbsc-button-outline {
      &.mbsc-hover {
        border-color: darken($mbsc-windows-danger, 20%);
      }

      &.mbsc-active {
        background: darken($mbsc-windows-danger, 20%);
        color: $mbsc-windows-light;
      }
    }

    &.mbsc-button-warning.mbsc-button-standard,
    &.mbsc-button-warning.mbsc-button-outline {
      &.mbsc-hover {
        border-color: darken($mbsc-windows-warning, 20%);
      }

      &.mbsc-active {
        background: darken($mbsc-windows-warning, 20%);
        color: $mbsc-windows-light;
      }
    }

    &.mbsc-button-info.mbsc-button-standard,
    &.mbsc-button-info.mbsc-button-outline {
      &.mbsc-hover {
        border-color: darken($mbsc-windows-info, 20%);
      }

      &.mbsc-active {
        background: darken($mbsc-windows-info, 20%);
        color: $mbsc-windows-light;
      }
    }

    &.mbsc-button-dark.mbsc-button-standard,
    &.mbsc-button-dark.mbsc-button-outline {
      &.mbsc-hover {
        border-color: darken($mbsc-windows-dark, 30%);
      }

      &.mbsc-active {
        background: darken($mbsc-windows-dark, 30%);
        color: $mbsc-windows-light;
      }
    }

    &.mbsc-button-light.mbsc-button-standard,
    &.mbsc-button-light.mbsc-button-outline {
      &.mbsc-hover {
        border-color: darken($mbsc-windows-light, 40%);
      }

      &.mbsc-active {
        background: darken($mbsc-windows-light, 40%);
        color: $mbsc-windows-dark;
      }
    }
  }
}

// calendar component


@mixin mbsc-ios-calendar($theme, $params) {
}



@mixin mbsc-material-calendar($theme, $params) {
}



@mixin mbsc-windows-calendar($theme, $params) {
}

// calendar-view component


@mixin mbsc-ios-calendar-view($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $background-param: map-get($params, 'calendar-background');
  $text-param: map-get($params, 'calendar-text');
  $accent-param: map-get($params, 'calendar-accent');
  $border-param: map-get($params, 'calendar-border');
  $mark-param: map-get($params, 'calendar-mark');
  $event-param: map-get($params, 'calendar-event');

  $background: if($background-param, $background-param, $background);
  $text: if($text-param, $text-param, if($background-param, get-contrast-color($background), $text));
  $accent: if($accent-param, $accent-param, $accent);
  $event: if($event-param, $event-param, #5ac8fa);

  $background-alt: '';
  $border-color: '';
  $popup-background: '';
  $popup-cell: '';
  $hover: '';

  @if (lightness($background) > 50%) {
    $background-alt: lighten($background, 3%);
    $border-color: darken($background, 17%);
    $popup-background: $background-alt;
    $popup-cell: $background-alt;
    $hover: darken($background, 10%);
  } @else {
    $background-alt: $background;
    $border-color: lighten($background, 20%);
    $popup-background: lighten($background, 16%);
    $popup-cell: lighten($background, 11%);
    $hover: lighten($background, 17%);
  }

  $highlight: '';
  $highlight-contrast: '';

  @if (lightness($background) > 50%) {
    $highlight: lighten(desaturate($accent, 14%), 39%);
    $highlight-contrast: #000;
  } @else {
    $highlight: darken(desaturate($accent, 14%), 39%);
    $highlight-contrast: #fff;
  }

  $border-color: if($border-param, $border-param, $border-color);
  $mark: if($mark-param, $mark-param, $border-color);

  .mbsc-#{$theme} {
    &.mbsc-calendar {
      background: $background-alt;
      color: $text;
    }

    &.mbsc-calendar-wrapper {
      border-color: $border-color;
    }

    &.mbsc-calendar-header {
      border-color: $border-color;
    }

    &.mbsc-calendar-button.mbsc-button {
      color: $accent;
    }

    &.mbsc-calendar-cell {
      background: $background-alt;
      border-color: $border-color;
      color: $text;
    }

    &.mbsc-calendar-day:after {
      border-color: $border-color;
    }

    &.mbsc-calendar-week-nr,
    &.mbsc-calendar-today {
      color: $accent;
    }

    &.mbsc-hover .mbsc-calendar-cell-text {
      background-color: rgba($accent, 0.3);
    }

    /* range highlight and selection */

    &.mbsc-range-day::after {
      background-color: $highlight;
    }

    &.mbsc-range-day .mbsc-calendar-cell-text {
      color: $highlight-contrast;
    }

    &.mbsc-range-hover::before {
      border-color: $hover;
    }

    &.mbsc-selected .mbsc-calendar-cell-text {
      border-color: $accent;
      background: $accent;
      color: get-contrast-color($accent);
    }

    &.mbsc-focus .mbsc-calendar-cell-text {
      box-shadow: 0 0 0 2px rgba($text, 0.5);
    }

    &.mbsc-focus .mbsc-calendar-day-text {
      box-shadow: none;
      border-color: rgba($text, 0.5);
    }

    &.mbsc-calendar-mark {
      background: $mark;
    }

    &.mbsc-calendar-label {
      color: $event;
    }

    &.mbsc-calendar-label-text {
      color: get-contrast-color($background);
    }

    &.mbsc-calendar-label-active,
    &.mbsc-calendar-label-dragging {
      .mbsc-calendar-label-inner {
        color: get-contrast-color($event);
      }

      .mbsc-calendar-label-text {
        color: inherit;
      }
    }

    &.mbsc-calendar-text-more .mbsc-calendar-label-text {
      color: $text;
    }

    /* Picker */
    &.mbsc-calendar-popup {
      .mbsc-popup-arrow,
      .mbsc-popup-body {
        background: $popup-background;
      }

      .mbsc-calendar-cell {
        background: $popup-cell;
      }
    }

    /* Multi month grid view */

    &.mbsc-calendar-grid {
      border-color: $border-color;
    }

    &.mbsc-calendar-month-title {
      color: $accent;
    }
  }
}



@mixin mbsc-material-calendar-view($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $background-param: map-get($params, 'calendar-background');
  $text-param: map-get($params, 'calendar-text');
  $accent-param: map-get($params, 'calendar-accent');
  $border-param: map-get($params, 'calendar-border');
  $mark-param: map-get($params, 'calendar-mark');
  $event-param: map-get($params, 'calendar-event');

  $background: if($background-param, $background-param, $background);
  $text: if($text-param, $text-param, if($background-param, get-contrast-color($background-param), $text));
  $accent: if($accent-param, $accent-param, $accent);
  $mark: if($mark-param, $mark-param, $accent);
  $event: if($event-param, $event-param, $accent);

  $border: '';
  $cell-hover: '';
  $picker-background: '';
  $hover: '';

  // Light background
  @if (lightness($background) > 50%) {
    $border: darken($background, 19%);
    $cell-hover: #000;
    $picker-background: $background;
    $hover: darken($background, 10%);
  }

  // Dark background
  @else {
    $border: lighten($background, 17%);
    $cell-hover: #fff;
    $picker-background: lighten($background, 19%);
    $hover: lighten($background, 10%);
  }

  $border: if($border-param, $border-param, $border);

  .mbsc-#{$theme} {
    &.mbsc-calendar-wrapper:after {
      box-shadow: inset 0 0.5em 0.25em -0.5em rgba($text, 0.5);
    }

    &.mbsc-calendar-button.mbsc-button {
      color: $text;
    }

    &.mbsc-calendar-slide {
      background: $background;
    }

    &.mbsc-calendar-picker-slide {
      background: $picker-background;
    }

    &.mbsc-calendar-week-day {
      color: rgba($text, 0.7);
    }

    &.mbsc-calendar-cell-text {
      color: $text;
    }

    &.mbsc-calendar-week-nr,
    &.mbsc-calendar-today {
      color: $accent;
    }

    &.mbsc-focus .mbsc-calendar-cell-text {
      box-shadow: 0 0 0 2px rgba($text, 0.7);
      // border-color: rgba($text, .7);
    }

    &.mbsc-hover .mbsc-calendar-cell-text {
      background: rgba($cell-hover, 0.1);
    }

    /* range highlight and selection */

    &.mbsc-range-hover::before {
      border-color: $hover;
    }

    &.mbsc-range-day::after {
      background-color: rgba($accent, 0.25);
    }

    &.mbsc-selected .mbsc-calendar-cell-text {
      background: $accent;
      border-color: $accent;
      color: $background;
    }

    /* Marks */
    &.mbsc-calendar-mark {
      background: $mark;
    }

    &.mbsc-calendar-label {
      color: $event;
    }

    &.mbsc-calendar-label-inner {
      color: get-contrast-color($event);
    }

    &.mbsc-calendar-label.mbsc-calendar-label-active,
    &.mbsc-calendar-label.mbsc-calendar-label-dragging {
      box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.14), 0 1px 8px 0 rgba(0, 0, 0, 0.12), 0 1px 3px -1px rgba(0, 0, 0, 0.2);
    }

    .mbsc-calendar-label.mbsc-calendar-label-active .mbsc-calendar-label-background {
      box-shadow: inset 0 0 0 1px rgba(#fff, 0.5);
    }

    &.mbsc-calendar-text-more {
      box-shadow: none;
    }

    &.mbsc-calendar-text-more .mbsc-calendar-label-text {
      color: $text;
    }

    /* Picker */
    &.mbsc-calendar-popup {
      .mbsc-popup-arrow,
      .mbsc-popup-body {
        background: $picker-background;
      }
    }

    /* Desktop style */
    &.mbsc-calendar-height-md {
      .mbsc-calendar-week-day,
      .mbsc-calendar-day,
      .mbsc-calendar-day:after {
        border-color: $border;
      }
    }

    /* Multi month grid view */

    &.mbsc-calendar-month-title {
      color: $accent;
    }
  }
}



@mixin mbsc-windows-calendar-view($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $background-param: map-get($params, 'calendar-background');
  $text-param: map-get($params, 'calendar-text');
  $accent-param: map-get($params, 'calendar-accent');
  $border-param: map-get($params, 'calendar-border');
  $mark-param: map-get($params, 'calendar-mark');
  $event-param: map-get($params, 'calendar-event');

  $background: if($background-param, $background-param, $background);
  $text: if($text-param, $text-param, if($background-param, get-contrast-color($background-param), $text));
  $accent: if($accent-param, $accent-param, $accent);
  $mark: if($mark-param, $mark-param, rgba($text, 0.5));
  $event: if($event-param, $event-param, $accent);

  $alt-text: '';
  $border: '';
  $button-bg: '';
  $hover: '';

  @if (lightness($background) > 50%) {
    $alt-text: lighten($text, 20%);
    $border: darken($background, 10%);
    $button-bg: darken($background, 20%);
    $hover: rgba($text, 0.1);
  } @else {
    $alt-text: darken($text, 20%);
    $border: lighten($background, 10%);
    $button-bg: lighten($background, 15%);
    $hover: rgba($text, 0.22);
  }

  $border: if($border-param, $border-param, $border);

  .mbsc-#{$theme} {
    &.mbsc-calendar {
      color: $text;
    }

    &.mbsc-calendar-button.mbsc-button {
      color: $text;

      &.mbsc-active {
        background: lighten($button-bg, 15%);
        border-color: lighten($button-bg, 15%);
      }

      &.mbsc-focus {
        box-shadow: 0 0 0 1px $text;
      }
    }

    &.mbsc-calendar-wrapper-fixed {
      border-bottom: 1px solid $border;
    }

    &.mbsc-calendar-slide {
      background: $background;
      color: $text;
    }

    &.mbsc-calendar-week-day {
      color: $alt-text;
    }

    &.mbsc-calendar-week-nr,
    &.mbsc-calendar-today {
      color: $accent;
    }

    /* range highlight and selection */

    &.mbsc-range-day {
      border-left-color: rgba($accent, 0.25);
    }

    &.mbsc-range-day-start.mbsc-ltr,
    &.mbsc-range-day-end.mbsc-rtl {
      border-left-color: transparent;
    }

    &.mbsc-range-hover .mbsc-calendar-cell-inner {
      border-top-color: $hover;
      border-bottom-color: $hover;
      border-style: dashed;
    }

    // &.mbsc-range-hover-start .mbsc-calendar-cell-inner {
    //   border-left-color: $hover;
    // }

    // &.mbsc-range-hover-end .mbsc-calendar-cell-inner {
    //   border-right-color: $hover;
    // }

    // &.mbsc-selected .mbsc-calendar-cell-inner {
    //   border-color: transparent;
    // }

    // &.mbsc-range-day-start,
    // &.mbsc-range-day-end {
    //   border-left-color: $accent;
    //   border-right-color: $accent;
    // }

    // &.mbsc-range-day-start.mbsc-range-day-end {
    //   border-left-color: transparent;
    // }

    &.mbsc-selected,
    &.mbsc-range-day {
      .mbsc-calendar-cell-inner {
        background: rgba($accent, 0.25);
      }
    }

    &.mbsc-selected.mbsc-range-day {
      .mbsc-calendar-cell-inner {
        background-color: $accent;
      }

      .mbsc-calendar-cell-text {
        color: get-contrast-color($accent);
      }
    }

    &.mbsc-calendar-day-colors.mbsc-selected:after {
      border: 2px solid $accent;
    }

    &.mbsc-calendar-cell.mbsc-focus:after {
      border: 1px solid $text;
    }

    &.mbsc-calendar-cell.mbsc-hover:after {
      background: $hover;
    }

    /* Marks */
    &.mbsc-calendar-mark {
      background: $mark;
    }

    &.mbsc-calendar-label {
      color: $event;
    }

    &.mbsc-calendar-label-inner {
      color: get-contrast-color($event);
    }

    &.mbsc-calendar-label.mbsc-calendar-label-active {
      outline: 1px solid $text;
    }

    &.mbsc-calendar-text-more .mbsc-calendar-label-text {
      color: $text;
    }

    &.mbsc-calendar-height-md {
      .mbsc-calendar-week-day {
        border-color: $border;
      }
    }

    &.mbsc-calendar-width-md {
      .mbsc-calendar-day,
      .mbsc-calendar-day:after {
        border-color: $border;
      }
    }

    /* Picker */
    &.mbsc-calendar-popup {
      .mbsc-popup-arrow,
      .mbsc-popup {
        background: $background;
        border-color: $border;
      }
    }

    /* Multi month grid view */

    &.mbsc-calendar-month-title {
      color: $accent;
    }
  }
}

// checkbox component


@mixin mbsc-ios-checkbox($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $bg-param: map-get($params, 'form-background');
  $acc-param: map-get($params, 'form-accent');

  $background: if($bg-param, $bg-param, $background);
  $accent: if($acc-param, $acc-param, $accent);

  $form-selection: '';

  @if (lightness($accent) > 50%) {
    $form-selection: lighten(saturate($accent, 15%), 3%);
  } @else {
    $form-selection: darken(desaturate($accent, 15%), 3%);
  }

  .mbsc-#{$theme} {
    &.mbsc-checkbox-box {
      color: $form-selection;
    }
  }
}



@mixin mbsc-material-checkbox($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $bg-param: map-get($params, 'form-background');
  $text-param: map-get($params, 'form-text');
  $acc-param: map-get($params, 'form-accent');

  $background: if($bg-param, $bg-param, $background);
  $text: if($text-param, $text-param, $text);
  $accent: if($acc-param, $acc-param, $accent);

  $checkbox: '';

  @if (lightness($background) > 50%) {
    $checkbox: lighten($background, 7%);
  } @else {
    $checkbox: $background;
  }

  .mbsc-#{$theme} {
    &.mbsc-checkbox-box {
      color: $accent;
      border-color: $text;
    }

    &.mbsc-checkbox-box:after {
      border-color: $checkbox;
    }
  }
}



@mixin mbsc-windows-checkbox($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $bg-param: map-get($params, 'form-background');
  $text-param: map-get($params, 'form-text');
  $acc-param: map-get($params, 'form-accent');

  $background: if($bg-param, $bg-param, $background);
  $text: if($text-param, $text-param, $text);
  $accent: if($acc-param, $acc-param, $accent);

  $checkbox-border: '';
  $input-hover: '';

  @if (lightness($background) > 50%) {
    $checkbox-border: desaturate(lighten($accent, 52%), 24%);
    $input-hover: darken($background, 55%);
  } @else {
    $checkbox-border: saturate(darken($accent, 52%), 24%);
    $input-hover: lighten($background, 55%);
  }

  .mbsc-#{$theme} {
    &.mbsc-checkbox-box {
      color: $accent;
      border-color: $text;
    }

    &.mbsc-checkbox-box:after {
      border-color: $checkbox-border;
    }

    &.mbsc-checkbox-box:before {
      background: $input-hover;
    }

    &.mbsc-checkbox-box.mbsc-active {
      border-color: $input-hover;
      background: $input-hover;
    }
  }
}

// datepicker component


@mixin mbsc-ios-datepicker($theme, $params) {
  $background: map-get($params, 'background');
  $accent: map-get($params, 'accent');
  $text: map-get($params, 'text');

  $background-alt: '';
  $background-top-bottom: '';
  $border-color: '';
  $buttons-background: '';
  $clear-hover: '';

  @if (lightness($background) > 50%) {
    $background-alt: lighten($background, 3%);
    $background-top-bottom: adjust-hue(darken(saturate($background, 12%), 13%), 216deg);
    $border-color: darken($background, 17%);
    $clear-hover: darken($border-color, 10%);
    $buttons-background: darken($background, 3%);
  } @else {
    $background-alt: lighten($background, 11%);
    $background-top-bottom: lighten($background, 11%);
    $border-color: lighten($background, 20%);
    $clear-hover: lighten($border-color, 10%);
    $buttons-background: lighten($background, 8%);
  }

  .mbsc-#{$theme} {
    &.mbsc-picker {
      .mbsc-popup-arrow,
      .mbsc-popup-body {
        background: $background-alt;
      }
    }

    &.mbsc-picker-header {
      border-color: $border-color;
    }

    &.mbsc-datepicker {
      .mbsc-calendar,
      .mbsc-calendar-slide,
      .mbsc-calendar-cell {
        background: $background-alt;
      }
    }

    &.mbsc-datepicker-top.mbsc-datepicker-control-date,
    &.mbsc-datepicker-bottom.mbsc-datepicker-control-date {
      background: $background-top-bottom;
    }

    &.mbsc-datepicker-inline {
      background: $background-alt;
      border-color: $border-color;
      color: $text;
    }

    /* Range Control */

    &.mbsc-range-control-value.active {
      color: $accent;
    }

    &.mbsc-range-control-text-empty,
    &.mbsc-range-control-text-empty.active {
      color: rgba($text, 0.4);
    }

    &.mbsc-range-label-clear {
      color: $border-color;

      &:hover {
        color: $clear-hover;
      }
    }
  }
}



@mixin mbsc-material-datepicker($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $ctrl-background: '';
  $clear-icon: '';
  $clear-icon-hover: '';

  @if (lightness($background) > 50%) {
    $ctrl-background: darken($text, 36%);
    $clear-icon: lighten($text, 50%);
    $clear-icon-hover: darken($clear-icon, 20%);
  } @else {
    $ctrl-background: lighten($text, 24%);
    $clear-icon: darken($text, 50%);
    $clear-icon-hover: lighten($clear-icon, 20%);
  }

  $background-alt: '';

  // Light background
  @if (lightness($background) > 50%) {
    $background-alt: $background;
  }

  // Dark background
  @else {
    $background-alt: lighten($background, 19%);
  }

  .mbsc-#{$theme} {
    &.mbsc-datepicker .mbsc-calendar-slide {
      background: $background-alt;
    }

    &.mbsc-datepicker-inline {
      background: $background;
      color: $text;
    }

    &.mbsc-datepicker-inline .mbsc-calendar-slide {
      background: $background;
    }

    /* Range Controls */

    &.mbsc-range-control-wrapper {
      border-bottom: 1px solid rgba($ctrl-background, 0.2);
    }

    &.mbsc-range-control-wrapper &.mbsc-segmented-button {
      &.mbsc-selected {
        border-bottom-color: $accent;
      }
    }

    &.mbsc-range-control-value,
    &.mbsc-range-control-label {
      &.active {
        color: $accent;
      }
    }

    &.mbsc-range-control-text-empty,
    &.mbsc-range-control-text-empty.active {
      color: rgba($text, 0.4);
    }

    &.mbsc-range-label-clear {
      color: $clear-icon;

      &:hover {
        color: $clear-icon-hover;
      }
    }
  }
}



@mixin mbsc-windows-datepicker($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $button-bg: '';

  @if (lightness($background) > 50%) {
    $button-bg: darken($background, 20%);
  } @else {
    $button-bg: lighten($background, 15%);
  }

  $border: '';
  $hover: '';

  @if (lightness($background) > 50%) {
    $border: darken($background, 10%);
    $hover: lighten($text, 70%);
  } @else {
    $border: lighten($background, 10%);
    $hover: darken($text, 70%);
  }

  .mbsc-#{$theme} {
    &.mbsc-datepicker-inline {
      background: $background;
      color: $text;
    }

    &.mbsc-picker-header {
      border-color: $border;
    }

    &.mbsc-datepicker-tab {
      border-color: $border;
    }

    &.mbsc-range-control-wrapper {
      border-bottom: 1px solid $border;
    }

    &.mbsc-range-control-wrapper &.mbsc-segmented-button {
      &.mbsc-selected {
        border-bottom-color: $accent;
      }

      &.mbsc-focus {
        box-shadow: 0 0 0 1px $text inset;
      }
    }

    &.mbsc-range-control-value,
    &.mbsc-range-control-label {
      &.active {
        color: $accent;
      }
    }

    &.mbsc-range-control-text-empty,
    &.mbsc-range-control-text-empty.active {
      color: rgba($text, 0.4);
    }

    &.mbsc-range-label-clear {
      color: rgba($text, 0.6);

      &:hover {
        color: $text;
      }
    }
  }
}

// datetime component


@mixin mbsc-ios-datetime($theme, $params) {
}



@mixin mbsc-material-datetime($theme, $params) {
}



@mixin mbsc-windows-datetime($theme, $params) {
}

// eventcalendar component


@mixin mbsc-ios-eventcalendar($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $background-param: map-get($params, 'calendar-background');
  $text-param: map-get($params, 'calendar-text');
  $accent-param: map-get($params, 'calendar-accent');
  $border-param: map-get($params, 'calendar-border');
  $event-param: map-get($params, 'calendar-event');

  $background: if($background-param, $background-param, $background);
  $text: if($text-param, $text-param, if($background-param, get-contrast-color($background), $text));
  $accent: if($accent-param, $accent-param, $accent);
  $event: if($event-param, $event-param, #5ac8fa);

  $is-dark: false;
  $background-alt: '';
  $background-contrast: '';
  $background-header: '';
  $border-color: '';
  $title-color: '';
  $schedule-time-color: '';
  $cell-disabled-bg: '';
  $cell-disabled-color: '';

  // Light background
  @if (lightness($background) > 50%) {
    $background-alt: lighten($background, 3%);
    $background-contrast: #000;
    $background-header: $background;
    $border-color: darken($background, 17%);
    $schedule-time-color: lighten($mbsc-ios-text, 54.9);
    $title-color: adjust-hue(lighten(saturate($text, 2%), 44%), 240deg);
    $cell-disabled-bg: darken($background-alt, 6.27);
    $cell-disabled-color: darken($cell-disabled-bg, 40.39);
  }

  // Dark background
  @else {
    $is-dark: true;
    $background-alt: $background;
    $background-contrast: #fff;
    $background-header: lighten($background, 11%);
    $border-color: lighten($background, 20%);
    $schedule-time-color: $mbsc-ios-dark-text;
    $title-color: adjust-hue(darken(desaturate($text, 2%), 44%), 240deg);
    $cell-disabled-bg: lighten($background-alt, 10);
    $cell-disabled-color: lighten($cell-disabled-bg, 37.25);
  }

  $border-color: if($border-param, $border-param, $border-color);

  .mbsc-#{$theme} {
    /* Calendar view */
    &.mbsc-eventcalendar {
      .mbsc-calendar-header,
      .mbsc-calendar-week-days {
        background: $background-header;
      }

      .mbsc-calendar-day.mbsc-disabled {
        background: $cell-disabled-bg;
      }
    }

    /* Agenda view */

    &.mbsc-event-list-empty {
      color: $title-color;
    }

    &.mbsc-event-day.mbsc-list-header {
      background: $background-alt;
      border-color: $border-color;
      color: $text;
    }

    &.mbsc-event.mbsc-list-item {
      background: $background-alt;
      color: $text;
    }

    &.mbsc-event.mbsc-list-item:before,
    &.mbsc-event.mbsc-list-item:after {
      border-color: $border-color;
    }

    &.mbsc-event.mbsc-list-item.mbsc-hover:before {
      background: rgba($background-contrast, 0.05);
    }

    &.mbsc-event.mbsc-list-item.mbsc-focus .mbsc-list-item-background,
    &.mbsc-event.mbsc-list-item.mbsc-selected .mbsc-list-item-background {
      background: rgba($background-contrast, 0.15);
    }

    &.mbsc-event-color {
      background: $event;
    }

    /* Popover events */

    @if ($is-dark) {
      &.mbsc-popover-list &.mbsc-event {
        background: lighten($background, 17%);
      }
    }

    /* Schedule view */

    /* TODO: refactor this without cascade */
    &.mbsc-eventcalendar-schedule .mbsc-calendar-day {
      background: $background-header;
    }

    &.mbsc-schedule-wrapper {
      background: $background-header;
    }

    /* Header */

    &.mbsc-schedule-header,
    &.mbsc-schedule-header-item {
      border-color: $border-color;
    }

    &.mbsc-schedule-header-day-today {
      color: $accent;
    }

    &.mbsc-schedule-header-day.mbsc-hover {
      background: rgba($accent, 0.3);
    }

    &.mbsc-schedule-header-dayname-curr,
    &.mbsc-schedule-header-day.mbsc-selected {
      color: $accent;
    }

    &.mbsc-schedule-header-day.mbsc-selected {
      background: $accent;
      color: $background-alt;
    }

    &.mbsc-schedule-header-dayname-curr {
      color: $text;
    }

    &.mbsc-schedule-date-header {
      background: $background-header;
      border-color: $border-color;
    }

    /* Grid & All-day row */
    &.mbsc-schedule-grid-wrapper {
      background: $background-alt;
    }

    &.mbsc-schedule-all-day-wrapper {
      border-color: $border-color;
      background: $background-alt;
    }

    &.mbsc-schedule-all-day-text,
    &.mbsc-schedule-timezone-label {
      color: $schedule-time-color;
    }

    &.mbsc-timeline-day::after,
    &.mbsc-timeline-slots,
    &.mbsc-timeline-slot-header,
    &.mbsc-timeline-header-month,
    &.mbsc-timeline-header-week,
    &.mbsc-timeline-footer-week,
    &.mbsc-timeline-header-date,
    &.mbsc-timeline-header-column,
    &.mbsc-timeline-header,
    &.mbsc-timeline-footer-date,
    &.mbsc-timeline-footer,
    &.mbsc-timeline-footer-column,
    &.mbsc-timeline-resource,
    &.mbsc-timeline-resource-header-cont,
    &.mbsc-timeline-resource-footer-cont,
    &.mbsc-timeline-sidebar-resource,
    &.mbsc-timeline-sidebar-header-cont,
    &.mbsc-timeline-sidebar-footer-cont,
    &.mbsc-timeline-column,
    &.mbsc-timeline-row,
    &.mbsc-schedule-resource-group,
    &.mbsc-schedule-resource,
    &.mbsc-schedule-all-day-item::after,
    &.mbsc-schedule-column,
    &.mbsc-schedule-item,
    &.mbsc-timeline-row-group::after,
    &.mbsc-timeline-row-date {
      border-color: $border-color;
    }

    &.mbsc-timeline-row-fixed {
      box-shadow: 0 1px 0 0 $border-color;
    }

    &.mbsc-timeline-header-column,
    &.mbsc-schedule-cursor-time,
    &.mbsc-schedule-time {
      color: $schedule-time-color;
    }

    /* Event style */

    &.mbsc-schedule-event {
      color: $event;
    }

    &.mbsc-schedule-event:after {
      background: rgba($background-alt, 0.9);
    }

    &.mbsc-schedule-event-title,
    &.mbsc-schedule-event-range {
      color: $background-contrast;
    }

    &.mbsc-schedule-event-active,
    &.mbsc-schedule-event-dragging {
      .mbsc-schedule-event-inner {
        color: get-contrast-color($event);
      }

      .mbsc-schedule-event-title,
      .mbsc-schedule-event-range {
        color: inherit;
      }
    }

    &.mbsc-schedule-time-indicator {
      border-color: $accent;
    }

    &.mbsc-schedule-time-indicator-day:before {
      background: $accent;
    }

    &.mbsc-schedule-time-indicator-time {
      background: rgba($background-alt, 0.8);
    }

    &.mbsc-schedule-time-indicator-time {
      color: $accent;
    }

    &.mbsc-schedule-cursor-time {
      background: rgba($background-alt, 0.8);
      color: $accent;
    }

    /* Invalid */

    &.mbsc-schedule-invalid {
      background: rgba($cell-disabled-bg, 0.75);
      color: $cell-disabled-color;
    }

    /* Timeline */

    &.mbsc-timeline-header-bg,
    &.mbsc-timeline-header-text,
    &.mbsc-timeline-footer-bg,
    &.mbsc-timeline-resource-bg,
    &.mbsc-timeline-resource-header-cont,
    &.mbsc-timeline-resource-footer-cont,
    &.mbsc-timeline-row-fixed,
    &.mbsc-timeline-sidebar-header-cont,
    &.mbsc-timeline-sidebar-footer-cont {
      background: $background-alt;
    }

    &.mbsc-timeline-header-active {
      color: get-contrast-color($accent);

      &::after {
        background-color: $accent;
      }
    }

    /* Connections */

    &.mbsc-connection {
      stroke: rgba($text, 0.7);
    }

    &.mbsc-connection-arrow {
      fill: $text;
    }
  }
}



@mixin mbsc-material-eventcalendar($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $background-param: map-get($params, 'calendar-background');
  $text-param: map-get($params, 'calendar-text');
  $accent-param: map-get($params, 'calendar-accent');
  $border-param: map-get($params, 'calendar-border');
  $event-param: map-get($params, 'calendar-event');

  $background: if($background-param, $background-param, $background);
  $text: if($text-param, $text-param, if($background-param, get-contrast-color($background-param), $text));
  $accent: if($accent-param, $accent-param, $accent);
  $event: if($event-param, $event-param, $accent);

  $border: '';
  $schedule-event-active: '';
  $schedule-text-color: '';
  $cell-disabled-bg: '';
  $cell-disabled-color: '';

  // Light background
  @if (lightness($background) > 50%) {
    $border: darken($background, 19%);
    $schedule-event-active: #000;
    $schedule-text-color: lighten(saturate(adjust-hue($text, 210deg), 4.27), 27.06);
    $cell-disabled-bg: darken($background, 6);
    $cell-disabled-color: darken($cell-disabled-bg, 40);
  }

  // Dark background
  @else {
    $border: lighten($background, 17%);
    $schedule-event-active: #fff;
    $schedule-text-color: darken(saturate(adjust-hue($text, 210deg), 6.32), 13.33);
    $cell-disabled-bg: lighten($background, 10);
    $cell-disabled-color: lighten($cell-disabled-bg, 37);
  }

  $border: if($border-param, $border-param, $border);

  .mbsc-#{$theme} {
    &.mbsc-eventcalendar {
      background: $background;
      color: $text;

      .mbsc-calendar-day.mbsc-disabled {
        background: $cell-disabled-bg;
      }
    }

    &.mbsc-event-list-empty {
      color: rgba($text, 0.7);
    }

    &.mbsc-schedule-date-header,
    &.mbsc-event-day.mbsc-list-header {
      background: $background;
      color: rgba($text, 0.7);
    }

    &.mbsc-colored-event.mbsc-list-item {
      background: $accent;
      color: get-contrast-color($accent);
    }

    &.mbsc-event.mbsc-list-item.mbsc-focus,
    &.mbsc-event.mbsc-list-item.mbsc-selected {
      box-shadow: 0 0.25em 0.5em 0 rgba(0, 0, 0, 0.4);
    }

    &.mbsc-event.mbsc-list-item.mbsc-focus::before,
    &.mbsc-event.mbsc-list-item.mbsc-selected::before {
      border-radius: 0.25em;
      box-shadow: inset 0 0 0 1px rgba(#fff, 0.5);
    }

    &.mbsc-event-color {
      background: $accent;
    }

    /* Schedule */

    /* Header */

    &.mbsc-schedule-header-dayname {
      color: $schedule-text-color;
    }

    &.mbsc-schedule-header-day {
      color: $text;
    }

    &.mbsc-schedule-header-dayname-curr {
      color: $accent;
    }

    &.mbsc-schedule-header-day-today {
      color: $accent;
    }

    &.mbsc-schedule-header-day.mbsc-hover {
      background: rgba($accent, 0.3);
    }

    &.mbsc-schedule-header-day.mbsc-selected {
      background-color: $accent;
      color: $background;
    }

    /* Grid & All-day cont */
    &.mbsc-timeline-day::after,
    &.mbsc-timeline-slots,
    &.mbsc-timeline-slot-header,
    &.mbsc-timeline-header-month,
    &.mbsc-timeline-header-week,
    &.mbsc-timeline-footer-week,
    &.mbsc-timeline-header-date,
    &.mbsc-timeline-header-column,
    &.mbsc-timeline-header,
    &.mbsc-timeline-footer-date,
    &.mbsc-timeline-footer-column,
    &.mbsc-timeline-footer,
    &.mbsc-timeline-resource,
    &.mbsc-timeline-resource-header-cont,
    &.mbsc-timeline-resource-footer-cont,
    &.mbsc-timeline-sidebar-resource,
    &.mbsc-timeline-sidebar-header-cont,
    &.mbsc-timeline-sidebar-footer-cont,
    &.mbsc-timeline-column,
    &.mbsc-timeline-row,
    &.mbsc-schedule-resource-group,
    &.mbsc-schedule-resource,
    &.mbsc-schedule-time-col,
    &.mbsc-schedule-all-day-item::after,
    &.mbsc-schedule-column,
    &.mbsc-schedule-item,
    &.mbsc-timeline-row-group::after,
    &.mbsc-timeline-row-date {
      border-color: $border;
    }

    &.mbsc-timeline-row-fixed {
      box-shadow: 0 1px 0 0 $border;
    }

    &.mbsc-schedule-time-wrapper-end:before,
    &.mbsc-schedule-time-wrapper:after {
      border-bottom: 1px solid $border;
    }

    &.mbsc-schedule-all-day-text,
    &.mbsc-schedule-timezone-label,
    &.mbsc-schedule-time {
      color: $schedule-text-color;
    }

    &.mbsc-schedule-time-indicator {
      border-color: $accent;
    }

    &.mbsc-schedule-time-indicator-day:before {
      background: $accent;
    }

    &.mbsc-schedule-time-indicator-time {
      background: rgba($background, 0.8);
    }

    &.mbsc-schedule-time-indicator-time {
      color: $accent;
    }

    &.mbsc-schedule-cursor-time {
      background: rgba($background, 0.8);
      color: $accent;
    }

    /* Event style */

    &.mbsc-schedule-event {
      color: $event;
    }

    &.mbsc-schedule-event-inner {
      color: get-contrast-color($event);
    }

    /* Invalid */
    &.mbsc-schedule-invalid {
      background: rgba($cell-disabled-bg, 0.75);
      color: $cell-disabled-color;
    }

    &.mbsc-schedule-event-active,
    &.mbsc-schedule-event-dragging {
      .mbsc-schedule-event-background {
        box-shadow: 0 4px 8px 0 rgba($schedule-event-active, 0.14), 0 1px 8px 0 rgba($schedule-event-active, 0.12),
          0 1px 3px -1px rgba($schedule-event-active, 0.2);
      }
    }

    &.mbsc-schedule-event-active .mbsc-schedule-event-background {
      box-shadow: inset 0 0 0 1px rgba(#fff, 0.5);
    }

    /* Timeline */

    &.mbsc-timeline-header-bg,
    &.mbsc-timeline-header-text,
    &.mbsc-timeline-footer-bg,
    &.mbsc-timeline-resource-bg,
    &.mbsc-timeline-resource-header-cont,
    &.mbsc-timeline-resource-footer-cont,
    &.mbsc-timeline-row-fixed,
    &.mbsc-timeline-sidebar-header-cont,
    &.mbsc-timeline-sidebar-footer-cont {
      background: $background;
    }

    &.mbsc-timeline-header-active {
      color: get-contrast-color($accent);

      &::after {
        background-color: $accent;
      }
    }

    /* Connections */

    &.mbsc-connection {
      stroke: rgba($text, 0.7);
    }

    &.mbsc-connection-arrow {
      fill: $text;
    }
  }
}



@mixin mbsc-windows-eventcalendar($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $background-param: map-get($params, 'calendar-background');
  $text-param: map-get($params, 'calendar-text');
  $accent-param: map-get($params, 'calendar-accent');
  $border-param: map-get($params, 'calendar-border');
  $event-param: map-get($params, 'calendar-event');

  $background: if($background-param, $background-param, $background);
  $text: if($text-param, $text-param, if($background-param, get-contrast-color($background-param), $text));
  $accent: if($accent-param, $accent-param, $accent);
  $event: if($event-param, $event-param, $accent);

  $border: '';
  $cell-disabled-bg: '';
  $cell-disabled-color: '';

  @if (lightness($background) > 50%) {
    $border: darken($background, 10%);
    $cell-disabled-bg: darken($background, 6);
    $cell-disabled-color: darken($cell-disabled-bg, 40);
  } @else {
    $border: lighten($background, 10%);
    $cell-disabled-bg: lighten($background, 10);
    $cell-disabled-color: lighten($cell-disabled-bg, 37);
  }

  $border: if($border-param, $border-param, $border);

  .mbsc-#{$theme} {
    &.mbsc-eventcalendar {
      background: $background;

      .mbsc-calendar-day.mbsc-disabled {
        background: $cell-disabled-bg;
      }
    }

    /* Event listing */

    &.mbsc-event-list-empty {
      color: rgba($text, 0.7);
    }

    &.mbsc-event-group {
      border-color: $border;
    }

    &.mbsc-event-day.mbsc-list-header,
    &.mbsc-event.mbsc-list-item {
      background: $background;
      color: $text;
    }

    &.mbsc-event.mbsc-focus,
    &.mbsc-event.mbsc-selected {
      background: rgba($accent, 0.25);
    }

    &.mbsc-event-color {
      background: $accent;
    }

    /* Schedule view */

    &.mbsc-schedule-wrapper {
      background: $background;
    }

    /* Header */

    &.mbsc-schedule-header-day {
      border-color: $border;
    }

    &.mbsc-schedule-header-item {
      color: $text;
    }

    &.mbsc-schedule-header-day-today {
      color: $accent;
    }

    &.mbsc-schedule-header-dayname.mbsc-selected:after {
      background: $accent;
    }

    &.mbsc-schedule-date-header {
      border-color: $border;
    }

    /* Time indicator */

    &.mbsc-schedule-time-indicator,
    &.mbsc-schedule-time-indicator-day {
      border-color: $accent;
    }

    &.mbsc-schedule-cursor-time,
    &.mbsc-schedule-time-indicator-time {
      background: rgba($background, 0.8);
      color: $accent;
    }

    &.mbsc-schedule-time-indicator-time {
      color: $accent;
    }

    /* Grid & All-day row */
    &.mbsc-timeline-day::after,
    &.mbsc-timeline-slots,
    &.mbsc-timeline-slot-header,
    &.mbsc-timeline-header-month,
    &.mbsc-timeline-header-week,
    &.mbsc-timeline-footer-week,
    &.mbsc-timeline-header-date,
    &.mbsc-timeline-header-column,
    &.mbsc-timeline-header,
    &.mbsc-timeline-footer-date,
    &.mbsc-timeline-footer-column,
    &.mbsc-timeline-footer,
    &.mbsc-timeline-resource,
    &.mbsc-timeline-resource-header-cont,
    &.mbsc-timeline-resource-footer-cont,
    &.mbsc-timeline-sidebar-resource,
    &.mbsc-timeline-sidebar-header-cont,
    &.mbsc-timeline-sidebar-footer-cont,
    &.mbsc-timeline-column,
    &.mbsc-timeline-row,
    &.mbsc-schedule-resource-group,
    &.mbsc-schedule-resource,
    &.mbsc-schedule-time-col,
    &.mbsc-schedule-all-day-item::after,
    &.mbsc-schedule-column,
    &.mbsc-schedule-item,
    &.mbsc-timeline-row-group::after,
    &.mbsc-timeline-row-date {
      border-color: $border;
    }

    &.mbsc-timeline-row-fixed {
      box-shadow: 0 1px 0 0 $border;
    }

    &.mbsc-schedule-time-wrapper {
      border-top: 1px solid $border;
      color: $text;
    }

    &.mbsc-schedule-time-wrapper-end {
      border-bottom: 1px solid $border;
      color: $text;
    }

    &.mbsc-schedule-all-day-text,
    &.mbsc-schedule-timezone-label,
    &.mbsc-schedule-time-cont {
      color: $text;
    }

    /* Event style */

    &.mbsc-schedule-event {
      color: $event;
    }

    &.mbsc-schedule-event-inner {
      color: get-contrast-color($event);
    }

    &.mbsc-schedule-event-dragging,
    &.mbsc-schedule-event-active {
      .mbsc-schedule-event-background {
        outline: 1px solid $text;
      }
    }

    /* Invalid */

    &.mbsc-schedule-invalid {
      background: rgba($cell-disabled-bg, 0.75);
      color: $cell-disabled-color;
    }

    /* Timeline */

    &.mbsc-timeline-header-bg,
    &.mbsc-timeline-header-text,
    &.mbsc-timeline-footer-bg,
    &.mbsc-timeline-resource-bg,
    &.mbsc-timeline-resource-header-cont,
    &.mbsc-timeline-resource-footer-cont,
    &.mbsc-timeline-row-fixed,
    &.mbsc-timeline-sidebar-header-cont,
    &.mbsc-timeline-sidebar-footer-cont {
      background: $background;
    }

    .mbsc-timeline-header-active::after {
      background-color: $accent;
    }

    /* Connections */

    &.mbsc-connection {
      stroke: rgba($text, 0.7);
    }

    &.mbsc-connection-arrow {
      fill: $text;
    }
  }
}

// form-controls component


@mixin mbsc-ios-form-controls($theme, $params) {
  $text: map-get($params, 'text');
  $background: map-get($params, 'background');

  $bg-param: map-get($params, 'form-background');
  $err-param: map-get($params, 'form-error');

  $background: if($bg-param, $bg-param, $background);

  $is-dark: false;
  $border-color: '';
  $wrapper-background: '';
  $wrapper-background-popup: '';

  @if (lightness($background) > 50%) {
    $border-color: darken($background, 17%);
    $wrapper-background: lighten($background, 6%);
    $wrapper-background-popup: $wrapper-background;
  } @else {
    $is-dark: true;
    $border-color: lighten($background, 20%);
    $wrapper-background: lighten($background, 11%);
    $wrapper-background-popup: lighten($background, 17%);
  }

  $error: if($err-param, $err-param, $mbsc-ios-error);

  .mbsc-#{$theme} {
    /* Wrapper */

    &.mbsc-form-control-wrapper {
      background: $wrapper-background;
    }

    &.mbsc-form-control-wrapper:before,
    &.mbsc-form-control-wrapper:after {
      border-color: $border-color;
    }

    &.mbsc-form-control-wrapper.mbsc-error:after,
    &.mbsc-form-control-wrapper.mbsc-error + .mbsc-form-control-wrapper:before {
      border-color: $error;
    }

    /* Inside popup */

    @if ($is-dark) {
      &.mbsc-popup &.mbsc-form-control-wrapper {
        background: $wrapper-background-popup;
      }
    }
  }
}



@mixin mbsc-material-form-controls($theme, $params) {
}



@mixin mbsc-windows-form-controls($theme, $params) {
}

// input component


$mbsc-ios-input-background: $mbsc-input-background-light !default;
$mbsc-ios-input-text: $mbsc-input-text-light !default;
$mbsc-ios-input-accent: $mbsc-input-accent-light !default;
$mbsc-ios-input-border: $mbsc-input-border-light !default;

$mbsc-ios-dark-input-background: $mbsc-input-background-dark !default;
$mbsc-ios-dark-input-text: $mbsc-input-text-dark !default;
$mbsc-ios-dark-input-accent: $mbsc-input-accent-dark !default;
$mbsc-ios-dark-input-border: $mbsc-input-border-dark !default;

$mbsc-ios-colors: map-merge(
  $mbsc-ios-colors,
  (
    // Colors map
    'input-background': $mbsc-ios-input-background,
    'input-text': $mbsc-ios-input-text,
    'input-accent': $mbsc-ios-input-accent,
    'input-border': $mbsc-ios-input-border
  )
);

$mbsc-ios-dark-colors: map-merge(
  $mbsc-ios-dark-colors,
  (
    // Colors map
    'input-background': $mbsc-ios-dark-input-background,
    'input-text': $mbsc-ios-dark-input-text,
    'input-accent': $mbsc-ios-dark-input-accent,
    'input-border': $mbsc-ios-dark-input-border
  )
);

@mixin mbsc-ios-input($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');

  $bg-param: map-get($params, 'input-background');
  $text-param: map-get($params, 'input-text');
  $brd-param: map-get($params, 'input-border');
  $err-param: map-get($params, 'form-error');

  $background: if($bg-param, $bg-param, $background);
  $text: if($text-param, $text-param, $text);
  $error: if($err-param, $err-param, $mbsc-ios-error);

  $is-dark: false;
  $border-color: '';
  $input-background: '';
  $input-background-popup: '';
  $input-tag-background: '';
  $textfield-text: $text;
  $textfield-icon-color: '';

  // Light background
  @if (lightness($background) > 50%) {
    $input-background: lighten($background, 6%);
    $input-background-popup: $input-background;
    $input-tag-background: darken($background, 10%);
    $textfield-icon-color: lighten($text, 54.9);
    $border-color: darken($background, 17%);
  }

  // Dark background
  @else {
    $is-dark: true;
    $input-background: lighten($background, 11%);
    $input-background-popup: lighten($background, 17%);
    $input-tag-background: lighten($background, 23%);
    $textfield-icon-color: $mbsc-ios-dark-text;
    $border-color: lighten($background, 20%);
  }

  $border-color: if($brd-param, $brd-param, $border-color);

  .mbsc-#{$theme} {
    /* Wrapper */

    &.mbsc-textfield-wrapper {
      background: none;
    }

    &.mbsc-textfield-wrapper-underline {
      background: $input-background;
    }

    /* Form element */

    &.mbsc-label,
    &.mbsc-textfield {
      color: $textfield-text;
    }

    &.mbsc-textfield-box,
    &.mbsc-textfield-outline {
      background: $input-background;
    }

    &.mbsc-textfield-outline {
      border-color: $border-color;
    }

    /* Icon */

    &.mbsc-textfield-icon,
    &.mbsc-select-icon {
      color: $textfield-icon-color;
    }

    /* Error */

    &.mbsc-textfield-outline.mbsc-error,
    &.mbsc-textfield-box.mbsc-error {
      border-color: $error;
    }

    &.mbsc-error-message {
      color: $error;
    }

    /* Select */

    &.mbsc-select {
      background: $input-background;
    }

    /* Textarea */

    &.mbsc-textarea-inner.mbsc-textfield-inner-box,
    &.mbsc-textarea-inner.mbsc-textfield-inner-outline {
      background: $input-background;
    }

    &.mbsc-textarea-inner.mbsc-textfield-inner-outline {
      border-color: $border-color;
    }

    &.mbsc-textarea-inner.mbsc-error {
      border-color: $error;
    }

    /* Inside popup */

    @if ($is-dark) {
      &.mbsc-popup &.mbsc-textfield-wrapper {
        background: none;
      }

      &.mbsc-popup &.mbsc-select,
      &.mbsc-popup &.mbsc-textfield-wrapper-underline,
      &.mbsc-popup &.mbsc-textfield-box,
      &.mbsc-popup &.mbsc-textfield-outline,
      &.mbsc-popup &.mbsc-textarea-inner.mbsc-textfield-inner-box,
      &.mbsc-popup &.mbsc-textarea-inner.mbsc-textfield-inner-outline {
        background: $input-background-popup;
      }
    }

    /* Input tags */

    &.mbsc-textfield-tag {
      background: $input-tag-background;
    }

    &.mbsc-textfield-tag-clear {
      color: $textfield-icon-color;
    }
  }
}



$mbsc-material-input-background: $mbsc-input-background-light !default;
$mbsc-material-input-text: $mbsc-input-text-light !default;
$mbsc-material-input-accent: $mbsc-input-accent-light !default;
$mbsc-material-input-border: $mbsc-input-border-light !default;

$mbsc-material-dark-input-background: $mbsc-input-background-dark !default;
$mbsc-material-dark-input-text: $mbsc-input-text-dark !default;
$mbsc-material-dark-input-accent: $mbsc-input-accent-dark !default;
$mbsc-material-dark-input-border: $mbsc-input-border-dark !default;

$mbsc-material-colors: map-merge(
  $mbsc-material-colors,
  (
    // Colors map
    'input-background': $mbsc-material-input-background,
    'input-text': $mbsc-material-input-text,
    'input-accent': $mbsc-material-input-accent,
    'input-border': $mbsc-material-input-border
  )
);

$mbsc-material-dark-colors: map-merge(
  $mbsc-material-dark-colors,
  (
    // Colors map
    'input-background': $mbsc-material-dark-input-background,
    'input-text': $mbsc-material-dark-input-text,
    'input-accent': $mbsc-material-dark-input-accent,
    'input-border': $mbsc-material-dark-input-border
  )
);

@mixin mbsc-material-input($theme, $params) {
  $background: map-get($params, 'background');
  $accent: map-get($params, 'accent');
  $text: map-get($params, 'text');

  $bg-param: map-get($params, 'input-background');
  $text-param: map-get($params, 'input-text');
  $acc-param: map-get($params, 'input-accent');
  $err-param: map-get($params, 'form-error');
  $brd-param: map-get($params, 'input-border');

  $text: if($text-param, $text-param, $text);
  $accent: if($acc-param, $acc-param, $accent);
  $error: if($err-param, $err-param, $mbsc-material-error);

  $input-base-color: '';
  $input-text: '';
  $input-icon-color: '';
  $input-border-color: '';
  $input-label-color: '';
  $input-background: '';
  $input-box-background: '';
  $input-box-hover-background: '';
  $input-box-focus-background: '';
  $input-tag-background: '';

  // Light background
  @if (lightness($background) > 50%) {
    $input-base-color: darken($text, 19%);
    $input-text: rgba($input-base-color, 0.87);
    $input-border-color: rgba($input-base-color, 0.42);
    $input-icon-color: rgba($input-base-color, 0.54);
    $input-label-color: rgba($input-base-color, 0.6);
    $input-background: if($bg-param, $bg-param, $background);
    $input-box-background: if($bg-param, $bg-param, darken($background, 4%));
    $input-box-hover-background: darken($input-box-background, 3%);
    $input-box-focus-background: darken($input-box-background, 7%);
    $input-tag-background: darken($background, 17%);
  }

  // Dark background
  @else {
    $input-base-color: $text;
    $input-text: $input-base-color;
    $input-border-color: $input-base-color;
    $input-icon-color: $input-base-color;
    $input-label-color: rgba($input-base-color, 0.6);
    $input-background: if($bg-param, $bg-param, $background);
    $input-box-background: if($bg-param, $bg-param, lighten($background, 4%));
    $input-box-hover-background: lighten($input-box-background, 3%);
    $input-box-focus-background: lighten($input-box-background, 7%);
    $input-tag-background: lighten($background, 27%);
  }

  $input-text: if($text-param, $text-param, $input-text);
  $input-icon-color: if($text-param, $text-param, $input-icon-color);
  $input-label-color: if($text-param, $text-param, $input-label-color);
  $input-border-color: if($brd-param, $brd-param, $input-border-color);

  .mbsc-#{$theme} {
    /* Form element */

    &.mbsc-textfield {
      border-bottom-color: $input-border-color;
      color: $input-text;
    }

    &.mbsc-textfield.mbsc-hover {
      border-color: $input-text;
    }

    &.mbsc-textfield.mbsc-error {
      border-color: $error;
    }

    /* Icon */

    &.mbsc-textfield-icon,
    &.mbsc-select-icon {
      color: $input-icon-color;
    }

    /* Ripple */

    &.mbsc-textfield-ripple {
      background-color: $accent;
    }

    &.mbsc-textfield-ripple.mbsc-error {
      background-color: $error;
    }

    /* Label */

    &.mbsc-label {
      color: $input-label-color;
    }

    &.mbsc-label-stacked.mbsc-focus,
    &.mbsc-label-floating.mbsc-focus {
      color: $accent;
    }

    &.mbsc-label.mbsc-error {
      color: $error;
    }

    /* Error message */

    &.mbsc-error-message {
      color: $error;
    }

    /* Select */

    &.mbsc-select {
      background: $input-background;
    }

    /* Box input ------------------------------------------------------------------------------- */

    &.mbsc-textfield-box.mbsc-select,
    &.mbsc-textfield-inner-box {
      background: $input-box-background;
    }

    &.mbsc-textfield-box.mbsc-select.mbsc-hover,
    &.mbsc-textfield-inner-box.mbsc-hover {
      background: $input-box-hover-background;
    }

    &.mbsc-textfield-box.mbsc-select.mbsc-focus,
    &.mbsc-textfield-inner-box.mbsc-focus {
      background: $input-box-focus-background;
    }

    /* ----------------------------------------------------------------------------------------- */

    /* Outline input --------------------------------------------------------------------------- */

    &.mbsc-textfield-fieldset {
      border-color: $input-border-color;
    }

    &.mbsc-textfield-fieldset.mbsc-hover {
      border-color: $input-text;
    }

    &.mbsc-textfield-fieldset.mbsc-focus {
      border-color: $accent;
    }

    &.mbsc-textfield-fieldset.mbsc-error {
      border-color: $error;
    }

    &.mbsc-textfield-fieldset.disabled {
      border-color: $input-text;
    }

    /* Input tag */

    &.mbsc-textfield-tag {
      background: $input-tag-background;
    }

    &.mbsc-textfield-tag-clear {
      color: $input-icon-color;
    }

    /* ----------------------------------------------------------------------------------------- */
  }
}



$mbsc-windows-input-background: $mbsc-input-background-light !default;
$mbsc-windows-input-text: $mbsc-input-text-light !default;
$mbsc-windows-input-accent: $mbsc-input-accent-light !default;
$mbsc-windows-input-border: $mbsc-input-border-light !default;

$mbsc-windows-dark-input-background: $mbsc-input-background-dark !default;
$mbsc-windows-dark-input-text: $mbsc-input-text-dark !default;
$mbsc-windows-dark-input-accent: $mbsc-input-accent-dark !default;
$mbsc-windows-dark-input-border: $mbsc-input-border-dark !default;

$mbsc-windows-colors: map-merge(
  $mbsc-windows-colors,
  (
    // Colors map
    'input-background': $mbsc-windows-input-background,
    'input-text': $mbsc-windows-input-text,
    'input-accent': $mbsc-windows-input-accent,
    'input-border': $mbsc-windows-input-border
  )
);

$mbsc-windows-dark-colors: map-merge(
  $mbsc-windows-dark-colors,
  (
    // Colors map
    'input-background': $mbsc-windows-dark-input-background,
    'input-text': $mbsc-windows-dark-input-text,
    'input-accent': $mbsc-windows-dark-input-accent,
    'input-border': $mbsc-windows-dark-input-border
  )
);

@mixin mbsc-windows-input($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $bg-param: map-get($params, 'input-background');
  $text-param: map-get($params, 'input-text');
  $brd-param: map-get($params, 'input-border');
  $acc-param: map-get($params, 'input-accent');
  $err-param: map-get($params, 'form-error');

  $background: if($bg-param, $bg-param, $background);
  $text: if($text-param, $text-param, $text);
  $accent: if($acc-param, $acc-param, $accent);
  $error: if($err-param, $err-param, $mbsc-windows-error);

  $input-backgound: $background;
  $input-hover: $text;
  $input-border: '';
  $input-disabled-background: '';
  $input-disabled-color: '';
  $input-tag-background: '';

  // Light background
  @if (lightness($background) > 50%) {
    $input-border: lighten($text, 33%);
    $input-disabled-background: darken($background, 5%);
    $input-disabled-color: lighten($text, 42%);
    $input-tag-background: darken($background, 10%);
  }

  // Dark background
  @else {
    $input-border: darken($text, 33%);
    $input-disabled-background: lighten($background, 5%);
    $input-disabled-color: darken($text, 42%);
    $input-tag-background: lighten($background, 27%);
  }

  $input-border: if($brd-param, $brd-param, $input-border);

  .mbsc-#{$theme} {
    /* Wrapper */

    &.mbsc-textfield-wrapper {
      color: $text;
    }

    &.mbsc-textfield-wrapper.mbsc-disabled {
      color: $input-disabled-color;
    }

    /* Form element */

    &.mbsc-textfield {
      background: $input-backgound;
      color: $text;
    }

    &.mbsc-textfield-box,
    &.mbsc-textfield-outline {
      border-color: $input-border;
    }

    &.mbsc-textfield.mbsc-hover {
      border-color: $input-hover;
    }

    &.mbsc-textfield-box.mbsc-focus,
    &.mbsc-textfield-outline.mbsc-focus {
      border-color: $accent;
      box-shadow: 0 0 0 1px $accent inset;
    }

    &.mbsc-textfield.mbsc-disabled {
      background: $input-disabled-background;
      border-color: $input-disabled-background;
    }

    &.mbsc-textfield.mbsc-error {
      border-color: $error;
    }

    &.mbsc-textfield-box.mbsc-error.mbsc-focus,
    &.mbsc-textfield-outline.mbsc-error.mbsc-focus {
      box-shadow: 0 0 0 1px $error inset;
    }

    /* Error message */

    &.mbsc-error-message {
      color: $error;
    }

    /* Label */

    &.mbsc-label {
      color: $text;
    }

    /* Underline input --------------------------------------------------------------------------- */

    &.mbsc-textfield-wrapper-underline {
      border-color: $input-border;
    }

    &.mbsc-textfield-wrapper-underline.mbsc-hover {
      border-color: $input-hover;
    }

    &.mbsc-textfield-wrapper-underline.mbsc-focus {
      border-color: $accent;
      box-shadow: 0 1px 0 0 $accent;
    }

    &.mbsc-textfield-wrapper-underline.mbsc-disabled {
      border-color: $input-disabled-background;
    }

    &.mbsc-textfield-wrapper-underline.mbsc-error {
      border-color: $error;
    }

    &.mbsc-textfield-wrapper-underline.mbsc-error.mbsc-focus {
      box-shadow: 0 1px 0 0 $error;
    }

    /* ----------------------------------------------------------------------------------------- */

    /* Input tags */

    &.mbsc-textfield-tag {
      background: $input-tag-background;
    }
  }
}

// list component


@mixin mbsc-ios-list($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');

  $colors: mbsc-ios-colors($params);

  $background-alt: '';
  $background-contrast: '';
  $border-color: '';
  $header-background: '';
  $header-text: '';

  @if (lightness($background) > 50%) {
    $background-alt: lighten($background, 3%);
    $background-contrast: #000;
    $border-color: darken($background, 17%);
    $header-background: adjust-hue(darken(saturate($background, 19%), 2%), 240deg);
    $header-text: adjust-hue(lighten(saturate($text, 2%), 44%), 240deg);
  } @else {
    $background-alt: $background;
    $background-contrast: #fff;
    $border-color: lighten($background, 20%);
    $header-background: adjust-hue(lighten(desaturate($background, 19%), 10%), 240deg);
    $header-text: adjust-hue(darken(desaturate($text, 2%), 44%), 240deg);
  }

  .mbsc-#{$theme} {
    &.mbsc-list-item {
      background: $background-alt;
      border-color: $border-color;
      color: $text;
    }

    &.mbsc-list-item:before,
    &.mbsc-list-item:after {
      border-top: 1px solid $border-color;
    }

    &.mbsc-list-header {
      background: $header-background;
      color: $header-text;
      border-color: $border-color;
    }

    &.mbsc-list-item.mbsc-active:before {
      background: rgba(0, 0, 0, 0.15);
    }
  }
}



@mixin mbsc-material-list($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  .mbsc-#{$theme} {
    &.mbsc-list-header {
      background: $background;
      color: $accent;
    }

    &.mbsc-list-item {
      background: $background;
      color: $text;
    }

    &.mbsc-list-item.mbsc-hover:before {
      background: rgba(0, 0, 0, 0.05);
    }

    &.mbsc-list-item.mbsc-active:before {
      background: rgba(0, 0, 0, 0.1);
    }
  }
}



@mixin mbsc-windows-list($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');

  $active: '';
  $hover: '';

  @if (lightness($background) > 50%) {
    $hover: rgba($text, 0.1);
    $active: rgba($text, 0.2);
  } @else {
    $hover: rgba($text, 0.22);
    $active: rgba($text, 0.3);
  }

  .mbsc-#{$theme} {
    &.mbsc-list-header,
    &.mbsc-list-item {
      background: $background;
      color: $text;
    }

    &.mbsc-list-item.mbsc-hover:before {
      background: $hover;
    }

    &.mbsc-list-item.mbsc-active:before {
      background: $active;
    }
  }
}

// page component


// Theme specific variables - inherited from global variables

// background
$mbsc-ios-page-background: $mbsc-page-background-light !default;
$mbsc-ios-dark-page-background: $mbsc-page-background-dark !default;

// text
$mbsc-ios-page-text: $mbsc-page-text-light !default;
$mbsc-ios-dark-page-text: $mbsc-page-text-dark !default;

// add variables to color maps
$mbsc-ios-colors: map-merge(
  $mbsc-ios-colors,
  (
    'page-background': $mbsc-ios-page-background,
    'page-text': $mbsc-ios-page-text,
  )
);

$mbsc-ios-dark-colors: map-merge(
  $mbsc-ios-dark-colors,
  (
    'page-background': $mbsc-ios-dark-page-background,
    'page-text': $mbsc-ios-dark-page-text,
  )
);

@mixin mbsc-ios-page($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $title-text: '';

  @if (lightness($background) > 50%) {
    // $background: darken($background, 2%);
    $background: adjust-hue(darken(saturate($background, 24%), 1%), 240deg);
    $title-text: lighten($text, 43%);
  } @else {
    // $background: adjust-hue(lighten(desaturate($background, 19%), 10%), 240deg);
    // $background: lighten($background, 2%);
    $title-text: darken($text, 43%);
  }

  // get custom params
  $text-param: map-get($params, 'page-text');
  $background-param: map-get($params, 'page-background');
  // overwrite params with custom variables
  $background: if($background-param, $background-param, $background);
  $text: if($text-param, $text-param, if($background-param, get-contrast-color($background-param), $text));

  .mbsc-#{$theme} {
    &.mbsc-page {
      background: $background;
      color: $text;
    }

    .mbsc-block-title,
    .mbsc-form-group-title {
      color: $title-text;
    }

    a {
      color: $accent;
    }
  }
}



// Theme specific variables - inherited from global variables

// background
$mbsc-material-page-background: $mbsc-page-background-light !default;
$mbsc-material-dark-page-background: $mbsc-page-background-dark !default;
// text
$mbsc-material-page-text: $mbsc-page-text-light !default;
$mbsc-material-dark-page-text: $mbsc-page-text-dark !default;

// add variablest to color maps
$mbsc-material-colors: map-merge(
  $mbsc-material-colors,
  (
    'page-background': $mbsc-material-page-background,
    'page-text': $mbsc-material-page-text,
  )
);

$mbsc-material-dark-colors: map-merge(
  $mbsc-material-dark-colors,
  (
    'page-background': $mbsc-material-dark-page-background,
    'page-text': $mbsc-material-dark-page-text,
  )
);

@mixin mbsc-material-page($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  .mbsc-#{$theme} {
    &.mbsc-page {
      background-color: $background;
      color: $text;
    }

    .mbsc-block-title,
    .mbsc-form-group-title {
      color: $accent;
    }

    a {
      color: $accent;
    }
  }
}



// Theme specific variables - inherited from global variables

// background
$mbsc-windows-page-background: $mbsc-page-background-light !default;
$mbsc-windows-dark-page-background: $mbsc-page-background-dark !default;

// text
$mbsc-windows-page-text: $mbsc-page-text-light !default;
$mbsc-windows-dark-page-text: $mbsc-page-text-dark !default;

// add variables to color maps
$mbsc-windows-colors: map-merge(
  $mbsc-windows-colors,
  (
    //
    'page-background': $mbsc-windows-page-background,
    'page-text': $mbsc-windows-page-text
  )
);

$mbsc-windows-dark-colors: map-merge(
  $mbsc-windows-dark-colors,
  (
    //
    'page-background': $mbsc-windows-dark-page-background,
    'page-text': $mbsc-windows-dark-page-text
  )
);

@mixin mbsc-windows-page($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  .mbsc-#{$theme} {
    &.mbsc-page {
      background-color: $background;
      color: $text;
    }

    .mbsc-block-title,
    .mbsc-form-group-title {
      color: $text;
    }

    a {
      color: $accent;
    }
  }
}

// popup component


@mixin mbsc-ios-popup($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $is-dark: false;
  $button-hover: '';
  $buttons-background: '';
  $popup-border: '';
  $popup-background: '';

  @if (lightness($background) > 50%) {
    $button-hover: darken($background, 5%);
    $buttons-background: $background; // darken($background, 3%);
    $popup-border: darken($background, 17%);
    $popup-background: adjust-hue(darken(saturate($background, 24%), 1%), 240deg);
  } @else {
    $is-dark: true;
    $button-hover: lighten($background, 14%);
    $buttons-background: lighten($background, 16%);
    $popup-border: lighten($background, 20%);
    $popup-background: lighten($background, 11%);
  }

  .mbsc-#{$theme} {
    &.mbsc-popup-arrow {
      background: $popup-background;
      // border-color: $popup-border;
      box-shadow: 0 0 1em rgba(0, 0, 0, 0.2);
    }

    &.mbsc-popup-top,
    &.mbsc-popup-bottom {
      border-color: $popup-border;
    }

    &.mbsc-popup-body {
      background: $popup-background;
      // border-color: $popup-border;
      color: $text;
    }

    &.mbsc-popup-body-round,
    &.mbsc-popup-body-center,
    &.mbsc-popup-body-anchored {
      box-shadow: 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12);
    }

    &.mbsc-popup-header-center {
      border-color: $popup-border;
    }

    &.mbsc-popup-buttons,
    &.mbsc-popup-header-no-buttons {
      border-color: $popup-border;
    }

    &.mbsc-popup-buttons-bottom,
    &.mbsc-popup-buttons-top {
      background: $buttons-background;
    }

    @if ($is-dark) {
      &.mbsc-popup-buttons-anchored {
        background: $buttons-background;
      }
    }

    &.mbsc-popup-button-flex.mbsc-button-flat.mbsc-font {
      border-color: $popup-border;
    }

    &.mbsc-popup-button-flex.mbsc-font.mbsc-hover,
    &.mbsc-popup-button-flex.mbsc-font.mbsc-focus {
      background: $button-hover;
    }

    &.mbsc-popup-button-flex.mbsc-font.mbsc-active {
      background: $popup-border;
    }

    &.mbsc-popup-button-flex.mbsc-button.mbsc-disabled {
      color: rgba($accent, 0.2);
    }
  }
}



@mixin mbsc-material-popup($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $popup-background: '';

  @if (lightness($background) > 50%) {
    $popup-background: $background;
  } @else {
    $popup-background: lighten($background, 19%);
  }

  .mbsc-#{$theme} {
    &.mbsc-popup-body {
      background: $popup-background;
      box-shadow: 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12);
      color: $text;
    }

    &.mbsc-popup-arrow {
      background: $popup-background;
      box-shadow: 0 0 1em rgba(0, 0, 0, 0.2);
    }

    &.mbsc-popup-button.mbsc-font {
      color: $accent;
    }
  }
}



@mixin mbsc-windows-popup($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $border: '';

  @if (lightness($background) > 50%) {
    $border: darken($background, 10%);
  } @else {
    $border: lighten($background, 10%);
  }

  .mbsc-#{$theme} {
    &.mbsc-popup {
      background: $background;
      border-color: $border;
      box-shadow: rgba(0, 0, 0, 0.133) 0 6px 14px 0, rgba(0, 0, 0, 0.11) 0 1px 4px 0;
      color: $text;
    }

    &.mbsc-popup-arrow {
      background: $background;
      border-color: $border;
    }

    &.mbsc-popup-header,
    &.mbsc-popup-buttons {
      border-color: $border;
    }
  }
}

// radio component


@mixin mbsc-ios-radio($theme, $params) {
  $background: map-get($params, 'background');
  $accent: map-get($params, 'accent');

  $accent-param: map-get($params, 'form-accent');
  $bg-param: map-get($params, 'form-background');

  $accent: if($accent-param, $accent-param, $accent);
  $background: if($bg-param, $bg-param, $background);

  $disabled: '';

  @if (lightness($background) > 50%) {
    $disabled: darken($background, 20%);
  } @else {
    $disabled: lighten($background, 23%);
  }

  $form-selection: '';

  @if (lightness($accent) > 50%) {
    $form-selection: lighten(saturate($accent, 15%), 3%);
  } @else {
    $form-selection: darken(desaturate($accent, 15%), 3%);
  }

  .mbsc-#{$theme} {
    &.mbsc-radio-box:after {
      border-color: $form-selection;
    }

    &.mbsc-radio-label.mbsc-disabled {
      color: $disabled;
    }
  }
}



@mixin mbsc-material-radio($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $accent-param: map-get($params, 'form-accent');
  $text-param: map-get($params, 'form-text');

  $accent: if($accent-param, $accent-param, $accent);
  $text: if($text-param, $text-param, $text);

  .mbsc-#{$theme} {
    &.mbsc-radio-box {
      color: $accent;
      border-color: $text;
    }
  }
}



@mixin mbsc-windows-radio($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $text-param: map-get($params, 'form-text');
  $accent-param: map-get($params, 'form-accent');

  $accent: if($accent-param, $accent-param, $accent);
  $text: if($text-param, $text-param, $text);

  .mbsc-#{$theme} {
    &.mbsc-radio-box {
      border: 0.125em solid $text;
    }

    &.mbsc-radio-box:after {
      background: $text;
    }

    &.mbsc-radio-box.mbsc-checked {
      border-color: $accent;
    }

    &.mbsc-radio-box.mbsc-active {
      border-color: rgba($text, 0.6);
    }

    &.mbsc-radio-box.mbsc-active:after {
      background: rgba($text, 0.6);
    }
  }
}

// scroller component


@mixin mbsc-ios-scroller($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $item-color: hsl(hue($text), saturation($text), 62%);

  $background-alt: '';
  $background-top-bottom: '';
  $item-3d: '';
  $overlay: '';
  $scroller-background: '';
  $scroller-selected-line: '';

  @if (lightness($background) > 50%) {
    $background-alt: lighten($background, 3%);
    $background-top-bottom: adjust-hue(darken(saturate($background, 12%), 13%), 216deg);
    $item-3d: darken($background, 33%);
    $overlay: $background-alt;
    $scroller-background: $background-alt;
    $scroller-selected-line: #000;
  } @else {
    $background-alt: lighten($background, 11%);
    $background-top-bottom: lighten($background, 11%);
    $item-3d: lighten($background, 40%);
    $overlay: $background-alt;
    $scroller-background: $background-alt;
    $scroller-selected-line: #fff;
  }

  .mbsc-#{$theme} {
    &.mbsc-scroller {
      background: $scroller-background;
    }

    &.mbsc-scroller-top,
    &.mbsc-scroller-bottom {
      background: $background-top-bottom;
    }

    &.mbsc-scroller-wheel-overlay {
      background: linear-gradient($overlay, rgba($overlay, 0) 52%, rgba($overlay, 0) 48%, $overlay);
    }

    &.mbsc-scroller-wheel-overlay-top,
    &.mbsc-scroller-wheel-overlay-bottom {
      background: linear-gradient(
        $background-top-bottom,
        rgba($background-top-bottom, 0) 52%,
        rgba($background-top-bottom, 0) 48%,
        $background-top-bottom
      );
    }

    &.mbsc-scroller-wheel-line {
      background: rgba($scroller-selected-line, 0.1);
    }

    &.mbsc-scroller-wheel-item {
      color: $item-color;
    }

    &.mbsc-scroller-wheel-item.mbsc-active,
    &.mbsc-scroller-wheel-item.mbsc-hover,
    &.mbsc-scroller-wheel-item.mbsc-focus,
    &.mbsc-scroller-wheel-header.mbsc-focus {
      background: rgba($accent, 0.15);
    }

    &.mbsc-scroller-wheel-item-2d,
    &.mbsc-scroller-wheel-item.mbsc-selected {
      color: $text;
    }

    /* 3D */

    &.mbsc-scroller-wheel-cont-3d {
      background: $scroller-background;
    }

    &.mbsc-scroller-wheel-item-3d {
      color: $item-3d;
    }

    &.mbsc-scroller-wheel-header,
    &.mbsc-scroller-wheel-item-3d.mbsc-wheel-item-multi {
      color: $text;
    }

    &.mbsc-scroller-wheel-item-3d.mbsc-wheel-item-multi.mbsc-selected-3d {
      color: $accent;
    }

    &.mbsc-scroller-wheel-cont-top.mbsc-scroller-wheel-cont-3d,
    &.mbsc-scroller-wheel-cont-bottom.mbsc-scroller-wheel-cont-3d {
      background: $background-top-bottom;
    }

    &.mbsc-scroller-wheel-cont-inline.mbsc-scroller-wheel-cont-3d {
      background: $background-alt;
    }

    /* Desktop style */

    &.mbsc-scroller-pointer {
      &.mbsc-scroller {
        background: $scroller-background;
      }

      &.mbsc-scroller-inline {
        background: $background-alt;
      }

      .mbsc-scroller-wheel-item {
        color: $text;
      }

      .mbsc-scroller-wheel-item.mbsc-selected {
        color: $accent;
      }
    }
  }
}



@mixin mbsc-material-scroller($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  .mbsc-#{$theme} {
    &.mbsc-scroller-wheel-cont::after,
    &.mbsc-scroller-wheel-cont::before {
      border-color: $accent;
    }

    &.mbsc-scroller-wheel-multi::after,
    &.mbsc-scroller-wheel-multi::before {
      border-color: transparent;
    }

    &.mbsc-scroller-wheel-item.mbsc-active,
    &.mbsc-scroller-wheel-item.mbsc-hover,
    &.mbsc-scroller-wheel-item.mbsc-focus,
    &.mbsc-scroller-wheel-header.mbsc-focus {
      background: rgba(0, 0, 0, 0.05);
    }

    &.mbsc-wheel-checkmark::after {
      border-color: $accent;
    }
  }
}



@mixin mbsc-windows-scroller($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $border: '';
  $wheel-button: '';

  @if (lightness($background) > 50%) {
    $border: darken($background, 10%);
  } @else {
    $border: lighten($background, 10%);
  }

  .mbsc-#{$theme} {
    &.mbsc-scroller-wheel-wrapper {
      color: $text;
      border-color: $border;
    }

    &.mbsc-scroller-wheel-line {
      background: rgba($accent, 0.4);
    }

    &.mbsc-scroller-wheel-item.mbsc-active,
    &.mbsc-scroller-wheel-item.mbsc-hover,
    &.mbsc-scroller-wheel-item.mbsc-focus,
    &.mbsc-scroller-wheel-header.mbsc-focus {
      background: rgba($text, 0.1);
    }

    &.mbsc-wheel-checkmark::after {
      border-color: $accent;
    }
  }
}

// segmented component


@mixin mbsc-ios-segmented($theme, $params) {
  $background: map-get($params, 'background');
  $accent: map-get($params, 'accent');

  $bg-param: map-get($params, 'form-background');
  $acc-param: map-get($params, 'form-accent');

  $background: if($bg-param, $bg-param, $background);
  $accent: if($acc-param, $acc-param, $accent);

  $is-dark: false;
  $selectbox: '';
  $selectbox-contrast: '';
  $button-background: '';
  $shadow: '';

  @if (lightness($background) > 50%) {
    $button-background: darken($background, 10%);
    $selectbox: #fff;
    $selectbox-contrast: #000;
    $shadow: darken($background, 24%);
  } @else {
    $is-dark: true;
    $button-background: lighten($background, 11%);
    $selectbox: #5a5a5a;
    $selectbox-contrast: #fff;
    $shadow: $background;
  }

  .mbsc-#{$theme} {
    &.mbsc-segmented {
      background: $button-background;
    }

    &.mbsc-segmented-item:before {
      border-color: rgba($selectbox-contrast, 0.2);
    }

    &.mbsc-segmented-item.mbsc-focus .mbsc-segmented-selectbox {
      box-shadow: 0 0 0 0.0625em rgba($selectbox-contrast, 0.5) inset;
    }

    &.mbsc-segmented-selectbox-inner {
      background: $selectbox;

      &.mbsc-selected {
        box-shadow: $shadow 3px 3px 8px -4px;
      }
    }

    &.mbsc-segmented-button.mbsc-button {
      background: transparent;
      color: $selectbox-contrast;
    }

    /* Inside popup and calendar header */
    @if ($is-dark) {
      &.mbsc-datepicker .mbsc-segmented,
      &.mbsc-popup &.mbsc-segmented,
      &.mbsc-calendar-header &.mbsc-segmented {
        background: lighten($background, 17%);
      }
    }

    /* Color presets */

    &.mbsc-segmented-primary {
      background: $mbsc-ios-primary;
    }

    &.mbsc-segmented-secondary {
      background: $mbsc-ios-secondary;
    }

    &.mbsc-segmented-success {
      background: $mbsc-ios-success;
    }

    &.mbsc-segmented-warning {
      background: $mbsc-ios-warning;
    }

    &.mbsc-segmented-danger {
      background: $mbsc-ios-danger;
    }

    &.mbsc-segmented-info {
      background: $mbsc-ios-info;
    }

    &.mbsc-segmented-light {
      background: $mbsc-ios-light;
    }

    &.mbsc-segmented-dark {
      background: $mbsc-ios-dark;
    }
  }
}



@mixin mbsc-material-segmented($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $bg-param: map-get($params, 'form-background');
  $acc-param: map-get($params, 'form-accent');
  $text-param: map-get($params, 'form-text');
  $background: if($bg-param, $bg-param, $background);
  $accent: if($acc-param, $acc-param, $accent);
  $text: if($text-param, $text-param, $text);

  $button-color: '';
  $button-background: '';
  $button-text: '';

  @if (lightness($background) > 50%) {
    $button-background: darken($background, 19%);
    $button-color: darken($text, 36%);
    $button-text: darken($text, 36%);
  } @else {
    $button-background: lighten($background, 17%);
    $button-color: lighten($text, 24%);
    $button-text: lighten($text, 24%);
  }

  .mbsc-#{$theme} {
    &.mbsc-segmented-button.mbsc-button {
      border-color: $accent;
      color: $button-text;

      &.mbsc-hover,
      &.mbsc-active {
        background: rgba($button-color, 0.2);
      }

      &.mbsc-selected {
        background: $accent;
        color: $background;
      }

      &.mbsc-focus::after {
        background: rgba($button-text, 0.2);
      }
    }
  }
}



@mixin mbsc-windows-segmented($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $bg-param: map-get($params, 'form-background');
  $text-param: map-get($params, 'form-text');
  $acc-param: map-get($params, 'form-accent');

  $background: if($bg-param, $bg-param, $background);
  $text: if($text-param, $text-param, $text);
  $accent: if($acc-param, $acc-param, $accent);

  $button-border: '';
  $button-hover: '';
  $button-active: '';
  $border: '';
  $form-background: '';
  $button-bg: '';

  @if (lightness($background) > 50%) {
    $button-border: darken($background, 40%);
    $button-hover: darken($background, 17%);
    $button-active: lighten($text, 28%);
    $button-bg: darken($background, 20%);
    $border: darken($background, 10%);
    $form-background: lighten($background, 13%);
  } @else {
    $button-border: lighten($background, 35%);
    $button-active: darken($text, 33%);
    $button-hover: lighten($background, 17%);
    $button-bg: lighten($background, 15%);
    $border: lighten($background, 10%);
    $form-background: darken($background, 12%);
  }

  .mbsc-#{$theme} {
    &.mbsc-segmented-button.mbsc-button {
      background: $button-bg;
      color: $text;

      &.mbsc-hover {
        background: $button-hover;
      }

      &.mbsc-active {
        background: $button-hover;
      }

      &.mbsc-selected {
        background: $button-active;
        color: $form-background;
      }
    }
  }
}

// select component


@mixin mbsc-ios-select($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $item-color: hsl(hue($text), saturation($text), 62%);

  $border-color: '';
  $clear: '';
  $clear-hover: '';
  $popup-background: '';

  @if (lightness($background) > 50%) {
    $border-color: darken($background, 17%);
    $clear: $border-color;
    $clear-hover: darken($border-color, 10%);
    $popup-background: adjust-hue(darken(saturate($background, 24%), 1%), 240deg);
  } @else {
    $border-color: lighten($background, 20%);
    $clear: lighten($background, 25%);
    $clear-hover: lighten($border-color, 10%);
    $popup-background: lighten($background, 11%);
  }

  .mbsc-#{$theme} {
    &.mbsc-select-scroller-inline {
      border-color: $border-color;
    }

    &.mbsc-select-filter-cont {
      background-color: $popup-background;
    }

    &.mbsc-select-filter-clear {
      color: $clear;

      &:hover {
        color: $clear-hover;
      }
    }

    &.mbsc-select-empty-text {
      color: $item-color;
    }

    &.mbsc-select-group-wheel {
      border-color: $border-color;
    }

    &.mbsc-select-group-wheel-multi .mbsc-scroller-wheel-item-3d {
      color: $text;
    }

    &.mbsc-select-group-wheel-multi .mbsc-selected-3d {
      color: $accent;
    }
  }
}



@mixin mbsc-material-select($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  $clear-icon: '';
  $clear-icon-hover: '';

  @if (lightness($background) > 50%) {
    $clear-icon: lighten($text, 50%);
    $clear-icon-hover: darken($clear-icon, 20%);
  } @else {
    $clear-icon: darken($text, 50%);
    $clear-icon-hover: lighten($clear-icon, 20%);
  }

  .mbsc-#{$theme} {
    &.mbsc-select-scroller-inline {
      background-color: $background;
      color: $text;
    }

    &.mbsc-select-filter-clear {
      color: $clear-icon;

      &:hover {
        color: $clear-icon-hover;
      }
    }

    &.mbsc-select-scroller.mbsc-scroller-pointer .mbsc-scroller-wheel-item.mbsc-selected {
      color: $accent;
      background: rgba(0, 0, 0, 0.15);
    }

    &.mbsc-select-group-wheel-multi .mbsc-selected {
      color: $accent;
    }
  }
}



@mixin mbsc-windows-select($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');
  $accent: map-get($params, 'accent');

  .mbsc-#{$theme} {
    &.mbsc-select-scroller-inline {
      background-color: $background;
      color: $text;
    }

    &.mbsc-select-scroller.mbsc-scroller-pointer .mbsc-scroller-wheel-item.mbsc-selected,
    &.mbsc-select-group-wheel-multi .mbsc-selected {
      color: $accent;
    }
  }
}

// stepper component


@mixin mbsc-ios-stepper($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');

  $bg-param: map-get($params, 'form-background');
  $text-param: map-get($params, 'form-text');

  $background: if($bg-param, $bg-param, $background);
  $text: if($text-param, $text-param, $text);

  $button-background: '';
  $border: '';

  // Light background
  @if (lightness($background) > 50%) {
    $button-background: darken($background, 10%);
    $border: #000;
  }

  // Dark background
  @else {
    $button-background: lighten($background, 17%);
    $border: #fff;
  }

  .mbsc-#{$theme} {
    &.mbsc-stepper-input {
      border-color: $button-background;
      color: $text;
    }

    &.mbsc-stepper-input.mbsc-disabled {
      color: rgba($text, 0.2);
    }

    &.mbsc-stepper-button {
      background: $button-background;
      color: $text;
    }

    &.mbsc-stepper-plus:before {
      border-color: rgba($border, 0.2);
    }
  }
}



@mixin mbsc-material-stepper($theme, $params) {
  $text: map-get($params, 'text');
  $background: map-get($params, 'background');

  $bg-param: map-get($params, 'form-background');
  $text-param: map-get($params, 'form-text');

  $background: if($bg-param, $bg-param, $background);
  $text: if($text-param, $text-param, $text);

  $button-color: '';
  $text-color: '';
  $text-color-disabled: '';

  // Light background
  @if (lightness($background) > 50%) {
    $button-color: darken($background, 16%);
    $text-color: lighten($text, 17%);
    $text-color-disabled: darken($background, 30%);
  }

  // Dark background
  @else {
    $button-color: lighten($background, 35%);
    $text-color: darken($text, 24%);
    $text-color-disabled: lighten($background, 45%);
  }

  .mbsc-#{$theme} {
    &.mbsc-stepper-input {
      border-color: $button-color;
      color: $text-color;
    }

    &.mbsc-stepper-input.mbsc-disabled {
      color: $text-color-disabled;
    }

    &.mbsc-stepper-button {
      background: $button-color;
      color: $button-color;
    }

    &.mbsc-stepper-inner {
      color: $background;
    }

    &.mbsc-color-none .mbsc-stepper-inner {
      color: $text;
    }

    &.mbsc-stepper-button.mbsc-disabled {
      background: $button-color;
    }

    &.mbsc-stepper-button.mbsc-disabled .mbsc-stepper-inner {
      color: $text-color-disabled;
    }
  }
}



@mixin mbsc-windows-stepper($theme, $params) {
  $text: map-get($params, 'text');
  $background: map-get($params, 'background');

  $bg-param: map-get($params, 'form-background');
  $text-param: map-get($params, 'form-text');

  $background: if($bg-param, $bg-param, $background);
  $text: if($text-param, $text-param, $text);

  $border-color: '';
  $disabled-text-color: '';
  $disabled-button-color: '';
  $hover: '';

  // Light background
  @if (lightness($background) > 50%) {
    $border-color: darken($background, 20%);
    $disabled-text-color: darken($background, 30%);
    $disabled-button-color: darken($background, 20%);
    $hover: #000;
  }

  // Dark background
  @else {
    $border-color: lighten($background, 15%);
    $disabled-text-color: lighten($background, 40%);
    $disabled-button-color: lighten($background, 15%);
    $hover: #fff;
  }

  .mbsc-#{$theme} {
    &.mbsc-stepper-input {
      border-color: $border-color;
      color: $text;
    }

    &.mbsc-stepper-input.mbsc-disabled {
      color: $disabled-text-color;
    }

    &.mbsc-stepper-button {
      color: $border-color;
    }

    &.mbsc-stepper-button.mbsc-hover:before {
      background: rgba($hover, 0.2);
    }

    &.mbsc-stepper-button.mbsc-disabled {
      background: $disabled-button-color;
    }

    &.mbsc-stepper-inner {
      color: $text;
    }

    &.mbsc-stepper-button.mbsc-disabled .mbsc-stepper-inner {
      color: $disabled-text-color;
    }
  }
}

// switch component


@mixin mbsc-ios-switch($theme, $params) {
  $background: map-get($params, 'background');
  $text: map-get($params, 'text');

  $bg-param: map-get($params, 'form-background');
  $ac-param: map-get($params, 'form-accent');
  $text-param: map-get($params, 'form-text');

  $background: if($bg-param, $bg-param, $background);
  $switch-track-checked: if($ac-param, $ac-param, #4cd764);
  $text: if($text-param, $text-param, $text);

  $switch-track: '';

  @if (lightness($background) > 50%) {
    $switch-track: darken($background, 7%);
  } @else {
    $switch-track: lighten($background, 22%);
  }

  .mbsc-#{$theme} {
    &.mbsc-switch-track {
      // when the switch is not checked
      &:after {
        background: $switch-track;
      }

      &.mbsc-focus:after {
        box-shadow: 0 0 0 0.125em rgba($text, 0.5);
      }

      // when the switch is checked
      &.mbsc-checked:after {
        background: $switch-track-checked;
      }
    }
  }
}



@mixin mbsc-material-switch($theme, $params) {
  $background: map-get($params, 'background');
  $accent: map-get($params, 'accent');

  $bg-param: map-get($params, 'form-background');
  $ac-param: map-get($params, 'form-accent');

  $background: if($bg-param, $bg-param, $background);
  $accent: if($ac-param, $ac-param, $accent);

  $track: '';
  $track-disabled: '';
  $handle-disabled: '';
  $handle: '';
  $bg-contrast: '';

  @if (lightness($background) > 50%) {
    $track: darken($background, 23%);
    $track-disabled: darken($background, 9%);
    $handle-disabled: darken($background, 20%);
    $handle: lighten($background, 5%);
    $bg-contrast: #000;
  } @else {
    $track: lighten($background, 17%);
    $track-disabled: lighten($background, 17%);
    $handle-disabled: lighten($background, 16%);
    $handle: lighten($background, 53%);
    $bg-contrast: #fff;
  }

  .mbsc-#{$theme} {
    &.mbsc-switch-track {
      background: $track;

      &.mbsc-checked {
        background: rgba($accent, 0.3);
      }

      &.mbsc-disabled {
        background: $track-disabled;
      }
    }

    &.mbsc-switch-handle {
      background: $handle;
      box-shadow: 0 3px 1px -2px rgba($bg-contrast, 0.2), 0 1px 5px 0 rgba($bg-contrast, 0.12);

      &.mbsc-checked {
        background: $accent;
      }

      &.mbsc-disabled {
        background: $handle-disabled;
      }
    }

    &.mbsc-switch-handle:before {
      background: rgba($bg-contrast, 0.1);
    }
  }
}



@mixin mbsc-windows-switch($theme, $params) {
  $background: map-get($params, 'background');
  $accent: map-get($params, 'accent');
  $text: map-get($params, 'text');

  $bg-param: map-get($params, 'form-background');
  $ac-param: map-get($params, 'form-accent');
  $text-param: map-get($params, 'form-text');

  $background: if($bg-param, $bg-param, $background);
  $accent: if($ac-param, $ac-param, $accent);
  $text: if($text-param, $text-param, $text);

  $active: '';
  $focus: '';

  @if (lightness($background) > 50%) {
    $active: darken($background, 55%);
    $focus: lighten($text, 45%);
  } @else {
    $active: lighten($background, 55%);
    $focus: darken($text, 45%);
  }

  .mbsc-#{$theme} {
    // Track

    &.mbsc-switch-track {
      &.mbsc-checked:before {
        border-color: $accent;
        background: $accent;
      }

      &:before,
      &.mbsc-disabled:before {
        border-color: $text;
        background: none;
      }

      &.mbsc-active:before {
        border-color: $active;
        background: $active;
      }

      &.mbsc-focus:after {
        border-color: $focus;
      }
    }

    // Handle

    &.mbsc-switch-handle {
      background: $text;

      &.mbsc-checked {
        background: $background;
      }

      &.mbsc-disabled {
        background: $text;
      }
    }
  }
}


@mixin mbsc-mobiscroll-theme($theme, $colors) {
  @include mbsc-mobiscroll-button($theme, $colors);
  // @include mbsc-mobiscroll-calendar($theme, $colors);
  @include mbsc-mobiscroll-calendar-view($theme, $colors);
  @include mbsc-mobiscroll-checkbox($theme, $colors);
  // @include mbsc-mobiscroll-datepicker($theme, $colors);
  // @include mbsc-mobiscroll-datetime($theme, $colors);
  @include mbsc-mobiscroll-eventcalendar($theme, $colors);
  @include mbsc-mobiscroll-form-controls($theme, $colors);
  @include mbsc-mobiscroll-input($theme, $colors);
  @include mbsc-mobiscroll-list($theme, $colors);
  @include mbsc-mobiscroll-page($theme, $colors);
  @include mbsc-mobiscroll-popup($theme, $colors);
  @include mbsc-mobiscroll-radio($theme, $colors);
  @include mbsc-mobiscroll-segmented($theme, $colors);
  // @include mbsc-mobiscroll-select($theme, $colors);
  // @include mbsc-mobiscroll-scroller($theme, $colors);
  // @include mbsc-mobiscroll-stepper($theme, $colors);
  @include mbsc-mobiscroll-switch($theme, $colors);
}

// Theme builder function for ios theme
@mixin mbsc-ios-theme($theme, $colors) {
  @include mbsc-ios-button($theme, $colors);
  @include mbsc-ios-calendar($theme, $colors);
  @include mbsc-ios-calendar-view($theme, $colors);
  @include mbsc-ios-checkbox($theme, $colors);
  @include mbsc-ios-datepicker($theme, $colors);
  @include mbsc-ios-datetime($theme, $colors);
  @include mbsc-ios-eventcalendar($theme, $colors);
  @include mbsc-ios-form-controls($theme, $colors);
  @include mbsc-ios-input($theme, $colors);
  @include mbsc-ios-list($theme, $colors);
  @include mbsc-ios-page($theme, $colors);
  @include mbsc-ios-popup($theme, $colors);
  @include mbsc-ios-radio($theme, $colors);
  @include mbsc-ios-scroller($theme, $colors);
  @include mbsc-ios-segmented($theme, $colors);
  @include mbsc-ios-select($theme, $colors);
  @include mbsc-ios-stepper($theme, $colors);
  @include mbsc-ios-switch($theme, $colors);
}

// Theme builder function for material theme
@mixin mbsc-material-theme($theme, $colors) {
  @include mbsc-material-button($theme, $colors);
  @include mbsc-material-calendar($theme, $colors);
  @include mbsc-material-calendar-view($theme, $colors);
  @include mbsc-material-checkbox($theme, $colors);
  @include mbsc-material-datepicker($theme, $colors);
  @include mbsc-material-datetime($theme, $colors);
  @include mbsc-material-eventcalendar($theme, $colors);
  @include mbsc-material-form-controls($theme, $colors);
  @include mbsc-material-input($theme, $colors);
  @include mbsc-material-list($theme, $colors);
  @include mbsc-material-page($theme, $colors);
  @include mbsc-material-popup($theme, $colors);
  @include mbsc-material-radio($theme, $colors);
  @include mbsc-material-scroller($theme, $colors);
  @include mbsc-material-segmented($theme, $colors);
  @include mbsc-material-select($theme, $colors);
  @include mbsc-material-stepper($theme, $colors);
  @include mbsc-material-switch($theme, $colors);
}

// Theme builder function for windows theme
@mixin mbsc-windows-theme($theme, $colors) {
  @include mbsc-windows-button($theme, $colors);
  @include mbsc-windows-calendar($theme, $colors);
  @include mbsc-windows-calendar-view($theme, $colors);
  @include mbsc-windows-checkbox($theme, $colors);
  @include mbsc-windows-datepicker($theme, $colors);
  @include mbsc-windows-datetime($theme, $colors);
  @include mbsc-windows-eventcalendar($theme, $colors);
  @include mbsc-windows-form-controls($theme, $colors);
  @include mbsc-windows-input($theme, $colors);
  @include mbsc-windows-list($theme, $colors);
  @include mbsc-windows-page($theme, $colors);
  @include mbsc-windows-popup($theme, $colors);
  @include mbsc-windows-radio($theme, $colors);
  @include mbsc-windows-scroller($theme, $colors);
  @include mbsc-windows-segmented($theme, $colors);
  @include mbsc-windows-select($theme, $colors);
  @include mbsc-windows-stepper($theme, $colors);
  @include mbsc-windows-switch($theme, $colors);
}

@mixin mbsc-custom-theme($theme, $base-theme, $colors) {
  @if $base-theme== 'ios' {
    @include mbsc-ios-theme($theme, $colors);
  }

  @if $base-theme== 'material' {
    @include mbsc-material-theme($theme, $colors);
  }

  @if $base-theme== 'windows' {
    @include mbsc-windows-theme($theme, $colors);
  }
}

@font-face {
  font-family: 'Mobiscroll';
  src: url(data:application/x-font-woff;base64,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)
    format('woff');
  font-weight: normal;
  font-style: normal;
}

.mbsc-font-icon:before {
  font-family: 'Mobiscroll';
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Icons */

.mbsc-icon-aid::before {
  content: '\ea01';
}
.mbsc-icon-airplane::before {
  content: '\ea02';
}
.mbsc-icon-alarm2::before {
  content: '\ea03';
}
.mbsc-icon-arrow-down2::before {
  content: '\ea04';
}
.mbsc-icon-arrow-down5::before {
  content: '\ea05';
}
.mbsc-icon-arrow-left2::before {
  content: '\ea06';
}
.mbsc-icon-arrow-left5::before {
  content: '\ea07';
}
.mbsc-icon-arrow-right2::before {
  content: '\ea08';
}
.mbsc-icon-arrow-right5::before {
  content: '\ea09';
}
.mbsc-icon-arrow-up2::before {
  content: '\ea0a';
}
.mbsc-icon-arrow-up5::before {
  content: '\ea0b';
}
.mbsc-icon-attachment::before {
  content: '\ea0c';
}
.mbsc-icon-bars::before {
  content: '\ea0d';
}
.mbsc-icon-book::before {
  content: '\ea0e';
}
.mbsc-icon-brightness-contrast::before {
  content: '\ea0f';
}
.mbsc-icon-bubble::before {
  content: '\ea10';
}
.mbsc-icon-bubbles::before {
  content: '\ea11';
}
.mbsc-icon-bullhorn::before {
  content: '\ea12';
}
.mbsc-icon-calendar::before {
  content: '\ea13';
}
.mbsc-icon-camera::before {
  content: '\ea14';
}
.mbsc-icon-cart::before {
  content: '\ea15';
}
.mbsc-icon-checkmark::before {
  content: '\ea16';
}
.mbsc-icon-clock::before {
  content: '\ea17';
}
.mbsc-icon-close::before {
  content: '\ea18';
}
.mbsc-icon-cloud::before {
  content: '\ea19';
}
.mbsc-icon-cloud-download::before {
  content: '\ea1a';
}
.mbsc-icon-cloud-upload::before {
  content: '\ea1b';
}
.mbsc-icon-cogs::before {
  content: '\ea1c';
}
.mbsc-icon-compass::before {
  content: '\ea1d';
}
.mbsc-icon-connection::before {
  content: '\ea1e';
}
.mbsc-icon-copy2::before {
  content: '\ea1f';
}
.mbsc-icon-copy3::before {
  content: '\ea20';
}
.mbsc-icon-credit::before {
  content: '\ea21';
}
.mbsc-icon-disk::before {
  content: '\ea22';
}
.mbsc-icon-download::before {
  content: '\ea23';
}
.mbsc-icon-drawer::before {
  content: '\ea24';
}
.mbsc-icon-droplet::before {
  content: '\ea25';
}
.mbsc-icon-earth::before {
  content: '\ea26';
}
.mbsc-icon-eye::before {
  content: '\ea27';
}
.mbsc-icon-eye-blocked::before {
  content: '\ea28';
}
.mbsc-icon-fa-dollar::before {
  content: '\ea29';
}
.mbsc-icon-fa-ellipsis-h::before {
  content: '\ea2a';
}
.mbsc-icon-fa-facebook::before {
  content: '\ea2b';
}
.mbsc-icon-fa-globe::before {
  content: '\ea2c';
}
.mbsc-icon-fa-google::before {
  content: '\ea2d';
}
.mbsc-icon-fa-google-plus::before {
  content: '\ea2e';
}
.mbsc-icon-fa-leaf::before {
  content: '\ea2f';
}
.mbsc-icon-fa-mail-reply::before {
  content: '\ea30';
}
.mbsc-icon-fa-retweet::before {
  content: '\ea31';
}
.mbsc-icon-fa-rotate-left::before {
  content: '\ea32';
}
.mbsc-icon-fa-twitter::before {
  content: '\ea33';
}
.mbsc-icon-feed2::before {
  content: '\ea34';
}
.mbsc-icon-file4::before {
  content: '\ea35';
}
.mbsc-icon-film::before {
  content: '\ea36';
}
.mbsc-icon-flag::before {
  content: '\ea37';
}
.mbsc-icon-folder::before {
  content: '\ea38';
}
.mbsc-icon-forward::before {
  content: '\ea39';
}
.mbsc-icon-foundation-mail::before {
  content: '\ea3a';
}
.mbsc-icon-foundation-minus-circle::before {
  content: '\ea3b';
}
.mbsc-icon-gift::before {
  content: '\ea3c';
}
.mbsc-icon-globe::before {
  content: '\ea3d';
}
.mbsc-icon-heart::before {
  content: '\ea3e';
}
.mbsc-icon-heart2::before {
  content: '\ea3f';
}
.mbsc-icon-history::before {
  content: '\ea40';
}
.mbsc-icon-home::before {
  content: '\ea41';
}
.mbsc-icon-image2::before {
  content: '\ea42';
}
.mbsc-icon-ion-android-social-user::before {
  content: '\ea43';
}
.mbsc-icon-ion-android-system-windows::before {
  content: '\ea44';
}
.mbsc-icon-ion-bluetooth::before {
  content: '\ea45';
}
.mbsc-icon-ion-close-circled::before {
  content: '\ea46';
}
.mbsc-icon-ion-email::before {
  content: '\ea47';
}
.mbsc-icon-ion-ios7-arrow-back::before {
  content: '\ea48';
}
.mbsc-icon-ion-ios7-arrow-forward::before {
  content: '\ea49';
}
.mbsc-icon-ion-ios7-checkmark-empty::before {
  content: '\ea4a';
}
.mbsc-icon-ion-ios7-close-empty::before {
  content: '\ea4b';
}
.mbsc-icon-ion-ios7-close-outline::before {
  content: '\ea4c';
}
.mbsc-icon-ion-ios7-email::before {
  content: '\ea4d';
}
.mbsc-icon-ion-ios7-information-outline::before {
  content: '\ea4e';
}
.mbsc-icon-ion-ios7-plus-empty::before {
  content: '\ea4f';
}
.mbsc-icon-ion-iphone::before {
  content: '\ea50';
}
.mbsc-icon-ion-navigate::before {
  content: '\ea51';
}
.mbsc-icon-ion-social-facebook::before {
  content: '\ea52';
}
.mbsc-icon-ion-usb::before {
  content: '\ea53';
}
.mbsc-icon-key2::before {
  content: '\ea54';
}
.mbsc-icon-library::before {
  content: '\ea55';
}
.mbsc-icon-line-bubble::before {
  content: '\ea56';
}
.mbsc-icon-line-calendar::before {
  content: '\ea57';
}
.mbsc-icon-line-food::before {
  content: '\ea58';
}
.mbsc-icon-line-heart::before {
  content: '\ea59';
}
.mbsc-icon-line-key::before {
  content: '\ea5a';
}
.mbsc-icon-line-mail::before {
  content: '\ea5b';
}
.mbsc-icon-line-megaphone::before {
  content: '\ea5c';
}
.mbsc-icon-line-music::before {
  content: '\ea5d';
}
.mbsc-icon-line-note::before {
  content: '\ea5e';
}
.mbsc-icon-line-paperplane::before {
  content: '\ea5f';
}
.mbsc-icon-line-params::before {
  content: '\ea60';
}
.mbsc-icon-line-phone::before {
  content: '\ea61';
}
.mbsc-icon-line-settings::before {
  content: '\ea62';
}
.mbsc-icon-line-star::before {
  content: '\ea63';
}
.mbsc-icon-line-t-shirt::before {
  content: '\ea64';
}
.mbsc-icon-line-tag::before {
  content: '\ea65';
}
.mbsc-icon-line-user::before {
  content: '\ea66';
}
.mbsc-icon-line-world::before {
  content: '\ea67';
}
.mbsc-icon-link::before {
  content: '\ea68';
}
.mbsc-icon-location::before {
  content: '\ea69';
}
.mbsc-icon-lock::before {
  content: '\ea6a';
}
.mbsc-icon-lock2::before {
  content: '\ea6b';
}
.mbsc-icon-loop2::before {
  content: '\ea6c';
}
.mbsc-icon-map::before {
  content: '\ea6d';
}
.mbsc-icon-material-arrow-back::before {
  content: '\ea6e';
}
.mbsc-icon-material-arrow-forward::before {
  content: '\ea6f';
}
.mbsc-icon-material-backspace::before {
  content: '\ea70';
}
.mbsc-icon-material-brightness-medium::before {
  content: '\ea71';
}
.mbsc-icon-material-check::before {
  content: '\ea72';
}
.mbsc-icon-material-check-box-outline-blank::before {
  content: '\ea73';
}
.mbsc-icon-material-close::before {
  content: '\ea74';
}
.mbsc-icon-material-crop::before {
  content: '\ea75';
}
.mbsc-icon-material-date-range::before {
  content: '\ea76';
}
.mbsc-icon-material-email::before {
  content: '\ea77';
}
.mbsc-icon-material-equalizer::before {
  content: '\ea78';
}
.mbsc-icon-material-event-note::before {
  content: '\ea79';
}
.mbsc-icon-material-explore::before {
  content: '\ea7a';
}
.mbsc-icon-material-filter::before {
  content: '\ea7b';
}
.mbsc-icon-material-filter-list::before {
  content: '\ea7c';
}
.mbsc-icon-material-format-bold::before {
  content: '\ea7d';
}
.mbsc-icon-material-format-ital::before {
  content: '\ea7e';
}
.mbsc-icon-material-format-list-numbered::before {
  content: '\ea7f';
}
.mbsc-icon-material-format-paint::before {
  content: '\ea80';
}
.mbsc-icon-material-format-underline::before {
  content: '\ea81';
}
.mbsc-icon-material-inbox::before {
  content: '\ea82';
}
.mbsc-icon-material-iso::before {
  content: '\ea83';
}
.mbsc-icon-material-keyboard-arrow-down::before {
  content: '\ea84';
}
.mbsc-icon-material-keyboard-arrow-left::before {
  content: '\ea85';
}
.mbsc-icon-material-keyboard-arrow-right::before {
  content: '\ea86';
}
.mbsc-icon-material-keyboard-arrow-up::before {
  content: '\ea87';
}
.mbsc-icon-material-label::before {
  content: '\ea88';
}
.mbsc-icon-material-language::before {
  content: '\ea89';
}
.mbsc-icon-material-list::before {
  content: '\ea8a';
}
.mbsc-icon-material-menu::before {
  content: '\ea8b';
}
.mbsc-icon-material-message::before {
  content: '\ea8c';
}
.mbsc-icon-material-more-horiz::before {
  content: '\ea8d';
}
.mbsc-icon-material-music-note::before {
  content: '\ea8e';
}
.mbsc-icon-material-notifications::before {
  content: '\ea8f';
}
.mbsc-icon-material-palette::before {
  content: '\ea90';
}
.mbsc-icon-material-pause::before {
  content: '\ea91';
}
.mbsc-icon-material-people::before {
  content: '\ea92';
}
.mbsc-icon-material-phone-iphone::before {
  content: '\ea93';
}
.mbsc-icon-material-photo-size-select-large::before {
  content: '\ea94';
}
.mbsc-icon-material-play-arrow::before {
  content: '\ea95';
}
.mbsc-icon-material-repeat::before {
  content: '\ea96';
}
.mbsc-icon-material-rotate-right::before {
  content: '\ea97';
}
.mbsc-icon-material-search::before {
  content: '\ea98';
}
.mbsc-icon-material-share::before {
  content: '\ea99';
}
.mbsc-icon-material-shuffle::before {
  content: '\ea9a';
}
.mbsc-icon-material-skip-next::before {
  content: '\ea9b';
}
.mbsc-icon-material-skip-previous::before {
  content: '\ea9c';
}
.mbsc-icon-material-star::before {
  content: '\ea9d';
}
.mbsc-icon-material-star-outline::before {
  content: '\ea9e';
}
.mbsc-icon-material-stop::before {
  content: '\ea9f';
}
.mbsc-icon-material-system-update::before {
  content: '\eaa0';
}
.mbsc-icon-material-texture::before {
  content: '\eaa1';
}
.mbsc-icon-material-today::before {
  content: '\eaa2';
}
.mbsc-icon-material-tune::before {
  content: '\eaa3';
}
.mbsc-icon-material-tv::before {
  content: '\eaa4';
}
.mbsc-icon-material-vertical-align-bottom::before {
  content: '\eaa5';
}
.mbsc-icon-material-view-day::before {
  content: '\eaa6';
}
.mbsc-icon-material-wb-auto::before {
  content: '\eaa7';
}
.mbsc-icon-material-zoom-in::before {
  content: '\eaa8';
}
.mbsc-icon-material-zoom-out::before {
  content: '\eaa9';
}
.mbsc-icon-meteo-Celsius::before {
  content: '\eaaa';
}
.mbsc-icon-meteo-Fahrenheit::before {
  content: '\eaab';
}
.mbsc-icon-meteo-cloud::before {
  content: '\eaac';
}
.mbsc-icon-meteo-cloud2::before {
  content: '\eaad';
}
.mbsc-icon-meteo-cloud3::before {
  content: '\eaae';
}
.mbsc-icon-meteo-cloud4::before {
  content: '\eaaf';
}
.mbsc-icon-meteo-cloud5::before {
  content: '\eab0';
}
.mbsc-icon-meteo-cloudy::before {
  content: '\eab1';
}
.mbsc-icon-meteo-cloudy2::before {
  content: '\eab2';
}
.mbsc-icon-meteo-cloudy3::before {
  content: '\eab3';
}
.mbsc-icon-meteo-cloudy4::before {
  content: '\eab4';
}
.mbsc-icon-meteo-compass::before {
  content: '\eab5';
}
.mbsc-icon-meteo-lightning::before {
  content: '\eab6';
}
.mbsc-icon-meteo-lightning2::before {
  content: '\eab7';
}
.mbsc-icon-meteo-lightning3::before {
  content: '\eab8';
}
.mbsc-icon-meteo-lightning4::before {
  content: '\eab9';
}
.mbsc-icon-meteo-lightning5::before {
  content: '\eaba';
}
.mbsc-icon-meteo-lines::before {
  content: '\eabb';
}
.mbsc-icon-meteo-moon::before {
  content: '\eabc';
}
.mbsc-icon-meteo-moon2::before {
  content: '\eabd';
}
.mbsc-icon-meteo-none::before {
  content: '\eabe';
}
.mbsc-icon-meteo-rainy::before {
  content: '\eabf';
}
.mbsc-icon-meteo-rainy2::before {
  content: '\eac0';
}
.mbsc-icon-meteo-rainy3::before {
  content: '\eac1';
}
.mbsc-icon-meteo-rainy4::before {
  content: '\eac2';
}
.mbsc-icon-meteo-snowflake::before {
  content: '\eac3';
}
.mbsc-icon-meteo-snowy::before {
  content: '\eac4';
}
.mbsc-icon-meteo-snowy2::before {
  content: '\eac5';
}
.mbsc-icon-meteo-snowy3::before {
  content: '\eac6';
}
.mbsc-icon-meteo-snowy4::before {
  content: '\eac7';
}
.mbsc-icon-meteo-snowy5::before {
  content: '\eac8';
}
.mbsc-icon-meteo-sun::before {
  content: '\eac9';
}
.mbsc-icon-meteo-sun2::before {
  content: '\eaca';
}
.mbsc-icon-meteo-sun3::before {
  content: '\eacb';
}
.mbsc-icon-meteo-sunrise::before {
  content: '\eacc';
}
.mbsc-icon-meteo-thermometer::before {
  content: '\eacd';
}
.mbsc-icon-meteo-weather::before {
  content: '\eace';
}
.mbsc-icon-meteo-weather2::before {
  content: '\eacf';
}
.mbsc-icon-meteo-weather3::before {
  content: '\ead0';
}
.mbsc-icon-meteo-weather4::before {
  content: '\ead1';
}
.mbsc-icon-meteo-weather5::before {
  content: '\ead2';
}
.mbsc-icon-meteo-wind::before {
  content: '\ead3';
}
.mbsc-icon-meteo-windy::before {
  content: '\ead4';
}
.mbsc-icon-meteo-windy2::before {
  content: '\ead5';
}
.mbsc-icon-meteo-windy3::before {
  content: '\ead6';
}
.mbsc-icon-meteo-windy4::before {
  content: '\ead7';
}
.mbsc-icon-meteo-windy5::before {
  content: '\ead8';
}
.mbsc-icon-minus::before {
  content: '\ead9';
}
.mbsc-icon-mobile::before {
  content: '\eada';
}
.mbsc-icon-music::before {
  content: '\eadb';
}
.mbsc-icon-neutral::before {
  content: '\eadc';
}
.mbsc-icon-newspaper::before {
  content: '\eadd';
}
.mbsc-icon-office::before {
  content: '\eade';
}
.mbsc-icon-pause2::before {
  content: '\eadf';
}
.mbsc-icon-pencil::before {
  content: '\eae0';
}
.mbsc-icon-phone::before {
  content: '\eae1';
}
.mbsc-icon-play::before {
  content: '\eae2';
}
.mbsc-icon-play3::before {
  content: '\eae3';
}
.mbsc-icon-plus::before {
  content: '\eae4';
}
.mbsc-icon-print::before {
  content: '\eae5';
}
.mbsc-icon-redo2::before {
  content: '\eae6';
}
.mbsc-icon-remove::before {
  content: '\eae7';
}
.mbsc-icon-reply::before {
  content: '\eae8';
}
.mbsc-icon-sad::before {
  content: '\eae9';
}
.mbsc-icon-sad2::before {
  content: '\eaea';
}
.mbsc-icon-share::before {
  content: '\eaeb';
}
.mbsc-icon-smiley::before {
  content: '\eaec';
}
.mbsc-icon-smiley2::before {
  content: '\eaed';
}
.mbsc-icon-stack::before {
  content: '\eaee';
}
.mbsc-icon-star::before {
  content: '\eaef';
}
.mbsc-icon-star2::before {
  content: '\eaf0';
}
.mbsc-icon-star3::before {
  content: '\eaf1';
}
.mbsc-icon-stop2::before {
  content: '\eaf2';
}
.mbsc-icon-stopwatch::before {
  content: '\eaf3';
}
.mbsc-icon-stream-sync::before {
  content: '\eaf4';
}
.mbsc-icon-support::before {
  content: '\eaf5';
}
.mbsc-icon-tag::before {
  content: '\eaf6';
}
.mbsc-icon-thumbs-up::before {
  content: '\eaf7';
}
.mbsc-icon-thumbs-up2::before {
  content: '\eaf8';
}
.mbsc-icon-undo2::before {
  content: '\eaf9';
}
.mbsc-icon-unlocked::before {
  content: '\eafa';
}
.mbsc-icon-upload::before {
  content: '\eafb';
}
.mbsc-icon-user4::before {
  content: '\eafc';
}
.mbsc-icon-volume-high::before {
  content: '\eafd';
}
.mbsc-icon-volume-medium::before {
  content: '\eafe';
}
.mbsc-icon-volume-mute2::before {
  content: '\eaff';
}

.mbsc-icon {
  display: inline-block;
  vertical-align: middle;
  width: 1.5em;
  height: 1.5em;
  line-height: 1.5em;
  text-align: center;
  flex: 0 0 auto;
}

.mbsc-icon > svg {
  display: block;
  margin: 0 auto;
  width: 100%;
  height: 100%;
  fill: currentColor;
}

:root {
  --mbsc-safe-top: 0;
  --mbsc-safe-right: 0;
  --mbsc-safe-bottom: 0;
  --mbsc-safe-left: 0;

  @supports (top: constant(safe-area-inset-top)) {
    --mbsc-safe-top: constant(safe-area-inset-top);
    --mbsc-safe-right: constant(safe-area-inset-right);
    --mbsc-safe-bottom: constant(safe-area-inset-bottom);
    --mbsc-safe-left: constant(safe-area-inset-left);
  }

  @supports (top: env(safe-area-inset-top)) {
    --mbsc-safe-top: env(safe-area-inset-top);
    --mbsc-safe-right: env(safe-area-inset-right);
    --mbsc-safe-bottom: env(safe-area-inset-bottom);
    --mbsc-safe-left: env(safe-area-inset-left);
  }
}

.mbsc-font {
  font-family: -apple-system, 'Segoe UI', Roboto, sans-serif;
  font-size: 16px;
  font-weight: normal;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-text-size-adjust: 100%;
}

.mbsc-reset {
  margin: 0;
  padding: 0;
  border: 0;
  background: none;
  // border-radius: 0;
  // font-family: inherit;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.mbsc-resize {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: -1;
}

.mbsc-resize-i {
  position: absolute;
  left: 0;
  top: 0;
}

.mbsc-resize-y {
  width: 200%;
  height: 200%;
}

.mbsc-hidden {
  visibility: hidden;
}

.mbsc-hidden-content {
  position: absolute;
  width: 1px;
  height: 1px;
  overflow: hidden;
  user-select: none;
  clip: rect(1px, 1px, 1px, 1px);
  white-space: nowrap;
}

.mbsc-ltr {
  direction: ltr;
}

.mbsc-rtl {
  direction: rtl;
}

.mbsc-ripple {
  background: currentColor;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  border-radius: 1000em;
  pointer-events: none;
  transform: scale(0);
}

/* Flex util classes */

.mbsc-flex,
.mbsc-flex-col {
  display: flex;
}

.mbsc-flex-col {
  flex-direction: column;
}

.mbsc-flex-1-1 {
  flex: 1 1 auto;
}

.mbsc-flex-1-0 {
  flex: 1 0 auto;
}

.mbsc-flex-1-0-0 {
  flex: 1 0 0;
}

/* IE11 hack, where flex-basis auto does not work correctly */
@media all and (-ms-high-contrast: none) {
  .mbsc-flex-1-0-0 {
    flex: 1 0 auto;
  }
}

.mbsc-flex-none {
  flex: none;
}

@media (-webkit-min-device-pixel-ratio: 2) {
  .mbsc-hb,
  .mbsc-hb:before,
  .mbsc-hb:after {
    border-style: none; // Needed for Tailwind css compatibility
    border-width: 0.5px !important;
  }
}



@if ($mbsc-ios-theme and $mbsc-button) {
  .mbsc-ios {
    &.mbsc-button {
      padding: 0 0.5em;
      margin: 0.5em 0.25em;
      line-height: 2.25em;
      border-radius: 0.25em;
      transition: opacity 0.1s ease-out, background-color 0.1s ease-out;
    }

    &.mbsc-icon-button {
      padding: 0.5em;
      line-height: normal;
      border-radius: 4em;
    }

    &.mbsc-button.mbsc-hover {
      opacity: 0.7;
    }

    &.mbsc-button.mbsc-active {
      opacity: 0.5;
    }

    &.mbsc-button:disabled,
    &.mbsc-button.mbsc-disabled {
      opacity: 0.2;
    }

    &.mbsc-button-outline.mbsc-active {
      opacity: 1;
    }

    &.mbsc-ltr {
      &.mbsc-button-icon-start {
        padding-right: 0.375em;
      }

      &.mbsc-button-icon-end {
        padding-left: 0.375em;
      }
    }

    &.mbsc-rtl {
      &.mbsc-button-icon-start {
        padding-left: 0.375em;
      }

      &.mbsc-button-icon-end {
        padding-right: 0.375em;
      }
    }

    .mbsc-button-group,
    .mbsc-button-group-justified,
    .mbsc-button-group-block {
      margin: 0.5em 0.75em;
    }

    .mbsc-button-group-block {
      margin: 0.5em 1em;
    }
  }

  @include mbsc-ios-button('ios', $mbsc-ios-colors);
  @include mbsc-ios-button('ios-dark', $mbsc-ios-dark-colors);
}



@if ($mbsc-material-theme and $mbsc-button) {
  .mbsc-material {
    &.mbsc-button {
      min-width: 4.571429em;
      padding: 0.428572em 1.142858em;
      border-radius: 0.285715em;
      font-size: 0.875em;
      font-weight: 600;
      text-transform: uppercase;
      line-height: 1.714286em;
      transition: box-shadow 0.2s ease-out, background-color 0.2s ease-out;
      margin: 0.5em;
    }

    &.mbsc-button-outline {
      border: 1px solid;
    }

    &.mbsc-button .mbsc-icon {
      font-size: 1.142858em;
    }

    &.mbsc-icon-button {
      min-width: 0;
      padding: 0.5em;
      border-radius: 2em;
      font-size: 1em;
      line-height: normal;
    }

    &.mbsc-icon-button .mbsc-icon {
      width: 1.5em;
      height: 1.5em;
      font-size: 1em;
    }

    &.mbsc-button:disabled,
    &.mbsc-button.mbsc-disabled {
      opacity: 0.3;
    }

    &.mbsc-ltr {
      &.mbsc-button-icon-start {
        padding-right: 0.5em;
        margin-left: -0.25em;
      }

      &.mbsc-button-icon-end {
        padding-left: 0.5em;
        margin-right: -0.25em;
      }
    }

    &.mbsc-rtl {
      &.mbsc-button-icon-start {
        padding-left: 0.5em;
        margin-right: -0.25em;
      }

      &.mbsc-button-icon-end {
        padding-right: 0.5em;
        margin-left: -0.25em;
      }
    }

    .mbsc-button-group,
    .mbsc-button-group-justified {
      margin: 0.5em;
    }

    .mbsc-button-group-block {
      margin: 0.5em 1em;
    }
  }

  @include mbsc-material-button('material', $mbsc-material-colors);
  @include mbsc-material-button('material-dark', $mbsc-material-dark-colors);
}



@if ($mbsc-windows-theme and $mbsc-button) {
  .mbsc-windows {
    &.mbsc-button {
      padding: 0 0.5em;
      line-height: 1.75em;
      margin: 0.5em 0.25em;
      border: 0.125em solid transparent;
      transition: border-color 0.1s ease-out, background-color 0.1s ease-out;
    }

    &.mbsc-icon-button {
      width: 2.75em;
      height: 2em;
    }

    &.mbsc-button-flat {
      transition: background-color 0.1s ease-out;

      &.mbsc-hover {
        opacity: 0.7;
      }

      &.mbsc-active {
        opacity: 1;
      }
    }

    &.mbsc-button:disabled,
    &.mbsc-button.mbsc-disabled {
      opacity: 0.3;
    }

    &.mbsc-ltr {
      &.mbsc-button-icon-start {
        padding-right: 0.5em;
      }

      &.mbsc-button-icon-end {
        padding-left: 0.5em;
      }
    }

    &.mbsc-rtl {
      &.mbsc-button-icon-start {
        padding-left: 0.5em;
      }

      &.mbsc-button-icon-end {
        padding-right: 0.5em;
      }
    }

    .mbsc-button-group,
    .mbsc-button-group-justified {
      margin: 0.5em 0.75em;
    }

    .mbsc-button-group-block {
      margin: 0.5em 1em;
    }
  }

  @include mbsc-windows-button('windows', $mbsc-windows-colors);
  @include mbsc-windows-button('windows-dark', $mbsc-windows-dark-colors);
}


@if $mbsc-button {
  .mbsc-button {
    position: relative;
    z-index: 1;
    display: inline-block;
    vertical-align: middle;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    text-align: center;
    text-overflow: ellipsis;
    font-size: 1em;
    box-sizing: border-box;
    touch-action: manipulation;
    user-select: none;
    white-space: nowrap;
    cursor: pointer;
  }

  .mbsc-button:disabled,
  .mbsc-button.mbsc-disabled {
    cursor: not-allowed;
  }

  .mbsc-button:focus {
    outline: 0;
  }

  .mbsc-button::-moz-focus-inner {
    border: 0;
  }

  .mbsc-icon-button {
    width: 2.5em;
    height: 2.5em;
    padding: 0.5em;
    flex: 0 0 auto;
  }

  .mbsc-button-icon > *,
  .mbsc-button-icon > * > * {
    // For custom icon markup
    height: 100%;
  }

  .mbsc-button-icon-end {
    // Needed by javascript/jquery component, where end icon is before the button text in the markup
    order: 1;
  }

  .mbsc-button-group,
  .mbsc-button-group-block,
  .mbsc-button-group-justified {
    // Prevent margin collision
    border: 1px solid transparent;
  }

  .mbsc-button-group-block .mbsc-button,
  .mbsc-button-block {
    display: block;
    width: 100%;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  .mbsc-button-group-justified,
  .mbsc-button-group-justified mbsc-button {
    display: flex;
    flex-wrap: wrap;
  }

  .mbsc-button-group-justified .mbsc-button,
  .mbsc-button-group-justified mbsc-button {
    flex: 1 auto;
  }
}



@if ($mbsc-ios-theme and $mbsc-popup) {
  .mbsc-ios {
    &.mbsc-popup-wrapper-bottom-full {
      padding-top: 1em;
    }

    &.mbsc-popup-wrapper-top-full {
      padding-bottom: 1em;
    }

    // &.mbsc-popup-overlay {
    //   background: rgba(0, 0, 0, .2);
    // }

    &.mbsc-popup-top {
      border-bottom: 1px solid;
    }

    &.mbsc-popup-bottom {
      border-top: 1px solid;
    }

    &.mbsc-popup-body-center {
      border-radius: 0.9375em;
    }

    &.mbsc-popup-body-bottom-full {
      border-radius: 0.75em 0.75em 0 0;
    }

    &.mbsc-popup-body-top-full {
      border-radius: 0 0 0.75em 0.75em;
    }

    &.mbsc-popup-body-anchored {
      border-radius: 0.9375em;
    }

    &.mbsc-popup-arrow {
      border-radius: 0 0.375em;
    }

    &.mbsc-popup-header {
      position: absolute;
      z-index: 1;
      top: 0;
      right: 0;
      left: 0;
      line-height: 1.25em;
      padding: 0.75em 4.375em;
      font-weight: bold;
      text-align: center;
    }

    &.mbsc-popup-header-no-buttons {
      position: relative;
      border-bottom: 1px solid;
    }

    &.mbsc-popup-header-center {
      position: relative;
      padding: 0.75em 0.5em;
      border-bottom: 1px solid;
    }

    &.mbsc-popup-top-full .mbsc-popup-header,
    &.mbsc-popup-bottom-full .mbsc-popup-header {
      padding: 1.125em 4.375em;
    }

    &.mbsc-popup-buttons {
      order: -1;
      border-bottom: 1px solid;
    }

    &.mbsc-popup-buttons.mbsc-ltr {
      text-align: right;
    }

    &.mbsc-popup-buttons.mbsc-rtl {
      text-align: left;
    }

    &.mbsc-popup-button.mbsc-font {
      // line-height: 2.75em;
      margin-top: 0.25em;
      margin-bottom: 0.25em;
      z-index: 2;
    }

    &.mbsc-popup-button-primary {
      font-weight: bold;
    }

    &.mbsc-popup-button-close.mbsc-ltr {
      float: left;
    }

    &.mbsc-popup-button-close.mbsc-rtl {
      float: right;
    }

    &.mbsc-popup-buttons-anchored,
    &.mbsc-popup-buttons-top,
    &.mbsc-popup-buttons-bottom {
      padding: 0 0.25em;
    }

    &.mbsc-popup-buttons-top-full,
    &.mbsc-popup-buttons-bottom-full {
      padding: 0.375em 0.25em;
    }

    /* Button styling for centered popup and desktop styled top/bottom popup */

    &.mbsc-popup-buttons.mbsc-flex {
      order: 0;
      border: 0;
      padding: 0;
      background: none;
    }

    &.mbsc-popup-button-flex.mbsc-font {
      flex: 1 1 100%;
      display: block;
    }

    &.mbsc-popup-button-flex.mbsc-font.mbsc-button {
      opacity: 1;
      margin-top: 0.5em;
      margin-bottom: 0.5em;
    }

    &.mbsc-popup-button-flex.mbsc-font.mbsc-button-flat {
      background: none;
      border-top: 1px solid;
      border-radius: 0;
      margin: 0;
      line-height: 2.75em;
    }

    &.mbsc-popup-button-flex.mbsc-button-flat.mbsc-ltr {
      border-right-style: solid;
      border-right-width: 1px;
    }

    &.mbsc-popup-button-flex.mbsc-button-flat.mbsc-rtl {
      border-left-style: solid;
      border-left-width: 1px;
    }

    &.mbsc-popup-button-flex.mbsc-button-flat:last-child {
      border-left: 0;
      border-right: 0;
    }

    /* Desktop styling */

    &.mbsc-popup-body-round {
      border-radius: 0.9375em;
    }

    &.mbsc-popup-pointer {
      .mbsc-popup-body-round {
        border-radius: 0.5em;
      }
    }

    &.mbsc-popup-round {
      .mbsc-popup {
        border: 0;
      }

      // .mbsc-popup-body-bottom.mbsc-popup-short,
      .mbsc-popup-body-bottom-full {
        border-radius: 0.75em 0.75em 0 0;
      }

      // .mbsc-popup-body-top.mbsc-popup-short,
      .mbsc-popup-body-top-full {
        border-radius: 0 0 0.75em 0.75em;
      }
    }
  }

  @include mbsc-ios-popup('ios', $mbsc-ios-colors);
  @include mbsc-ios-popup('ios-dark', $mbsc-ios-dark-colors);
}



@if ($mbsc-material-theme and $mbsc-popup) {
  .mbsc-material {
    &.mbsc-popup-body-center,
    &.mbsc-popup-body-anchored,
    &.mbsc-popup-body-round {
      border-radius: 0.25em;
    }

    &.mbsc-popup-header {
      font-size: 1.25em;
      font-weight: 500;
      padding: 0.8em 0.8em 0 0.8em;
      line-height: 1.4em;
    }

    &.mbsc-popup-buttons {
      padding: 0.5em;
      display: block;
    }

    &.mbsc-popup-buttons.mbsc-ltr {
      text-align: right;
    }

    &.mbsc-popup-buttons.mbsc-rtl {
      text-align: right;
    }

    &.mbsc-popup-button.mbsc-button-flat {
      margin-left: 0;
      margin-right: 0;
    }

    &.mbsc-popup-button.mbsc-font {
      margin-top: 0;
      margin-bottom: 0;
      font-weight: 500;
      font-size: 0.9375em;
    }

    &.mbsc-popup-round {
      .mbsc-popup-body-bottom-full {
        border-radius: 0.25em 0.25em 0 0;
      }

      .mbsc-popup-body-top-full {
        border-radius: 0 0 0.25em 0.25em;
      }
    }
  }

  @include mbsc-material-popup('material', $mbsc-material-colors);
  @include mbsc-material-popup('material-dark', $mbsc-material-dark-colors);
}



@if ($mbsc-windows-theme and $mbsc-popup) {
  .mbsc-windows {
    &.mbsc-popup {
      border: 1px solid;
    }

    &.mbsc-popup-anchored-top {
      margin-top: -0.75em;
    }

    &.mbsc-popup-anchored-bottom {
      margin-top: 0.75em;
    }

    &.mbsc-popup-arrow {
      width: 1em;
      height: 1em;
      border: 1px solid;
    }

    &.mbsc-popup-arrow-bottom,
    &.mbsc-popup-arrow-top {
      margin-left: -0.5em;
    }

    &.mbsc-popup-arrow-left,
    &.mbsc-popup-arrow-right {
      margin-top: -0.5em;
    }

    &.mbsc-popup-arrow-bottom {
      top: 1.5em;
    }

    &.mbsc-popup-arrow-top {
      bottom: 1.5em;
    }

    &.mbsc-popup-arrow-left {
      right: 1.5em;
    }

    &.mbsc-popup-arrow-right {
      left: 1.5em;
    }

    &.mbsc-popup-header {
      padding: 0.5em;
      font-weight: bold;
      text-align: center;
      border-bottom: 1px solid;
    }

    &.mbsc-popup-buttons {
      display: flex;
      flex-direction: row-reverse;
      border-top: 1px solid;
    }

    &.mbsc-popup-button.mbsc-font {
      flex: 1 1;
      padding-top: 0.25em;
      padding-bottom: 0.25em;
    }

    &.mbsc-popup-button.mbsc-button-flat {
      margin: 0;
    }
  }

  @include mbsc-windows-popup('windows', $mbsc-windows-colors);
  @include mbsc-windows-popup('windows-dark', $mbsc-windows-dark-colors);
}


@if $mbsc-popup {
  @keyframes mbsc-fade-in {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  @keyframes mbsc-fade-out {
    from {
      opacity: 1;
    }

    to {
      opacity: 0;
    }
  }

  @keyframes mbsc-pop-in {
    from {
      opacity: 0;
      transform: scale(0.8);
    }

    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes mbsc-pop-out {
    from {
      opacity: 1;
      transform: scale(1);
    }

    to {
      opacity: 0;
      transform: scale(0.8);
    }
  }

  @keyframes mbsc-slide-up-in {
    from {
      transform: translateY(100%);
    }

    to {
      transform: translateY(0);
    }
  }

  @keyframes mbsc-slide-up-out {
    from {
      transform: translateY(0);
    }

    to {
      transform: translateY(100%);
    }
  }

  @keyframes mbsc-slide-down-in {
    from {
      transform: translateY(-100%);
    }

    to {
      transform: translateY(0);
    }
  }

  @keyframes mbsc-slide-down-out {
    from {
      transform: translateY(0);
    }

    to {
      transform: translateY(-100%);
    }
  }

  .mbsc-popup-open-ios {
    overflow: hidden;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    height: auto;
  }

  .mbsc-popup-ctx {
    position: relative;
    --mbsc-safe-top: 0;
    --mbsc-safe-right: 0;
    --mbsc-safe-bottom: 0;
    --mbsc-safe-left: 0;
  }

  .mbsc-popup-limits {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
  }

  .mbsc-popup-limits-anchored,
  .mbsc-popup-limits-center {
    margin: 1em;
  }

  .mbsc-popup,
  .mbsc-popup-wrapper {
    box-sizing: border-box;
  }

  .mbsc-popup-wrapper {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    pointer-events: none;
    justify-content: center;
    border: 0 solid transparent;
    z-index: 99998;
    user-select: none;
  }

  .mbsc-popup-wrapper-ctx {
    position: absolute;
    right: auto;
    bottom: auto;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .mbsc-popup-wrapper-center {
    padding: 1em;
    align-items: center;
    border-width: var(--mbsc-safe-top) var(--mbsc-safe-right) var(--mbsc-safe-bottom) var(--mbsc-safe-left);
  }

  .mbsc-popup-wrapper-top {
    align-items: flex-start;
    border-bottom: var(--mbsc-safe-bottom) solid transparent;
  }

  .mbsc-popup-wrapper-bottom {
    align-items: flex-end;
    border-top-width: var(--mbsc-safe-top);
  }

  .mbsc-popup-wrapper-anchored {
    border-width: 0 var(--mbsc-safe-right) var(--mbsc-safe-bottom) var(--mbsc-safe-left);
  }

  .mbsc-popup-hidden {
    opacity: 0;
  }

  .mbsc-popup-overlay {
    position: absolute;
    z-index: 1;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: -10em;
    pointer-events: auto;
    background: rgba(0, 0, 0, 0.5);
    transform: translateZ(0);
  }

  .mbsc-popup-overlay-in {
    opacity: 1;
    animation: mbsc-fade-in 150ms cubic-bezier(0, 0, 0.2, 1);
  }

  .mbsc-popup-overlay-out {
    opacity: 0;
    animation: mbsc-fade-out 75ms cubic-bezier(0.4, 0, 1, 1);
  }

  .mbsc-popup {
    max-height: 100%;
    min-width: 12em;
    position: relative;
    z-index: 2;
    pointer-events: auto;
    text-shadow: none;
    user-select: none;
  }

  .mbsc-popup-center {
    min-width: 16em;
  }

  .mbsc-popup-bottom {
    width: 100%;
    bottom: 0;
  }

  .mbsc-popup-top {
    width: 100%;
    top: 0;
  }

  .mbsc-popup-anchored {
    position: absolute;
    // max-width: calc(100% - 16px);
  }

  // .mbsc-popup-anchored-top {
  //   margin-top: -1em;
  // }

  // .mbsc-popup-anchored-bottom {
  //   margin-top: 1em;
  // }

  .mbsc-popup-anchored-left {
    margin-left: -1em;
  }

  .mbsc-popup-anchored-right {
    margin-left: 1em;
  }

  .mbsc-popup-arrow-wrapper {
    position: absolute;
    z-index: 1;
    overflow: hidden;
    pointer-events: none;
  }

  .mbsc-popup-arrow-wrapper-top,
  .mbsc-popup-arrow-wrapper-bottom {
    left: 0;
    right: 0;
    height: 2em;
  }

  .mbsc-popup-arrow-wrapper-left,
  .mbsc-popup-arrow-wrapper-right {
    top: 0;
    bottom: 0;
    width: 2em;
  }

  .mbsc-popup-arrow-wrapper-top {
    top: 100%;
  }

  .mbsc-popup-arrow-wrapper-bottom {
    bottom: 100%;
  }

  .mbsc-popup-arrow-wrapper-left {
    left: 100%;
  }

  .mbsc-popup-arrow-wrapper-right {
    right: 100%;
  }

  .mbsc-popup-arrow {
    position: absolute;
    box-sizing: border-box;
    width: 1.5em;
    height: 1.5em;
    pointer-events: auto;
  }

  .mbsc-popup-arrow-top {
    bottom: 1.25em;
  }

  .mbsc-popup-arrow-bottom {
    top: 1.25em;
  }

  .mbsc-popup-arrow-top,
  .mbsc-popup-arrow-bottom {
    left: 50%;
    margin-left: -0.75em;
    transform: rotate(-45deg);
  }

  .mbsc-popup-arrow-left {
    right: 1.25em;
  }

  .mbsc-popup-arrow-right {
    left: 1.25em;
  }

  .mbsc-popup-arrow-left,
  .mbsc-popup-arrow-right {
    top: 50%;
    margin-top: -0.75em;
    transform: rotate(45deg);
  }

  .mbsc-popup-focus {
    outline: 0;
  }

  .mbsc-popup-body {
    overflow: hidden;
    transform: translateZ(0);
  }

  .mbsc-popup-body-top {
    padding-top: var(--mbsc-safe-top);
  }

  .mbsc-popup-body-bottom {
    padding-bottom: var(--mbsc-safe-bottom);
  }

  .mbsc-popup-body-top,
  .mbsc-popup-body-bottom {
    padding-left: var(--mbsc-safe-left);
    padding-right: var(--mbsc-safe-right);
  }

  .mbsc-popup-body-round {
    padding: 0;
  }

  .mbsc-popup-header {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .mbsc-popup-content {
    overflow: auto;
    -webkit-overflow-scrolling: touch;
  }

  .mbsc-popup-padding {
    padding: 1em;
  }

  /* Animations */

  .mbsc-popup-pop-in {
    opacity: 1;
    animation: mbsc-pop-in 150ms cubic-bezier(0, 0, 0.2, 1);
  }

  .mbsc-popup-pop-out {
    opacity: 0;
    animation: mbsc-pop-out 75ms cubic-bezier(0.4, 0, 1, 1);
  }

  .mbsc-popup-slide-up-in {
    animation: mbsc-slide-up-in 250ms cubic-bezier(0, 0, 0.2, 1);
  }

  .mbsc-popup-slide-up-out {
    animation: mbsc-slide-up-out 200ms cubic-bezier(0.4, 0, 1, 1);
  }

  .mbsc-popup-slide-down-in {
    animation: mbsc-slide-down-in 250ms cubic-bezier(0, 0, 0.2, 1);
  }

  .mbsc-popup-slide-down-out {
    animation: mbsc-slide-down-out 200ms cubic-bezier(0.4, 0, 1, 1);
  }

  /* Desktop styling */

  .mbsc-popup-pointer {
    &.mbsc-picker .mbsc-popup-overlay,
    .mbsc-popup-overlay-anchored {
      background: none;
    }
  }

  .mbsc-popup-round {
    .mbsc-popup-slide-up-in {
      animation-name: mbsc-slide-up-in, mbsc-fade-in;
    }

    .mbsc-popup-slide-up-out {
      animation-name: mbsc-slide-up-out, mbsc-fade-out;
    }

    .mbsc-popup-slide-down-in {
      animation-name: mbsc-slide-down-in, mbsc-fade-in;
    }

    .mbsc-popup-slide-down-out {
      animation-name: mbsc-slide-down-out, mbsc-fade-out;
    }

    .mbsc-popup-top,
    .mbsc-popup-bottom {
      width: auto;
      margin-top: 3em;
      margin-bottom: 3em;
    }

    // .mbsc-popup-short {
    //   margin-top: 0;
    //   margin-bottom: 0;
    // }
  }
}



@if ($mbsc-ios-theme and $mbsc-calendar-view) {
  .mbsc-ios {
    &.mbsc-calendar-controls {
      /* IE11 needs px size here to avoid subpixel values */
      padding: 2px;
      /* padding: .125em; */
    }

    &.mbsc-calendar-cell {
      border-top-style: solid;
      border-top-width: 1px;
    }

    &.mbsc-calendar-cell-text {
      height: 2em;
      padding: 0 0.5em;
      line-height: 2em;
      border-radius: 2em;
    }

    &.mbsc-calendar-week-day {
      height: 1.7em;
      line-height: 1.7em;
      font-size: 0.625em;
    }

    &.mbsc-calendar-week-nr {
      width: 2em;
      height: auto;
      font-size: 0.75em;
      font-weight: bold;
      line-height: 3em;
    }

    &.mbsc-calendar-day-text {
      width: 1.625em;
      height: 1.625em;
      margin: 0.1875em;
      padding: 0;
      line-height: 1.625em;
      border: 2px solid transparent;
      border-radius: 2em;
    }

    /* Range Highlight */

    &.mbsc-range-day .mbsc-calendar-cell-inner {
      z-index: 1;
    }

    &.mbsc-range-day::after,
    &.mbsc-range-hover::before {
      content: '';
      height: 1.875em;
      position: absolute;
      left: -1px;
      right: -1px;
      top: 0.25em;
      z-index: 0;
      box-sizing: border-box;
    }

    &.mbsc-range-hover::before {
      border-top: 2px dashed;
      border-bottom: 2px dashed;
    }

    &.mbsc-range-day-start.mbsc-ltr::after,
    &.mbsc-range-day-end.mbsc-rtl::after,
    &.mbsc-range-hover-start.mbsc-ltr::before,
    &.mbsc-range-hover-end.mbsc-rtl::before {
      left: 50%;
      margin-left: -0.9375em;
      border-radius: 2em 0 0 2em;
    }

    &.mbsc-range-day-end.mbsc-ltr::after,
    &.mbsc-range-day-start.mbsc-rtl::after,
    &.mbsc-range-hover-end.mbsc-ltr::before,
    &.mbsc-range-hover-start.mbsc-rtl::before {
      right: 50%;
      margin-right: -0.9375em;
      border-radius: 0 2em 2em 0;
    }

    &.mbsc-range-day-start.mbsc-range-day-end::after,
    &.mbsc-range-hover-start.mbsc-range-hover-end::before {
      display: none;
    }

    /* Marks */

    &.mbsc-calendar-day-marked {
      padding-bottom: 0.4375em;
    }

    &.mbsc-calendar-marks {
      margin-top: -0.0625em;
    }

    &.mbsc-calendar-mark {
      width: 0.375em;
      height: 0.375em;
      border-radius: 0.375em;
      margin: 0 0.0625em;
    }

    /* Colors */

    &.mbsc-calendar-day-colors .mbsc-calendar-day-text {
      background-clip: padding-box;
    }

    &.mbsc-calendar-day-colors.mbsc-hover .mbsc-calendar-cell-text {
      background-clip: border-box;
    }

    /* Labels */

    &.mbsc-calendar-text:before {
      border-radius: 0.4em;
    }

    &.mbsc-calendar-label {
      font-weight: 600;
    }

    &.mbsc-calendar-label-background {
      margin: 0 -0.1em;
      background: currentColor;
      opacity: 0.3;
      transition: opacity 0.15s ease-in-out;
    }

    &.mbsc-calendar-label-start.mbsc-ltr .mbsc-calendar-label-background,
    &.mbsc-calendar-label-end.mbsc-rtl .mbsc-calendar-label-background {
      margin-left: 0;
      border-top-left-radius: 0.4em;
      border-bottom-left-radius: 0.4em;
    }

    &.mbsc-calendar-label-end.mbsc-ltr .mbsc-calendar-label-background,
    &.mbsc-calendar-label-start.mbsc-rtl .mbsc-calendar-label-background {
      margin-right: 0;
      border-top-right-radius: 0.4em;
      border-bottom-right-radius: 0.4em;
    }

    &.mbsc-calendar-label-hover .mbsc-calendar-label-background {
      opacity: 0.6;
    }

    &.mbsc-calendar-label.mbsc-calendar-label-hover:before {
      background: none;
    }

    &.mbsc-calendar-label-dragging .mbsc-calendar-label-background,
    &.mbsc-calendar-label-active .mbsc-calendar-label-background {
      opacity: 0.9;
    }

    /* Desktop styling */

    &.mbsc-calendar-height-md {
      .mbsc-calendar-day:after {
        position: absolute;
        top: 0;
        right: 100%;
        bottom: 0;
        z-index: 1;
        margin-right: -1px;
        border-left-width: 1px;
        border-left-style: solid;
        content: '';
      }
    }

    &.mbsc-calendar-width-md {
      .mbsc-calendar-title {
        font-size: 1.5em;
        line-height: 1.666667em;
        padding: 0 0.166667em;
      }

      // .mbsc-calendar-year {
      //   font-weight: 200;
      // }

      .mbsc-calendar-week-day {
        height: 2.5em;
        padding: 0 0.5em;
        line-height: 2.5em;
        font-size: 1em;
        border-left: 1px solid transparent;
      }

      .mbsc-calendar-week-nr {
        padding: 0;
        font-size: 0.75em;
      }

      .mbsc-calendar-day-inner {
        min-height: 2.5em;
      }

      .mbsc-calendar-day-labels .mbsc-calendar-day-inner {
        min-height: 4.75em;
      }

      .mbsc-calendar-marks {
        padding: 0 0.75em;
      }

      .mbsc-calendar-day-text {
        width: 1.375em;
        height: 1.375em;
        line-height: 1.375em;
      }

      .mbsc-calendar-text {
        height: 1.8em;
        line-height: 1.8em;
        margin: 0 0.5em 0.2em 0.6em;
      }

      .mbsc-calendar-label {
        padding: 0 0.4em;
      }

      .mbsc-calendar-label-text {
        font-size: 1.2em;
      }

      .mbsc-calendar-label-background {
        margin: 0 -0.4em;
      }
    }

    &.mbsc-calendar-height-md.mbsc-calendar-width-md {
      .mbsc-calendar-week-day.mbsc-ltr,
      .mbsc-calendar-day.mbsc-ltr,
      .mbsc-calendar-marks.mbsc-ltr {
        text-align: right;
      }

      .mbsc-calendar-week-day.mbsc-rtl,
      .mbsc-calendar-day.mbsc-rtl,
      .mbsc-calendar-marks.mbsc-rtl {
        text-align: left;
      }
    }

    /* Multi month grid view */

    &.mbsc-calendar-grid-view .mbsc-calendar-title {
      font-size: 1.5em;
      line-height: 1.666667em;
      padding: 0 0.166667em;
    }

    &.mbsc-calendar-grid {
      border-top: 1px solid;
    }

    &.mbsc-calendar-grid .mbsc-calendar-grid-item .mbsc-calendar-week-days {
      background: none;
    }

    &.mbsc-calendar-grid .mbsc-calendar-cell {
      border: 0;
    }
  }

  @include mbsc-ios-calendar-view('ios', $mbsc-ios-colors);
  @include mbsc-ios-calendar-view('ios-dark', $mbsc-ios-dark-colors);
}



@if ($mbsc-material-theme and $mbsc-calendar-view) {
  .mbsc-material {
    &.mbsc-calendar-wrapper-fixed:after {
      content: '';
      position: absolute;
      z-index: 7;
      bottom: -0.5em;
      left: 0;
      right: 0;
      height: 0.5em;
      pointer-events: none;
    }

    &.mbsc-calendar-controls {
      padding: 0.5em;
    }

    &.mbsc-calendar-week-day {
      height: 2.5em;
      line-height: 2.5em;
      font-size: 0.75em;
      font-weight: bold;
    }

    &.mbsc-calendar-week-nr {
      width: 2.181819em;
      height: auto;
      font-size: 0.6875em;
      line-height: 3.272728em;
      font-weight: bold;
    }

    &.mbsc-calendar-button.mbsc-button {
      padding: 0.428572em;
    }

    &.mbsc-calendar-title {
      font-size: 1.428572em;
      font-weight: 400;
      text-transform: none;
      line-height: 1.4em;
    }

    &.mbsc-calendar-cell-text {
      height: 2em;
      padding: 0 0.5em;
      line-height: 2em;
      border-radius: 2em;
    }

    &.mbsc-calendar-month-name {
      font-size: 0.8125em;
    }

    &.mbsc-calendar-month-name.mbsc-ltr {
      padding-left: 0.461539em;
      // margin-right: -1em;
    }

    &.mbsc-calendar-month-name.mbsc-rtl {
      padding-right: 0.461539em;
      // margin-left: -1em;
    }

    &.mbsc-calendar-day-text {
      height: 1.846154em;
      width: 1.846154em;
      line-height: 1.846154em;
      margin: 0.230769em auto;
      padding: 0;
      font-size: 0.8125em;
      border: 2px solid transparent;
    }

    /* Range Highlight */

    &.mbsc-range-day::after,
    &.mbsc-range-hover::before {
      content: '';
      height: 1.75em;
      position: absolute;
      left: 0;
      right: 0;
      top: 0.25em;
      z-index: -1;
    }

    &.mbsc-range-hover::before {
      box-sizing: content-box;
      height: 1.5em;
      border-top: 2px dashed;
      border-bottom: 2px dashed;
    }

    &.mbsc-range-day-start.mbsc-ltr::after,
    &.mbsc-range-day-end.mbsc-rtl::after {
      margin-left: -0.875em;
      border-radius: 2em 0 0 2em;
      left: 50%;
      right: 0;
    }

    &.mbsc-range-day-end.mbsc-ltr::after,
    &.mbsc-range-day-start.mbsc-rtl::after {
      margin-right: -0.875em;
      border-radius: 0 2em 2em 0;
      right: 50%;
      left: 0;
    }

    &.mbsc-range-hover-start.mbsc-ltr::before,
    &.mbsc-range-hover-end.mbsc-rtl::before {
      left: 50%;
      right: 0;
    }

    &.mbsc-range-hover-end.mbsc-ltr::before,
    &.mbsc-range-hover-start.mbsc-rtl::before {
      right: 50%;
      left: 0;
    }

    &.mbsc-range-day-start.mbsc-range-day-end::after,
    &.mbsc-range-hover-start.mbsc-range-hover-end::before {
      display: none;
    }

    /* Marks */

    &.mbsc-calendar-marks {
      margin-top: -0.375em;
      transition: transform 0.1s ease-out;
    }

    &.mbsc-focus .mbsc-calendar-marks,
    &.mbsc-hover .mbsc-calendar-marks,
    &.mbsc-selected .mbsc-calendar-marks,
    &.mbsc-calendar-day-colors .mbsc-calendar-marks,
    &.mbsc-highlighted .mbsc-calendar-marks,
    &.mbsc-hover-highlighted .mbsc-calendar-marks {
      transform: translate3d(0, 0.25em, 0);
    }

    /* Labels */
    &.mbsc-calendar-label {
      font-weight: 600;
      border-radius: 0.4em;
    }

    &.mbsc-calendar-label-background {
      margin: 0 -0.1em;
      background: currentColor;
    }

    &.mbsc-calendar-text:before {
      border-radius: 0.4em;
    }

    &.mbsc-calendar-label:before {
      margin: 0 -0.1em;
      border-radius: 0;
    }

    &.mbsc-calendar-label-start.mbsc-ltr .mbsc-calendar-label-background,
    &.mbsc-calendar-label-end.mbsc-rtl .mbsc-calendar-label-background,
    &.mbsc-calendar-label-start.mbsc-ltr.mbsc-calendar-text:before,
    &.mbsc-calendar-label-end.mbsc-rtl.mbsc-calendar-text:before {
      margin-left: 0;
      border-top-left-radius: 0.4em;
      border-bottom-left-radius: 0.4em;
    }

    &.mbsc-calendar-label-end.mbsc-ltr .mbsc-calendar-label-background,
    &.mbsc-calendar-label-start.mbsc-rtl .mbsc-calendar-label-background,
    &.mbsc-calendar-label-end.mbsc-ltr.mbsc-calendar-text:before,
    &.mbsc-calendar-label-start.mbsc-rtl.mbsc-calendar-text:before {
      margin-right: 0;
      border-top-right-radius: 0.4em;
      border-bottom-right-radius: 0.4em;
    }

    /* Desktop style */
    &.mbsc-calendar-height-md {
      .mbsc-calendar-slide {
        padding: 0;
      }

      .mbsc-calendar-day {
        border-bottom: 1px solid;
      }

      .mbsc-calendar-day:after {
        position: absolute;
        top: 0;
        right: 100%;
        bottom: 0;
        z-index: 1;
        margin-right: -1px;
        border-left-width: 1px;
        border-left-style: solid;
        content: '';
      }
    }

    &.mbsc-calendar-width-md {
      .mbsc-calendar-week-day {
        padding: 0 1em;
      }

      .mbsc-calendar-day-inner {
        min-height: 4em;
      }

      .mbsc-calendar-day-labels .mbsc-calendar-day-inner {
        min-height: 5.5em;
      }

      .mbsc-calendar-day-text {
        margin: 0.461539em;
      }

      .mbsc-calendar-week-nr {
        line-height: 3.818182em;
        padding: 0;
      }

      .mbsc-calendar-marks {
        padding: 0 1.125em;
        margin-left: -1px;
        margin-right: -1px;
      }

      .mbsc-calendar-text {
        height: 1.8em;
        line-height: 1.8em;
        margin: 0 0.5em 0.2em 0.6em;
      }

      .mbsc-calendar-label {
        padding: 0 0.4em;
      }

      .mbsc-calendar-label-text {
        font-size: 1.2em;
      }

      .mbsc-calendar-label:before,
      .mbsc-calendar-label-background {
        margin: 0 -0.4em;
      }
    }

    &.mbsc-calendar-height-md.mbsc-calendar-width-md {
      .mbsc-calendar-week-day.mbsc-ltr,
      .mbsc-calendar-day.mbsc-ltr,
      .mbsc-calendar-marks.mbsc-ltr {
        text-align: left;
      }

      .mbsc-calendar-week-day.mbsc-rtl,
      .mbsc-calendar-day.mbsc-rtl,
      .mbsc-calendar-marks.mbsc-rtl {
        text-align: right;
      }
    }
  }

  @include mbsc-material-calendar-view('material', $mbsc-material-colors);
  @include mbsc-material-calendar-view('material-dark', $mbsc-material-dark-colors);
}



@if ($mbsc-windows-theme and $mbsc-calendar-view) {
  .mbsc-windows {
    &.mbsc-calendar-header {
      margin-bottom: -1px;
    }

    &.mbsc-calendar-button.mbsc-button.mbsc-font {
      margin: 1px;
    }

    &.mbsc-calendar-title-wrapper .mbsc-calendar-button {
      padding: 0;
    }

    &.mbsc-calendar-controls {
      padding: 0.5em;
      min-height: 2.125em;
    }

    &.mbsc-calendar-week-day {
      height: 2.5em;
      font-size: 0.75em;
      line-height: 2.5em;
    }

    &.mbsc-calendar-body .mbsc-calendar-week-day {
      border: 0;
    }

    &.mbsc-calendar-week-nr {
      width: 2em;
      font-size: 0.75em;
      font-weight: bold;
      line-height: 3em;
    }

    &.mbsc-calendar-picker-slide {
      padding: 0.25em;
    }

    &.mbsc-calendar-cell {
      border-width: 0;
      border-style: solid;
      border-top-width: 1px;
      border-color: transparent;
      background-clip: padding-box;
      padding: 0;
    }

    &.mbsc-calendar-cell:after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;
    }

    &.mbsc-calendar-cell-inner {
      display: table;
      position: relative;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      border: 1px solid transparent;
      border-top-width: 2px;
      border-bottom-width: 2px;
    }

    &.mbsc-calendar-cell-text {
      display: table-cell;
      vertical-align: middle;
      min-width: 0;
    }

    &.mbsc-calendar-month-name {
      font-size: 0.875em;
      vertical-align: middle;

      &.mbsc-ltr {
        margin-left: 0.5em;
      }

      &.mbsc-rtl {
        margin-right: 0.5em;
      }
    }

    &.mbsc-calendar-day-inner {
      display: block;
    }

    &.mbsc-calendar-day-empty:after {
      display: none;
    }

    &.mbsc-calendar-day-text {
      display: inline-block;
      font-size: 0.875em;
      line-height: 2.285715em;
      border-radius: 2em;
      // width: 2.285715em;
      margin: 0 0.5em;
    }

    /* Marks */
    &.mbsc-calendar-marks {
      bottom: 0.25em;
      // margin-top: -0.5em;
    }

    &.mbsc-calendar-label-background {
      background: currentColor;
    }

    &.mbsc-calendar-width-md {
      .mbsc-calendar-title {
        font-size: 1.625em;
        font-weight: 300;
        line-height: 1.461538em;
        padding: 0 0.307693em;
      }

      .mbsc-calendar-week-day {
        height: 2.142858em;
        padding: 0 0.5em;
        font-size: 0.875em;
        line-height: 2.142858em;
      }

      .mbsc-calendar-week-nr {
        height: auto;
        padding: 0;
        font-size: 0.75em;
      }

      .mbsc-calendar-day:after {
        border-left-width: 1px;
        border-left-style: solid;
      }

      .mbsc-calendar-day-inner {
        min-height: 4em;
      }

      .mbsc-calendar-day-text {
        text-align: center;
      }

      .mbsc-calendar-day-labels .mbsc-calendar-day-inner {
        min-height: 4.75em;
      }

      .mbsc-calendar-marks {
        // margin: 0;
        bottom: 0.5em;
        padding: 0 0.625em;
      }

      .mbsc-calendar-text {
        height: 1.8em;
        line-height: 1.8em;
        padding: 0 0.6em;
      }

      .mbsc-calendar-label-text {
        font-size: 1.2em;
      }

      .mbsc-calendar-week-day.mbsc-ltr,
      .mbsc-calendar-day.mbsc-ltr,
      .mbsc-calendar-marks.mbsc-ltr {
        text-align: left;
      }

      .mbsc-calendar-week-day.mbsc-rtl,
      .mbsc-calendar-day.mbsc-rtl,
      .mbsc-calendar-marks.mbsc-rtl {
        text-align: right;
      }

      .mbsc-calendar-day.mbsc-calendar-day-labels {
        text-align: center;
      }
    }

    &.mbsc-calendar-grid-view .mbsc-calendar-title {
      font-size: 1.625em;
      font-weight: 300;
      line-height: 1.461538em;
      padding: 0 0.307693em;
    }
  }

  @include mbsc-windows-calendar-view('windows', $mbsc-windows-colors);
  @include mbsc-windows-calendar-view('windows-dark', $mbsc-windows-dark-colors);
}


@if $mbsc-calendar-view {
  @keyframes mbsc-zoom-in-up {
    from {
      opacity: 0;
      transform: scale(2);
    }

    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes mbsc-zoom-in-down {
    from {
      opacity: 0;
      transform: scale(0.5);
    }

    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes mbsc-zoom-out-up {
    from {
      opacity: 1;
      transform: scale(1);
    }

    to {
      opacity: 0;
      transform: scale(2);
    }
  }

  @keyframes mbsc-zoom-out-down {
    from {
      opacity: 1;
      transform: scale(1);
    }

    to {
      opacity: 0;
      transform: scale(0.5);
    }
  }

  .mbsc-calendar {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    touch-action: manipulation;
    position: relative;
  }

  .mbsc-calendar-wrapper {
    position: relative;
    z-index: 1;
    // width: 100%;
    height: 100%;
    overflow: hidden;
  }

  /* Header */

  .mbsc-calendar-header {
    position: relative;
    z-index: 1;
    will-change: opacity;

    .mbsc-calendar-week-days {
      margin-left: -1px;
    }
  }

  .mbsc-calendar-controls {
    align-items: center;
    box-sizing: content-box;
    min-height: 2.5em;
  }

  .mbsc-calendar-button.mbsc-button.mbsc-reset {
    margin: 0;
  }

  .mbsc-calendar-button-prev-multi {
    order: -1;
  }

  .mbsc-calendar-button-next-multi {
    order: 1;
  }

  .mbsc-calendar-body {
    // height: 100%;
    box-sizing: border-box;
    overflow: hidden;
  }

  .mbsc-calendar-body-inner {
    // height: 100%;
    position: relative;
    overflow: hidden;
    margin-left: -1px;
  }

  .mbsc-calendar-wrapper-fixed {
    // display: block;
    height: auto;
    overflow: visible;

    // .mbsc-calendar-header {
    //   display: block;
    // }

    // .mbsc-calendar-body {
    //   display: block;
    //   height: auto;
    // }
  }

  .mbsc-calendar-title-wrapper {
    align-items: center;
    overflow: hidden;
  }

  .mbsc-calendar-title {
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 1.125em;
    // line-height: 2.222223em;
    padding: 0 0.25em;
    display: inline-block;
    vertical-align: middle;
  }

  /* Scrollview */

  .mbsc-calendar-scroll-wrapper {
    display: block;
    overflow: hidden;
    position: relative;
    height: 100%;
  }

  .mbsc-calendar-picker-wrapper {
    position: relative;
    width: 16em;
    height: 16em;
    overflow: hidden;
  }

  .mbsc-calendar-picker {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
  }

  .mbsc-calendar-picker-main {
    position: relative;
    z-index: 0;
  }

  .mbsc-calendar-picker-in-up {
    opacity: 0;
    animation: mbsc-zoom-in-up 200ms cubic-bezier(0, 0, 0.2, 1) forwards;
  }

  .mbsc-calendar-picker-in-down {
    opacity: 0;
    animation: mbsc-zoom-in-down 200ms cubic-bezier(0, 0, 0.2, 1) forwards;
  }

  .mbsc-calendar-picker-out-up {
    opacity: 0;
    animation: mbsc-zoom-out-up 200ms cubic-bezier(0.4, 0, 1, 1) forwards;
  }

  .mbsc-calendar-picker-out-down {
    opacity: 0;
    animation: mbsc-zoom-out-down 200ms cubic-bezier(0, 0, 0.2, 1) forwards;
  }

  .mbsc-calendar-scroll-wrapper > div {
    height: 100%;
  }

  .mbsc-calendar-scroll-wrapper > div > div {
    height: 100%;
    transform: translate3d(0, 0, 0);
  }

  .mbsc-calendar-slide {
    position: absolute;
    z-index: 0;
    top: 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
  }

  .mbsc-calendar-slide.mbsc-calendar-picker {
    position: relative;
  }

  .mbsc-calendar-slide.mbsc-ltr {
    left: 0;
  }

  .mbsc-calendar-slide.mbsc-rtl {
    right: 0;
  }

  .mbsc-calendar-table {
    height: 100%;
    // Needed for iOS Safari to prevent animation flicker
    transform: translateZ(0);
  }

  .mbsc-calendar-cell {
    position: relative;
    text-align: center;
    white-space: nowrap;
    cursor: pointer;
  }

  .mbsc-calendar-cell:focus {
    outline: 0;
  }

  .mbsc-calendar-cell-inner {
    display: inline-block;
    padding: 0 0.25em;
    vertical-align: middle;
  }

  .mbsc-calendar-cell.mbsc-disabled {
    cursor: not-allowed;
  }

  .mbsc-calendar-week-day {
    height: 2em;
    font-size: 0.6875em;
    line-height: 2em;
    text-align: center;
    border-bottom: 1px solid transparent;
  }

  .mbsc-calendar-year,
  .mbsc-calendar-month {
    vertical-align: middle;
  }

  .mbsc-calendar-year-text,
  .mbsc-calendar-month-text {
    margin: 0.875em 0;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 4.5em;
    box-sizing: border-box;
  }

  .mbsc-calendar-day {
    min-width: 0;
    text-align: center;
  }

  .mbsc-calendar-day-empty {
    cursor: default;
  }

  .mbsc-calendar-day-text {
    display: inline-block;
    text-align: center;
  }

  .mbsc-calendar-day-text.mbsc-calendar-cell-text {
    // Stronger rule is needed for box-sizing, if placed inside mbsc-grid
    box-sizing: content-box;
  }

  .mbsc-calendar-day-inner {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 0;
    border: 1px solid transparent;
    box-sizing: border-box;
  }

  .mbsc-calendar-day-hidden {
    visibility: hidden;
  }

  .mbsc-calendar-month-name {
    display: none;
  }

  .mbsc-calendar-width-md .mbsc-calendar-month-name {
    display: inline-block;
    font-weight: bold;
  }

  .mbsc-calendar-popup .mbsc-calendar-month-name {
    display: none;
  }

  .mbsc-calendar-day-outer .mbsc-calendar-day-text,
  .mbsc-calendar-day-outer .mbsc-calendar-month-name {
    opacity: 0.5;
  }

  .mbsc-calendar-day-outer.mbsc-selected .mbsc-calendar-day-text,
  .mbsc-calendar-day-outer.mbsc-selected .mbsc-calendar-month-name {
    opacity: 1;
  }

  .mbsc-disabled .mbsc-calendar-cell-text,
  .mbsc-disabled .mbsc-calendar-month-name {
    opacity: 0.2;
  }

  /* Day highlight */

  .mbsc-calendar-day-highlight:before {
    content: ' ';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
  }

  /* Marks */

  .mbsc-calendar-day-marked {
    padding-bottom: 0.25em;
  }

  .mbsc-calendar-marks {
    position: absolute;
    left: 0;
    right: 0;
    height: 0.375em;
    margin-top: -0.125em;
    overflow: hidden;
    white-space: nowrap;
    text-align: center;
  }

  .mbsc-calendar-mark {
    display: inline-block;
    vertical-align: top;
    width: 0.25em;
    height: 0.25em;
    margin: 0.0625em;
    border-radius: 0.25em;
  }

  /* Labels */

  .mbsc-calendar-labels {
    position: absolute;
    left: -1px;
    right: -1px;
  }

  .mbsc-calendar-label-wrapper {
    position: absolute;
    z-index: 2;
    pointer-events: none;
  }

  .mbsc-calendar-label {
    padding: 0 0.3em;
  }

  .mbsc-calendar-text:focus {
    outline: 0;
  }

  .mbsc-calendar-label-text {
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
    z-index: 2;
  }

  .mbsc-calendar-text:before,
  .mbsc-calendar-label-background {
    content: '';
    position: absolute;
    z-index: 0;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    transition: background-color 0.2s;
  }

  .mbsc-calendar-label.mbsc-calendar-label-hover:before {
    background-color: rgba(0, 0, 0, 0.2);
    z-index: 1;
  }

  .mbsc-calendar-text-more {
    padding: 0 0.3em;
  }

  .mbsc-calendar-text-more.mbsc-calendar-label-active:before,
  .mbsc-calendar-text-more.mbsc-calendar-label-hover:before {
    background-color: rgba(0, 0, 0, 0.1);
  }

  .mbsc-calendar-text {
    display: block;
    position: relative;
    height: 1.6em;
    line-height: 1.6em;
    // overflow: hidden;
    font-size: 0.625em;
    white-space: nowrap;
    margin: 0 0.1em 0.2em 0.2em;
  }

  .mbsc-calendar-text-placeholder {
    z-index: -1;
  }

  .mbsc-calendar-text.mbsc-ltr {
    text-align: left;
  }

  .mbsc-calendar-text.mbsc-rtl {
    text-align: right;
  }

  /* Label drag & drop */

  .mbsc-calendar-label-resize {
    position: absolute;
    top: 0;
    height: 100%;
    width: 0.5em;
    z-index: 2;
    cursor: ew-resize;
  }

  .mbsc-calendar-label-resize:after {
    content: '';
    display: none;
    position: absolute;
    top: 50%;
    width: 0.8em;
    height: 0.8em;
    margin-top: -0.4em;
    background: #fff;
    border-radius: 0.8em;
    border: 1px solid currentColor;
    box-sizing: border-box;
  }

  .mbsc-calendar-label-resize-start.mbsc-ltr,
  .mbsc-calendar-label-resize-end.mbsc-rtl {
    left: 0;
  }

  .mbsc-calendar-label-resize-start.mbsc-rtl,
  .mbsc-calendar-label-resize-end.mbsc-ltr {
    right: 0;
  }

  .mbsc-calendar-label-resize-start.mbsc-ltr:after,
  .mbsc-calendar-label-resize-end.mbsc-rtl:after {
    left: -0.4em;
  }

  .mbsc-calendar-label-resize-end.mbsc-ltr:after,
  .mbsc-calendar-label-resize-start.mbsc-rtl:after {
    right: -0.4em;
  }

  .mbsc-calendar-label-resize-start-touch:before,
  .mbsc-calendar-label-resize-end-touch:before {
    content: '';
    position: absolute;
    top: 50%;
    margin-top: -1em;
    width: 2em;
    height: 2em;
  }

  .mbsc-calendar-label-resize-start-touch:before {
    left: -1em;
  }

  .mbsc-calendar-label-resize-end-touch:before {
    right: -1em;
  }

  .mbsc-calendar-label-hover,
  .mbsc-calendar-label-dragging {
    .mbsc-calendar-label-resize:after {
      display: block;
    }
  }

  .mbsc-calendar-label-inactive {
    opacity: 0.4;
  }

  .mbsc-calendar-label-hidden {
    visibility: hidden;
  }

  .mbsc-calendar-labels-dragging {
    z-index: 3;
  }

  /* Multi month grid view */

  .mbsc-calendar-grid {
    overflow: auto;
    padding: 0 2%;

    .mbsc-calendar-table {
      margin: 0 5%;
      width: 90%;
    }
  }

  .mbsc-calendar-grid-item {
    margin: 1em 0;
    // Space for month title, needed for height 100% to work correctly for .mbsc-calendar-table in Safari
    padding-top: 2.5em;
  }

  .mbsc-calendar-month-title {
    padding-bottom: 0.8em;
    margin: 0 10%;
    margin-top: -2em;
    font-size: 1.25em;
    font-weight: bold;
    line-height: 1.2em;
  }

  /* Variable row */

  .mbsc-calendar-body-inner-variable {
    overflow-y: auto;

    .mbsc-calendar-table {
      min-height: 20em;
    }
  }
}



@if ($mbsc-ios-theme) {
  @include mbsc-ios-calendar('ios', $mbsc-ios-colors);
  @include mbsc-ios-calendar('ios-dark', $mbsc-ios-dark-colors);
}



@if ($mbsc-material-theme) {
  @include mbsc-material-calendar('material', $mbsc-material-colors);
  @include mbsc-material-calendar('material-dark', $mbsc-material-dark-colors);
}



@if ($mbsc-windows-theme) {
  @include mbsc-windows-calendar('windows', $mbsc-windows-colors);
  @include mbsc-windows-calendar('windows-dark', $mbsc-windows-dark-colors);
}




@if ($mbsc-material-theme and $mbsc-scroller) {
  .mbsc-material {
    &.mbsc-scroller-wheel-wrapper {
      margin: 0 0.25em;
      padding: 0.5em 0;
    }

    &.mbsc-scroller-wheel-cont::after,
    &.mbsc-scroller-wheel-cont::before {
      content: '';
      display: block;
      position: absolute;
      width: 100%;

      border-width: 2px;
      border-top-style: solid;
      z-index: 1;
    }

    &.mbsc-scroller-wheel-cont::after {
      bottom: -2px;
    }

    &.mbsc-scroller-wheel-cont::before {
      top: -2px;
    }

    &.mbsc-scroller-wheel-group {
      padding: 2em 0.25em;
    }

    &.mbsc-scroller-wheel-item {
      padding: 0 0.272727em;
      font-size: 1.375em;
      text-align: center;
    }

    &.mbsc-wheel-item-multi {
      padding: 0 2em; // .5em added for more space between checkmark and text
    }

    &.mbsc-scroller-wheel-header {
      font-weight: bold;
      font-size: 1.125em;
    }

    /* Checkmark styling */

    &.mbsc-wheel-checkmark {
      box-sizing: border-box;
      position: absolute;
      top: 50%;
      display: none;
      border-radius: 0.1875em;
      width: 1.3em;
      height: 1.3em;
      margin-top: -0.55em;

      &::after {
        content: '';
        box-sizing: border-box;
        position: absolute;
        display: block;

        top: 0.27273em;
        left: 0.27273em;
        width: 0.8125em;
        height: 0.4375em;
        opacity: 0;
        border: 0.125em solid;
        border-top: 0;
        border-right: 0;
        transform: scale(0) rotate(-45deg);
        transition: transform 0.1s ease-out;
      }

      &.mbsc-selected::after {
        opacity: 1;
        transform: scale(1) rotate(-45deg);
      }

      &.mbsc-ltr {
        left: 0.25em;
      }

      &.mbsc-rtl {
        right: 0.25em;
      }
    }

    &.mbsc-wheel-item-multi &.mbsc-wheel-checkmark {
      display: block;
    }

    /* Desktop styling */

    &.mbsc-scroller-pointer {
      .mbsc-scroller-wheel-group {
        padding: 0;
      }

      .mbsc-scroller-wheel-wrapper {
        margin: 0;
        padding: 0;
      }

      .mbsc-scroller-wheel-item {
        font-size: 1em;
        padding: 0 1.25em;
      }

      .mbsc-wheel-item-multi {
        padding: 0 2em;
      }

      .mbsc-wheel-item-multi {
        &.mbsc-ltr {
          text-align: left;
        }

        &.mbsc-rtl {
          text-align: right;
        }
      }
    }
  }

  @include mbsc-material-scroller('material', $mbsc-material-colors);
  @include mbsc-material-scroller('material-dark', $mbsc-material-dark-colors);
}



@if ($mbsc-ios-theme and $mbsc-scroller) {
  .mbsc-ios {
    &.mbsc-scroller-wheel-multi.mbsc-scroller-wheel-cont-3d {
      visibility: hidden;
    }

    &.mbsc-scroller-wheel-group-cont {
      padding: 0 0.625em;
    }

    &.mbsc-scroller-wheel-group {
      padding: 0.625em;
    }

    &.mbsc-wheel-group-checkmark {
      padding: 0.625em 0;
    }

    &.mbsc-scroller-wheel-3d {
      overflow: visible;
    }

    &.mbsc-scroller-wheel-line {
      display: block;
      z-index: 1;
      border-radius: 0.5em;
      margin: 0 0.625em;
    }

    &.mbsc-scroller-wheel-overlay {
      display: block;
    }

    &.mbsc-scroller-wheel-item {
      padding: 0 0.5em;
      font-size: 1.25em;
      text-align: center;
      box-sizing: border-box;
      border-radius: 0.5em;
    }

    &.mbsc-scroller-wheel-item-3d {
      font-size: 1.125em;
    }

    &.mbsc-wheel-item-checkmark {
      padding-left: 1.75em;
      padding-right: 1.75em;
    }

    &.mbsc-scroller-wheel-header {
      font-size: 0.875em;
    }

    /* checkmark */

    &.mbsc-wheel-checkmark {
      display: none;
      position: absolute;
      width: 1.75em;
      height: 1.75em;
      top: 50%;
      margin-top: -0.875em;

      &::after {
        content: '';
        box-sizing: border-box;
        position: absolute;
        display: block;
        opacity: 0;
        transform: rotate(-45deg);

        top: 32%;
        left: 26%;
        width: 0.75em;
        height: 0.375em;
        border: 0.125em solid currentColor;
        border-top: 0;
        border-right: 0;
        transition: opacity 0.2s ease-in-out;
      }

      &.mbsc-selected::after {
        opacity: 1;
      }

      &.mbsc-ltr {
        left: 0;
      }

      &.mbsc-rtl {
        right: 0;
      }
    }

    &.mbsc-wheel-item-multi &.mbsc-wheel-checkmark,
    &.mbsc-scroller-pointer &.mbsc-wheel-item-checkmark .mbsc-wheel-checkmark {
      display: block;
    }

    /* Desktop styling */

    &.mbsc-scroller-pointer {
      .mbsc-scroller-wheel-group-cont,
      .mbsc-scroller-wheel-group {
        padding: 0;
      }

      .mbsc-scroller-wheel-wrapper {
        padding: 0.1875em 0;
      }

      .mbsc-scroller-wheel-overlay {
        display: none;
      }

      .mbsc-scroller-wheel-item {
        font-size: 1em;
        padding: 0 0.75em;
        margin: 0 0.1875em;
      }

      .mbsc-scroller-wheel-line {
        margin: 0 0.1875em;
      }

      .mbsc-wheel-item-checkmark.mbsc-ltr {
        text-align: left;
        padding-left: 1.75em;
      }

      .mbsc-wheel-item-checkmark.mbsc-rtl {
        text-align: right;
        padding-right: 1.75em;
      }

      // Group styling

      .mbsc-scroller-wheel-header {
        font-size: 0.75em;
        font-weight: 700;
      }
    }
  }

  @include mbsc-ios-scroller('ios', $mbsc-ios-colors);
  @include mbsc-ios-scroller('ios-dark', $mbsc-ios-dark-colors);
}



@if ($mbsc-windows-theme and $mbsc-scroller) {
  .mbsc-windows {
    &.mbsc-scroller {
      position: relative;
    }

    &.mbsc-scroller-wheel-wrapper.mbsc-ltr {
      border-left-width: 1px;
      border-left-style: solid;
    }

    &.mbsc-scroller-wheel-wrapper-0.mbsc-ltr {
      border-left: 0;
    }

    &.mbsc-scroller-wheel-wrapper.mbsc-rtl {
      border-right-width: 1px;
      border-right-style: solid;
    }

    &.mbsc-scroller-wheel-wrapper-0.mbsc-rtl {
      border-right: 0;
    }

    &.mbsc-scroller-wheel-item {
      padding: 0 0.5em;
      box-sizing: border-box;
      text-align: center;
    }

    &.mbsc-wheel-item-multi {
      padding: 0 2.0625em; // .5em is added for more space between checkmark and text

      &.mbsc-ltr {
        text-align: left;
      }

      &.mbsc-rtl {
        text-align: right;
      }
    }

    &.mbsc-scroller-wheel-line {
      display: block;
      z-index: 1;
    }

    &.mbsc-scroller-wheel-header {
      font-size: 1.375em;
    }

    /* Checkmark styling */

    &.mbsc-wheel-checkmark {
      box-sizing: border-box;
      position: absolute;
      top: 50%;
      display: none;

      margin-top: -0.8125em;
      width: 1.5625em;
      height: 1.5625em;

      &::after {
        content: '';
        box-sizing: border-box;
        position: absolute;
        display: block;
        opacity: 0;
        transform: rotate(-45deg);

        top: 28%;
        left: 21%;
        width: 0.875em;
        height: 0.475em;
        border: 0.125em solid;
        border-top: 0;
        border-right: 0;
      }

      &.mbsc-selected::after {
        opacity: 1;
      }

      &.mbsc-ltr {
        left: 0.25em;
      }

      &.mbsc-rtl {
        right: 0.25em;
      }
    }

    &.mbsc-wheel-item-multi &.mbsc-wheel-checkmark {
      display: block;
    }

    /* Desktop styling */

    &.mbsc-scroller-pointer {
      .mbsc-scroller-wheel-item {
        padding: 0 1.25em;
      }

      .mbsc-wheel-item-multi {
        padding: 0 2.0625em; // .5em is added for more space between checkmark and text
      }
    }

    &.mbsc-scroller-bar::after {
      border-radius: 0;
    }
  }

  @include mbsc-windows-scroller('windows', $mbsc-windows-colors);
  @include mbsc-windows-scroller('windows-dark', $mbsc-windows-dark-colors);
}


@if $mbsc-scroller {
  .mbsc-scroller {
    width: 100%;
    box-sizing: border-box;
    position: relative;
    text-align: center;
    user-select: none;
  }

  .mbsc-scroller-wheel-group-cont {
    position: relative;
    max-width: 100%;
    vertical-align: middle;
    display: inline-block;
    overflow: hidden;
  }

  .mbsc-scroller-wheel-group-cont:first-child:last-child {
    display: block;
  }

  .mbsc-scroller-wheel-group {
    margin: 0 auto;
    position: relative;
    justify-content: center;
  }

  .mbsc-scroller-wheel-group-3d {
    perspective: 100em;
  }

  .mbsc-scroller-wheel-wrapper {
    display: block;
    max-width: 100%;
    position: relative;
    touch-action: none;
    /* Temp */
    min-width: 80px;
  }

  .mbsc-scroller-pointer .mbsc-scroller-wheel-wrapper-0:last-child {
    flex: 1 1 auto; // Expand single wheel in desktop mode
  }

  .mbsc-scroller-wheel-line {
    display: none;
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    pointer-events: none;
    transform: translateY(-50%);
  }

  .mbsc-scroller-wheel-overlay {
    display: none;
    position: absolute;
    z-index: 2;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    pointer-events: none;
  }

  .mbsc-scroller-wheel {
    display: block;
    overflow: hidden;
    /* Fixes Firefox rendering issues */
    border-top: 1px solid transparent;
    border-bottom: 1px solid transparent;
    margin: -1px 0;
  }

  .mbsc-scroller-wheel-cont {
    position: relative;
    z-index: 1;
    top: 50%;
    border-top: 1px solid transparent;
    border-bottom: 1px solid transparent;
    transform: translateY(-50%);
  }

  .mbsc-scroller-wheel-cont-3d {
    overflow: hidden;
    border: 0;
  }

  .mbsc-scroller-wheel-cont-3d > div {
    position: relative;
    top: -1px;
  }

  .mbsc-scroller-wheel-wrapper-3d,
  .mbsc-scroller-wheel-3d {
    transform-style: preserve-3d;
  }

  .mbsc-scroller-items-3d {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform-style: preserve-3d;
    transform: translateY(-50%);
  }

  .mbsc-scroller .mbsc-scroller-wheel,
  .mbsc-scroller .mbsc-scroller-wheel-cont {
    box-sizing: content-box;
  }

  .mbsc-scroller-wheel-item {
    display: block;
    position: relative;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .mbsc-scroller-wheel-item:focus,
  .mbsc-scroller-wheel-header:focus {
    outline: 0;
  }

  .mbsc-scroller-wheel-item-3d {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    cursor: pointer;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  .mbsc-scroller-wheel-item.mbsc-disabled {
    opacity: 0.3;
  }

  .mbsc-scroller-wheel-header {
    display: block; // Needed by Angular
    padding: 0 0.5em;

    &.mbsc-ltr {
      text-align: left;
    }

    &.mbsc-rtl {
      text-align: right;
    }
  }

  .mbsc-scroller-bar-cont {
    position: absolute;
    z-index: 4;
    top: 0;
    bottom: 0;
    width: 10px;
    opacity: 0;
    background: rgba(0, 0, 0, 0.05);
    transform: translateZ(0);
    transition: opacity 0.2s;

    &.mbsc-ltr {
      right: 0;
    }

    &.mbsc-rtl {
      left: 0;
    }
  }

  .mbsc-scroller-bar-hidden {
    display: none;
  }

  .mbsc-scroller-bar-hover,
  .mbsc-scroller-bar-started {
    opacity: 1;
  }

  .mbsc-scroller-bar {
    position: absolute;
    right: 0;
    top: 0;
    width: 10px;
    // height: 100%;

    &::after {
      content: '';
      position: absolute;
      top: 2px;
      right: 2px;
      bottom: 2px;
      left: 2px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 3px;
    }
  }
}



@if ($mbsc-ios-theme and $mbsc-datepicker) {
  .mbsc-ios {
    &.mbsc-datetime-year-wheel .mbsc-scroller-wheel-item {
      min-width: 3.8em;
    }

    &.mbsc-datetime-month-wheel .mbsc-scroller-wheel-item {
      text-align: left;
    }

    &.mbsc-datetime-day-wheel .mbsc-scroller-wheel-item,
    &.mbsc-datetime-hour-wheel .mbsc-scroller-wheel-item,
    &.mbsc-datetime-date-wheel .mbsc-scroller-wheel-item {
      text-align: right;
    }
  }
}





@if ($mbsc-windows-theme and $mbsc-datepicker) {
  .mbsc-windows {
    &.mbsc-datetime-date-wheel.mbsc-scroller-wheel-wrapper {
      min-width: 120px;
    }

    &.mbsc-datetime-month-wheel .mbsc-scroller-wheel-item,
    &.mbsc-datetime-date-wheel .mbsc-scroller-wheel-item {
      text-align: left;
    }

    &.mbsc-datetime-month-wheel.mbsc-rtl .mbsc-scroller-wheel-item,
    &.mbsc-datetime-date-wheel.mbsc-rtl .mbsc-scroller-wheel-item {
      text-align: right;
    }
  }
}




@if ($mbsc-ios-theme and $mbsc-segmented) {
  .mbsc-ios {
    &.mbsc-segmented {
      margin: 0.75em;
      padding: 0.0625em;
      position: relative;
      border-radius: 0.5625em;
    }

    &.mbsc-segmented-item:before {
      content: '';
      position: absolute;
      border-left: 1px solid;
      top: 0.3125em;
      bottom: 0.3125em;
      opacity: 1;
      transition: opacity 0.2s ease-in-out;
    }

    &.mbsc-segmented-item.mbsc-ltr:before {
      left: 0;
    }

    &.mbsc-segmented-item.mbsc-rtl:before {
      right: 0;
    }

    &.mbsc-segmented-item:first-child:before,
    &.mbsc-segmented-item-selected:before,
    &.mbsc-segmented-item-selected + .mbsc-segmented-item:before {
      opacity: 0;
    }

    &.mbsc-segmented-selectbox {
      position: absolute;
      padding: 0.0625em;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      user-select: none;
      border-radius: 0.4375em;
      display: none;
    }

    &.mbsc-segmented-selectbox.mbsc-selected {
      display: block;
    }

    &.mbsc-segmented-selectbox-animate {
      transition: transform 0.2s ease-in-out;
    }

    &.mbsc-segmented-selectbox-inner {
      height: 100%;
      border-radius: 0.4375em;
      transition: transform 0.1s ease-in-out;
      visibility: hidden;
    }

    &.mbsc-segmented-selectbox-inner-visible {
      visibility: visible;
    }

    &.mbsc-segmented-item:first-child .mbsc-segmented-selectbox-inner {
      transform-origin: left;
    }

    &.mbsc-segmented-item:last-child .mbsc-segmented-selectbox-inner {
      transform-origin: right;
    }

    &.mbsc-segmented-dragging {
      .mbsc-segmented-selectbox-inner {
        transform: scale(0.97, 0.95);
      }

      .mbsc-segmented-item:first-child .mbsc-segmented-selectbox-inner {
        transform: scale(0.97, 0.95) translateX(0.0625em);
      }

      .mbsc-segmented-item:last-child .mbsc-segmented-selectbox-inner {
        transform: scale(0.97, 0.95) translateX(-0.0625em);
      }
    }

    &.mbsc-segmented-button.mbsc-icon-button {
      height: 2.307693em; // 30px / 13px
    }

    &.mbsc-segmented-button.mbsc-button {
      margin: 0;
      padding: 0 0.615385em; // 0 8px
      border-radius: 0;
      font-size: 0.8125em; // 13px
      line-height: 2.307693em; // 30px / 13px
      transition: opacity 0.1s ease-out, background-color 0.1s ease-out, transform 0.1s ease-in-out;
      // for the scaling to look better
      -webkit-font-smoothing: subpixel-antialiased;

      &.mbsc-active,
      &.mbsc-hover {
        opacity: 1;
      }
    }

    &.mbsc-segmented-dragging .mbsc-segmented-item-selected .mbsc-segmented-button {
      transform: scale(0.97, 0.95);
    }

    &.mbsc-segmented-item-selected:first-child .mbsc-segmented-button {
      transform-origin: left;
    }

    &.mbsc-segmented-item-selected:last-child .mbsc-segmented-button {
      transform-origin: right;
    }
  }

  @include mbsc-ios-segmented('ios', $mbsc-ios-colors);
  @include mbsc-ios-segmented('ios-dark', $mbsc-ios-dark-colors);
}



@if ($mbsc-material-theme and $mbsc-segmented) {
  .mbsc-material {
    &.mbsc-segmented {
      padding: 0.75em;
    }

    &.mbsc-segmented-selectbox {
      display: none;
    }

    &.mbsc-segmented-item:first-child {
      .mbsc-segmented-button {
        border-top-left-radius: 0.25em;
        border-bottom-left-radius: 0.25em;
      }

      .mbsc-segmented-button.mbsc-rtl {
        border-radius: 0 0.25em 0.25em 0;
        border-right-width: 0.142858em;
      }
    }

    &.mbsc-segmented-item:last-child {
      .mbsc-segmented-button {
        border-top-right-radius: 0.25em;
        border-bottom-right-radius: 0.25em;
        border-right-width: 0.142858em;
      }

      .mbsc-segmented-button.mbsc-rtl {
        border-radius: 0.25em 0 0 0.25em;
        border-right-width: 0;
      }
    }

    &.mbsc-segmented-button.mbsc-button {
      margin: 0;
      padding: 0.285715em 1.142858em;
      border: 0.142858em solid transparent;
      border-right-width: 0;
      border-radius: 0;
      background: none;
      box-shadow: none;
      min-width: auto;
      font-size: 0.875em;

      &.mbsc-hover,
      &.mbsc-active {
        box-shadow: none;
      }

      &.mbsc-focus:after {
        content: '';
        display: block;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
      }

      /* Color presets */

      &.mbsc-button-primary {
        color: $mbsc-material-primary;
        border-color: $mbsc-material-primary;

        &.mbsc-hover,
        &.mbsc-active {
          background: rgba($mbsc-material-primary, 0.2);
        }

        &.mbsc-selected {
          background: $mbsc-material-primary;
        }
      }

      &.mbsc-button-secondary {
        color: $mbsc-material-secondary;
        border-color: $mbsc-material-secondary;

        &.mbsc-hover,
        &.mbsc-active {
          background: rgba($mbsc-material-secondary, 0.2);
        }

        &.mbsc-selected {
          background: $mbsc-material-secondary;
        }
      }

      &.mbsc-button-success {
        color: $mbsc-material-success;
        border-color: $mbsc-material-success;

        &.mbsc-hover,
        &.mbsc-active {
          background: rgba($mbsc-material-success, 0.2);
        }

        &.mbsc-selected {
          background: $mbsc-material-success;
        }
      }

      &.mbsc-button-danger {
        color: $mbsc-material-danger;
        border-color: $mbsc-material-danger;

        &.mbsc-hover,
        &.mbsc-active {
          background: rgba($mbsc-material-danger, 0.2);
        }

        &.mbsc-selected {
          background: $mbsc-material-danger;
        }
      }

      &.mbsc-button-warning {
        color: $mbsc-material-warning;
        border-color: $mbsc-material-warning;

        &.mbsc-hover,
        &.mbsc-active {
          background: rgba($mbsc-material-warning, 0.2);
        }

        &.mbsc-selected {
          background: $mbsc-material-warning;
        }
      }

      &.mbsc-button-info {
        color: $mbsc-material-info;
        border-color: $mbsc-material-info;

        &.mbsc-hover,
        &.mbsc-active {
          background: rgba($mbsc-material-info, 0.2);
        }

        &.mbsc-selected {
          background: $mbsc-material-info;
        }
      }
    }
  }

  @include mbsc-material-segmented('material', $mbsc-material-colors);
  @include mbsc-material-segmented('material-dark', $mbsc-material-dark-colors);
}



@if ($mbsc-windows-theme and $mbsc-segmented) {
  .mbsc-windows {
    &.mbsc-segmented {
      padding: 0.75em;
    }

    &.mbsc-segmented-selectbox {
      display: none;
    }

    &.mbsc-segmented-button.mbsc-button {
      margin: 0;
      border-color: transparent;

      &.mbsc-active,
      &.mbsc-hover {
        border-color: transparent;
      }

      &.mbsc-focus {
        z-index: 2;
      }

      /* Color presets */
      &.mbsc-button-primary {
        &.mbsc-active,
        &.mbsc-selected {
          background: $mbsc-windows-primary;
        }
      }

      &.mbsc-button-secondary {
        &.mbsc-active,
        &.mbsc-selected {
          background: $mbsc-windows-secondary;
        }
      }

      &.mbsc-button-success {
        &.mbsc-active,
        &.mbsc-selected {
          background: $mbsc-windows-success;
        }
      }

      &.mbsc-button-danger {
        &.mbsc-active,
        &.mbsc-selected {
          background: $mbsc-windows-danger;
        }
      }

      &.mbsc-button-warning {
        &.mbsc-active,
        &.mbsc-selected {
          background: $mbsc-windows-warning;
        }
      }

      &.mbsc-button-info {
        &.mbsc-active,
        &.mbsc-selected {
          background: $mbsc-windows-info;
        }
      }
    }
  }

  @include mbsc-windows-segmented('windows', $mbsc-windows-colors);
  @include mbsc-windows-segmented('windows-dark', $mbsc-windows-dark-colors);
}


@if $mbsc-segmented {
  .mbsc-segmented {
    // display: table;
    // table-layout: fixed;
    // width: 100%;
    overflow: hidden;
    // box-sizing: border-box;
  }

  .mbsc-segmented-item {
    margin: 0;
    // display: table-cell;
    position: relative;
    // vertical-align: top;
    text-align: center;
    font-size: 1em;
    flex: 1 1 0;
    user-select: none;
    min-width: 0; // needed for the items to have equal widths
  }

  .mbsc-segmented-label {
    display: block;
    margin: 0;
    padding: 0;
  }

  .mbsc-segmented-input {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    position: absolute;
  }

  .mbsc-segmented-button.mbsc-button {
    width: 100%;
  }
}



@mixin mbsc-ios-timegrid($theme, $params) {
  $background: map-get($params, 'background');
  $accent: map-get($params, 'accent');
  $text: map-get($params, 'text');

  $background-hover: '';
  $selected-text: '';

  @if (lightness($background) > 50%) {
    $background-hover: lighten($accent, 35%);
    $selected-text: lighten($text, 100%);
  } @else {
    $background-hover: darken($accent, 25%);
    $selected-text: lighten($text, 100%);
  }

  .mbsc-#{$theme} {
    &.mbsc-timegrid-item:hover,
    &.mbsc-timegrid-item:focus {
      background: rgba($accent, 0.3);
    }

    &.mbsc-timegrid-item.mbsc-selected {
      background: $accent;
      color: $selected-text;
    }
  }
}


@if ($mbsc-ios-theme and $mbsc-datepicker) {
  .mbsc-ios {
    &.mbsc-timegrid-item {
      line-height: 2em;
      margin: 0.5em 0.625em;
      border-radius: 2em;
      font-size: 0.875em;
    }

    &.mbsc-timegrid-item.mbsc-selected,
    &.mbsc-timegrid-item:hover,
    &.mbsc-timegrid-item:focus {
      font-size: 1em;
      margin: 0 0.25em;
      outline: none;
    }
  }

  @include mbsc-ios-timegrid('ios', $mbsc-ios-colors);
  @include mbsc-ios-timegrid('ios-dark', $mbsc-ios-dark-colors);
}



@mixin mbsc-material-timegrid($theme, $params) {
  $background: map-get($params, 'background');
  $accent: map-get($params, 'accent');
  $text: map-get($params, 'text');

  $background-hover: '';
  $selected-text: '';

  @if (lightness($background) > 50%) {
    $background-hover: lighten($accent, 35%);
    $selected-text: lighten($text, 100%);
  } @else {
    $background-hover: darken($accent, 25%);
    $selected-text: darken($text, 100%);
  }

  .mbsc-#{$theme} {
    &.mbsc-timegrid-item:hover,
    &.mbsc-timegrid-item:focus {
      background: rgba($text, 0.1);
    }

    &.mbsc-timegrid-item.mbsc-selected {
      background: $accent;
      color: $selected-text;
    }
  }
}


@if ($mbsc-material-theme and $mbsc-datepicker) {
  .mbsc-material {
    &.mbsc-timegrid-item {
      line-height: 2em;
      margin: 0.5em 0.625em;
      border-radius: 2em;
      font-size: 0.875em;
    }

    &.mbsc-timegrid-item.mbsc-selected,
    &.mbsc-timegrid-item:hover,
    &.mbsc-timegrid-item:focus {
      margin: 0 0.25em;
      outline: none;
    }
  }

  @include mbsc-material-timegrid('material', $mbsc-material-colors);
  @include mbsc-material-timegrid('material-dark', $mbsc-material-dark-colors);
}



@mixin mbsc-windows-timegrid($theme, $params) {
  $background: map-get($params, 'background');
  $accent: map-get($params, 'accent');
  $text: map-get($params, 'text');

  .mbsc-#{$theme} {
    &.mbsc-timegrid-item.mbsc-selected {
      background: rgba($accent, 0.25);
      color: $text;
    }

    &.mbsc-timegrid-item:hover,
    &.mbsc-timegrid-item:focus {
      background: rgba($text, 0.1);
    }

    &.mbsc-timegrid-item.mbsc-selected:hover,
    &.mbsc-timegrid-item.mbsc-selected:focus {
      background: mix(rgba($accent, 0.5), rgba($text, 0.2));
    }
  }
}


@if ($mbsc-windows-theme and $mbsc-datepicker) {
  .mbsc-windows {
    &.mbsc-timegrid-item {
      padding: 1.3125em 0;
      margin: 0.0625em;
      font-size: 14px;
    }

    &.mbsc-timegrid-item:focus {
      outline: none;
    }
  }

  @include mbsc-windows-timegrid('windows', $mbsc-windows-colors);
  @include mbsc-windows-timegrid('windows-dark', $mbsc-windows-dark-colors);
}


@if $mbsc-datepicker {
  // When the calendar control is present, then the calendar specifies the height,
  // but when the row is wrapped, we need a minimum height, so the grid won't collapse
  // into 0
  .mbsc-datepicker-control-calendar .mbsc-datepicker-tab-timegrid {
    min-height: 10em;
  }

  // when there is no other control there the grid needs a minimum height
  .mbsc-datepicker-tab-timegrid {
    min-height: 19em;
  }

  .mbsc-timegrid-item.mbsc-disabled {
    pointer-events: none;
    opacity: 0.2;
  }

  .mbsc-datepicker-tab-timegrid.mbsc-datepicker-tab {
    position: relative;
    overflow: auto;
    align-items: flex-start;
    width: 18.5em;
  }

  .mbsc-timegrid-container {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: table;
    table-layout: fixed;
    width: 100%;
  }

  .mbsc-timegrid-row {
    display: table-row;
  }

  .mbsc-timegrid-cell {
    display: table-cell;
    position: relative; // do not remove!

    &.mbsc-disabled {
      cursor: not-allowed;
    }
  }

  .mbsc-timegrid-item {
    vertical-align: middle;
    text-align: center;
    white-space: nowrap;
    cursor: pointer;
    user-select: none;
  }
}



@if ($mbsc-ios-theme and $mbsc-forms) {
  .mbsc-ios {
    &.mbsc-form-control-wrapper {
      margin-top: -1px;
    }

    &.mbsc-form-control-wrapper.mbsc-error {
      z-index: 2;
    }

    &.mbsc-form-control-wrapper:before,
    &.mbsc-form-control-wrapper:after {
      content: '';
      position: absolute;
      border-top: 1px solid transparent;
    }

    &.mbsc-form-control-wrapper.mbsc-ltr:after,
    &.mbsc-form-control-wrapper.mbsc-ltr:before {
      right: 0;
      left: 1em;
    }

    &.mbsc-form-control-wrapper.mbsc-rtl:after,
    &.mbsc-form-control-wrapper.mbsc-rtl:before {
      left: 0;
      right: 1em;
    }

    &.mbsc-form-control-wrapper:before {
      top: 0;
    }

    &.mbsc-form-control-wrapper:after {
      bottom: 0;
    }

    .mbsc-block-title + &.mbsc-form-control-wrapper.mbsc-ltr:before,
    .mbsc-form-group-title + &.mbsc-form-control-wrapper.mbsc-ltr:before,
    &.mbsc-form-control-wrapper.mbsc-ltr:first-child:before,
    &.mbsc-form-control-wrapper.mbsc-ltr:last-child:after {
      left: 0;
    }

    .mbsc-block-title + &.mbsc-form-control-wrapper.mbsc-rtl:before,
    .mbsc-form-group-title + &.mbsc-form-control-wrapper.mbsc-rtl:before,
    &.mbsc-form-control-wrapper.mbsc-rtl:first-child:before,
    &.mbsc-form-control-wrapper.mbsc-rtl:last-child:after {
      right: 0;
    }

    &.mbsc-form-control-label.mbsc-disabled,
    &.mbsc-description.mbsc-disabled {
      opacity: 0.3;
    }

    .mbsc-form-group-inset .mbsc-form-group-title + .mbsc-form-control-wrapper,
    .mbsc-form-group-inset .mbsc-form-control-wrapper:first-child {
      border-top-left-radius: 0.5em;
      border-top-right-radius: 0.5em;
    }

    .mbsc-form-group-inset .mbsc-form-control-wrapper:last-child {
      border-bottom-left-radius: 0.5em;
      border-bottom-right-radius: 0.5em;
    }

    .mbsc-form-group-inset .mbsc-form-group-title,
    .mbsc-form-group-inset .mbsc-form-group-title + .mbsc-form-control-wrapper:before,
    .mbsc-form-group-inset .mbsc-form-control-wrapper:first-child:before,
    .mbsc-form-group-inset .mbsc-form-control-wrapper:last-child:after {
      border-width: 0;
    }
  }

  @include mbsc-ios-form-controls('ios', $mbsc-ios-colors);
  @include mbsc-ios-form-controls('ios-dark', $mbsc-ios-dark-colors);
}



@if ($mbsc-material-theme and $mbsc-forms) {
  .mbsc-material {
    &.mbsc-form-control-label.mbsc-disabled,
    &.mbsc-description.mbsc-disabled {
      opacity: 0.3;
    }
  }

  @include mbsc-material-form-controls('material', $mbsc-material-colors);
  @include mbsc-material-form-controls('material-dark', $mbsc-material-dark-colors);
}



@if ($mbsc-windows-theme and $mbsc-forms) {
  .mbsc-windows {
    &.mbsc-form-control-label.mbsc-disabled,
    &.mbsc-description.mbsc-disabled {
      opacity: 0.3;
    }
  }

  @include mbsc-windows-form-controls('windows', $mbsc-windows-colors);
  @include mbsc-windows-form-controls('windows-dark', $mbsc-windows-dark-colors);
}


@if $mbsc-forms {
  .mbsc-form-control-label {
    display: block;
  }

  .mbsc-form-control-input {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    border: 0;
    opacity: 0;
    margin: 0;
    z-index: 3;
  }
}



@if ($mbsc-ios-theme and $mbsc-input) {
  .mbsc-ios {
    /* Wrapper */

    &.mbsc-textfield-wrapper-has-icon-right.mbsc-rtl,
    &.mbsc-textfield-wrapper-has-icon-left.mbsc-ltr {
      z-index: 1;
    }

    &.mbsc-textfield-wrapper-has-icon-left.mbsc-ltr:before,
    &.mbsc-textfield-wrapper-has-icon-left.mbsc-ltr:after {
      left: 3.375em;
    }

    &.mbsc-textfield-wrapper-has-icon-right.mbsc-rtl:after,
    &.mbsc-textfield-wrapper-has-icon-right.mbsc-rtl:before {
      right: 3.375em;
    }

    /* Inner element */

    &.mbsc-textfield-inner {
      position: static;
      flex: 1 auto;
      z-index: -1;
    }

    &.mbsc-textfield-inner.mbsc-disabled {
      opacity: 0.5;
    }

    /* Form element */

    &.mbsc-textfield {
      display: flex;
      height: 2.75em;
      padding: 0 1em;
      background: transparent;
      height: 2.75em;
    }

    &.mbsc-textfield-has-icon-left {
      padding-left: 3.375em;
    }

    &.mbsc-textfield-has-icon-right {
      padding-right: 3.375em;
    }

    &.mbsc-textfield-stacked,
    &.mbsc-textfield-floating {
      height: 3.5em;
      padding-top: 1.25em;
    }

    /* Icons */

    &.mbsc-textfield-icon {
      top: 0.625em;
      z-index: 1;
    }

    &.mbsc-textfield-icon-left {
      left: 0.9375em;
    }

    &.mbsc-textfield-icon-right {
      right: 0.9375em;
    }

    &.mbsc-textfield-icon-floating,
    &.mbsc-textfield-icon-stacked {
      top: 1em;
    }

    /* Label */

    &.mbsc-label-inline {
      line-height: 2.75em;
    }

    &.mbsc-label-inline.mbsc-ltr {
      padding-left: 1em;
    }

    &.mbsc-label-inline.mbsc-rtl {
      padding-right: 1em;
    }

    &.mbsc-label-stacked {
      top: 0.666667em;
      font-size: 0.75em;
      line-height: 1em;
    }

    &.mbsc-label-stacked.mbsc-ltr {
      left: 1.333334em;
    }

    &.mbsc-label-stacked.mbsc-rtl {
      right: 1.333334em;
    }

    &.mbsc-label-floating {
      top: 0.875em;
      line-height: 2em;
    }

    &.mbsc-label-floating.mbsc-ltr {
      left: 1em;
    }

    &.mbsc-label-floating.mbsc-rtl {
      right: 1em;
    }

    &.mbsc-label-floating-active.mbsc-label {
      transform: translateY(-0.75em) scale(0.75);
    }

    &.mbsc-label.mbsc-disabled {
      opacity: 0.5;
    }

    /* Error message */

    &.mbsc-error-message {
      display: block;
      padding: 0 1.333334em;
      line-height: 1.666667em;
    }

    &.mbsc-error-message-underline {
      position: static;
    }

    &.mbsc-error-message-has-icon-left.mbsc-ltr {
      padding-left: 4.5em;
    }

    &.mbsc-error-message-has-icon-right.mbsc-rtl {
      padding-right: 4.5em;
    }

    /* Underline input --------------------------------------------------------------------------- */

    .mbsc-textfield-wrapper-underline {
      // Cut corners of select dropdown in case of inset form group
      overflow: hidden;
    }

    /* Form element*/

    &.mbsc-textfield-underline-inline-has-icon-left.mbsc-ltr {
      padding-left: 1em;
    }

    &.mbsc-textfield-underline-inline-has-icon-right.mbsc-rtl {
      padding-right: 1em;
    }

    /* Label */

    &.mbsc-label-underline {
      margin: 0;
    }

    &.mbsc-label-underline-stacked-has-icon-left.mbsc-ltr {
      left: 4.5em;
    }

    &.mbsc-label-underline-stacked-has-icon-right.mbsc-rtl {
      right: 4.5em;
    }

    &.mbsc-label-underline-inline-has-icon-left.mbsc-ltr {
      padding-left: 3.375em;
    }

    &.mbsc-label-underline-inline-has-icon-right.mbsc-rtl {
      padding-right: 3.375em;
    }

    &.mbsc-label-underline-floating-has-icon-left.mbsc-ltr {
      left: 3.375em;
    }

    &.mbsc-label-underline-floating-has-icon-right.mbsc-rtl {
      right: 3.375em;
    }

    /* Error message */

    &.mbsc-error-message-underline.mbsc-error-message-inline.mbsc-ltr {
      padding-left: 1.333334em;
    }

    &.mbsc-error-message-underline.mbsc-error-message-inline.mbsc-rtl {
      padding-right: 1.333334em;
    }

    /* ------------------------------------------------------------------------------------------- */

    /* Box & outline input common styles --------------------------------------------------------- */

    /* Wrapper */

    &.mbsc-textfield-wrapper-outline,
    &.mbsc-textfield-wrapper-box {
      margin: 1.5em 1em;
    }

    &.mbsc-textfield-wrapper-outline.mbsc-font::after,
    &.mbsc-textfield-wrapper-outline.mbsc-font::before,
    &.mbsc-textfield-wrapper-box.mbsc-font::after,
    &.mbsc-textfield-wrapper-box.mbsc-font::before {
      border: none;
    }

    /* Form element */

    &.mbsc-textfield-box,
    &.mbsc-textfield-outline {
      border-radius: 0.5em;
      border: 1px solid transparent;
    }

    &.mbsc-textfield-inner-box,
    &.mbsc-textfield-inner-outline {
      position: relative;
    }

    /* Label */

    &.mbsc-label-box-stacked-has-icon-left.mbsc-ltr,
    &.mbsc-label-outline-stacked-has-icon-left.mbsc-ltr {
      left: 4.5em;
    }

    &.mbsc-label-outline-stacked-has-icon-right.mbsc-rtl,
    &.mbsc-label-box-stacked-has-icon-right.mbsc-rtl {
      right: 4.5em;
    }

    &.mbsc-label-box-stacked,
    &.mbsc-label-outline-stacked,
    &.mbsc-label-box-floating,
    &.mbsc-label-outline-floating {
      margin: 0 1px;
    }

    &.mbsc-label-outline-floating-has-icon-left.mbsc-ltr,
    &.mbsc-label-box-floating-has-icon-left.mbsc-ltr {
      left: 3.375em;
    }

    &.mbsc-label-outline-floating-has-icon-right.mbsc-rtl,
    &.mbsc-label-box-floating-has-icon-right.mbsc-rtl {
      right: 3.375em;
    }

    /* Error message */

    &.mbsc-error-message-outline,
    &.mbsc-error-message-box {
      margin: 0 1px;
    }

    /* ------------------------------------------------------------------------------------------- */

    /* Select */

    &.mbsc-select.mbsc-ltr {
      padding-right: 3.375em;
    }

    &.mbsc-select.mbsc-rtl {
      padding-left: 3.375em;
    }

    &.mbsc-select.mbsc-textfield-has-icon-right.mbsc-ltr {
      padding-right: 4.875em;
    }

    &.mbsc-select.mbsc-textfield-has-icon-left.mbsc-rtl {
      padding-left: 4.875em;
    }

    /* Select icon */

    &.mbsc-select-icon {
      top: 0.625em;
    }

    &.mbsc-select-icon-stacked,
    &.mbsc-select-icon-floating {
      top: 1em;
    }

    &.mbsc-select-icon.mbsc-ltr {
      right: 0.9375em;
    }

    &.mbsc-select-icon.mbsc-rtl {
      left: 0.9375em;
    }

    &.mbsc-select-icon-right.mbsc-ltr {
      right: 3.375em;
    }

    &.mbsc-select-icon-left.mbsc-rtl {
      left: 3.375em;
    }

    /* Textarea */

    &.mbsc-textarea {
      height: 3em;
      border: 0;
    }

    &.mbsc-textarea.mbsc-textfield-stacked,
    &.mbsc-textarea.mbsc-textfield-floating {
      padding-top: 0;
    }

    &.mbsc-textarea-inner {
      padding-top: 0.625em;
      padding-bottom: 0.625em;
    }

    &.mbsc-textarea-inner.mbsc-textfield-inner-box,
    &.mbsc-textarea-inner.mbsc-textfield-inner-outline {
      border: 1px solid transparent;
      border-radius: 0.5em;
    }

    &.mbsc-textarea-inner.mbsc-textfield-inner-stacked,
    &.mbsc-textarea-inner.mbsc-textfield-inner-floating {
      padding-top: 1.375em;
    }

    /* Input tags */

    &.mbsc-textfield-tags-inner {
      padding-top: 0;
      padding-bottom: 0;
    }

    &.mbsc-textfield.mbsc-textfield-tags {
      padding-top: 0.3125em;
      padding-bottom: 0.3125em;
      min-height: 2.75em;
    }

    &.mbsc-textfield-tags.mbsc-textfield-stacked,
    &.mbsc-textfield-tags.mbsc-textfield-floating {
      min-height: 2.125em;
      padding-top: 0;
      padding-bottom: 0;
    }

    &.mbsc-textfield-tag {
      border-radius: 1em;
    }
  }

  @include mbsc-ios-input('ios', $mbsc-ios-colors);
  @include mbsc-ios-input('ios-dark', $mbsc-ios-dark-colors);
}



@if ($mbsc-material-theme and $mbsc-input) {
  .mbsc-material {
    /* Wrapper */

    &.mbsc-textfield-wrapper {
      margin: 1.5em 1em;
      box-sizing: border-box;
    }

    /* Inner wrapper */

    &.mbsc-textfield-inner.mbsc-disabled {
      opacity: 0.5;
    }

    /* Form element */

    &.mbsc-textfield {
      display: block;
      width: 100%;
      height: 2.25em;
      background-color: transparent;
      border-bottom: 1px solid;
      font-size: 1em;
      transition: border-color 0.2s;
    }

    &.mbsc-textfield-has-icon-left {
      padding-left: 2em;
    }

    &.mbsc-textfield-has-icon-right {
      padding-right: 2em;
    }

    /* Icon */

    &.mbsc-textfield-icon {
      top: 0.375em;
    }

    &.mbsc-textfield-icon-floating,
    &.mbsc-textfield-icon-stacked,
    &.mbsc-textfield-icon-outline {
      top: 1em;
    }

    /* Ripple */

    .mbsc-textfield-ripple {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      transform: scaleX(0);
      transition: transform 180ms cubic-bezier(0.4, 0, 0.2, 1);
      z-index: 2;
    }

    .mbsc-textfield-ripple-active {
      transform: scaleX(1);
    }

    /* Label */

    &.mbsc-label {
      font-size: 0.75em;
    }

    &.mbsc-label-inline {
      line-height: 2em;
      font-size: 1em;
    }

    &.mbsc-label-inline.mbsc-ltr {
      padding-right: 0.5em;
    }

    &.mbsc-label-inline.mbsc-rtl {
      padding-left: 0.5em;
    }

    &.mbsc-label-floating {
      font-size: 1em;
      line-height: 1.5em;
      top: 1.125em;
    }

    &.mbsc-label-floating-active {
      transform: translateY(-1.125em) scale(0.75);
    }

    &.mbsc-label.mbsc-disabled {
      opacity: 0.5;
    }

    /* Error message */

    &.mbsc-error-message.mbsc-ltr {
      left: 0;
    }

    &.mbsc-error-message.mbsc-rtl {
      right: 0;
    }

    &.mbsc-error-message-has-icon-left.mbsc-ltr {
      left: 2.66667em;
    }

    &.mbsc-error-message-has-icon-right.mbsc-rtl {
      right: 2.66667em;
    }

    /* Underline input --------------------------------------------------------------------------- */

    /* Form element */

    &.mbsc-textfield-underline-stacked,
    &.mbsc-textfield-underline-floating {
      height: 3em;
      padding-top: 0.875em;
    }

    /* Icon */

    &.mbsc-textfield-icon-underline {
      top: 0.25em;
    }

    &.mbsc-textfield-icon-underline.mbsc-textfield-icon-floating,
    &.mbsc-textfield-icon-underline.mbsc-textfield-icon-stacked {
      top: 1.125em;
    }

    /* Label */

    &.mbsc-label-underline-inline {
      padding-top: 1px;
    }

    &.mbsc-label-underline-stacked-has-icon-left.mbsc-ltr {
      left: 2.66667em;
    }

    &.mbsc-label-underline-stacked-has-icon-right.mbsc-rtl {
      right: 2.66667em;
    }

    &.mbsc-label-underline-floating-has-icon-left.mbsc-ltr {
      left: 2em;
    }

    &.mbsc-label-underline-floating-has-icon-right.mbsc-rtl {
      right: 2em;
    }

    /* ------------------------------------------------------------------------------------------- */

    /* Box input --------------------------------------------------------------------------------- */

    /* Inner wrapper */

    &.mbsc-textfield-box,
    &.mbsc-textfield-inner-box {
      border-radius: 0.25em 0.25em 0 0;
    }

    /* Form element */

    &.mbsc-textfield-box {
      padding: 0 1em;
    }

    &.mbsc-textfield-box-stacked,
    &.mbsc-textfield-box-floating {
      height: 3.5em;
      padding-top: 1.25em;
    }

    &.mbsc-textfield-underline.mbsc-disabled {
      border-style: dotted;
    }

    /* Label */

    &.mbsc-label-box-inline {
      padding-top: 1px;
    }

    &.mbsc-label-box-stacked {
      top: 0.666667em;
    }

    &.mbsc-label-box-floating {
      top: 1em;
    }

    &.mbsc-label-box-floating.mbsc-label-floating-active {
      transform: translateY(-0.625em) scale(0.75);
    }

    /* ------------------------------------------------------------------------------------------- */

    /* Outline input ----------------------------------------------------------------------------- */

    /* Inner wrapper */

    &.mbsc-textfield-inner-outline {
      padding: 0 2px;
    }

    /* Form element */

    &.mbsc-textfield-outline {
      height: 3.5em;
      padding: 2px 1em;
      border: 0;
      border-radius: 4px;
    }

    &.mbsc-textfield-fieldset {
      top: -0.5em;
      border: 1px solid;
      border-radius: 4px;
      transition: border-color 0.2s;
    }

    &.mbsc-textfield-fieldset-has-icon-left {
      padding-left: 3em;
    }

    &.mbsc-textfield-fieldset-has-icon-right {
      padding-right: 3em;
    }

    &.mbsc-textfield-fieldset.mbsc-focus {
      border-width: 2px;
    }

    &.mbsc-textfield-legend {
      padding: 0;
      margin: 0;
      font-size: 0.75em;
      color: transparent;
      width: 0.01px; // Fraction needed for IE11
      white-space: nowrap;
    }

    &.mbsc-textfield-legend-active {
      width: auto;
      padding: 0 0.333334em;
      margin: 0 -0.333334em;
    }

    /* Label */

    &.mbsc-label-outline-inline {
      line-height: 3.375em;
    }

    &.mbsc-label-outline-stacked {
      top: -0.5em;
      margin: 0 2px; // Compensate borders
    }

    &.mbsc-label-outline-floating {
      top: 1em;
      margin: 0 2px; // Compensate borders
    }

    &.mbsc-label-outline-floating.mbsc-label-floating-active {
      margin-top: -1px;
      transform: translateY(-1.5em) scale(0.75);
    }

    /* Error message */

    &.mbsc-error-message-outline {
      margin: 0 2px; // Compensate border
    }

    /* ------------------------------------------------------------------------------------------- */

    /* Box & outline input common styles --------------------------------------------------------- */

    /* Form element */

    &.mbsc-textfield-outline-has-icon-left,
    &.mbsc-textfield-box-has-icon-left {
      padding-left: 3em;
    }

    &.mbsc-textfield-outline-has-icon-right,
    &.mbsc-textfield-box-has-icon-right {
      padding-right: 3em;
    }

    /* Icon */

    &.mbsc-textfield-icon-outline-left,
    &.mbsc-textfield-icon-box-left {
      left: 0.75em;
    }

    &.mbsc-textfield-icon-outline-right,
    &.mbsc-textfield-icon-box-right {
      right: 0.75em;
    }

    /* Label */

    &.mbsc-label-box-stacked.mbsc-ltr,
    &.mbsc-label-outline-stacked.mbsc-ltr {
      left: 1.333334em;
    }

    &.mbsc-label-box-stacked.mbsc-rtl,
    &.mbsc-label-outline-stacked.mbsc-rtl {
      right: 1.333334em;
    }

    &.mbsc-label-outline-stacked-has-icon-left.mbsc-ltr,
    &.mbsc-label-box-stacked-has-icon-left.mbsc-ltr {
      left: 4em;
    }

    &.mbsc-label-box-stacked-has-icon-right.mbsc-rtl,
    &.mbsc-label-outline-stacked-has-icon-right.mbsc-rtl {
      right: 4em;
    }

    &.mbsc-label-box-floating.mbsc-ltr,
    &.mbsc-label-outline-floating.mbsc-ltr {
      left: 1em;
    }

    &.mbsc-label-box-floating.mbsc-rtl,
    &.mbsc-label-outline-floating.mbsc-rtl {
      right: 1em;
    }

    &.mbsc-label-outline-floating-has-icon-left.mbsc-ltr,
    &.mbsc-label-box-floating-has-icon-left.mbsc-ltr {
      left: 3em;
    }

    &.mbsc-label-outline-floating-has-icon-left.mbsc-rtl,
    &.mbsc-label-box-floating-has-icon-left.mbsc-rtl {
      right: 1em;
    }

    &.mbsc-label-outline-floating-has-icon-right.mbsc-rtl,
    &.mbsc-label-box-floating-has-icon-right.mbsc-rtl {
      right: 3em;
    }

    /* Error message */

    &.mbsc-error-message-box.mbsc-ltr,
    &.mbsc-error-message-outline.mbsc-ltr {
      left: 1.333334em;
    }

    &.mbsc-error-message-box.mbsc-rtl,
    &.mbsc-error-message-outline.mbsc-rtl {
      right: 1.333334em;
    }

    &.mbsc-error-message-box.mbsc-error-message-has-icon-left.mbsc-ltr,
    &.mbsc-error-message-outline.mbsc-error-message-has-icon-left.mbsc-ltr {
      left: 4em;
    }

    &.mbsc-error-message-box.mbsc-error-message-has-icon-right.mbsc-rtl,
    &.mbsc-error-message-outline.mbsc-error-message-has-icon-right.mbsc-rtl {
      right: 4em;
    }

    /* ------------------------------------------------------------------------------------------- */

    /* Select */

    &.mbsc-select.mbsc-ltr {
      padding-right: 3em;
    }

    &.mbsc-select.mbsc-rtl {
      padding-left: 3em;
    }

    &.mbsc-select.mbsc-textfield-has-icon-right.mbsc-ltr {
      padding-right: 4.5em;
    }

    &.mbsc-select.mbsc-textfield-has-icon-left.mbsc-rtl {
      padding-left: 4.5em;
    }

    &.mbsc-select.mbsc-textfield-underline-has-icon-right.mbsc-ltr {
      padding-right: 3.75em;
    }

    &.mbsc-select.mbsc-textfield-underline-has-icon-left.mbsc-rtl {
      padding-left: 3.75em;
    }

    /* Select icon */

    &.mbsc-select-icon {
      top: 0.375em;
    }

    &.mbsc-select-icon.mbsc-ltr {
      right: 0.75em;
    }

    &.mbsc-select-icon.mbsc-rtl {
      left: 0.75em;
    }

    &.mbsc-select-icon-right.mbsc-ltr {
      right: 3em;
    }

    &.mbsc-select-icon-left.mbsc-rtl {
      left: 3em;
    }

    &.mbsc-select-icon-stacked,
    &.mbsc-select-icon-floating,
    &.mbsc-select-icon-outline {
      top: 1em;
    }

    &.mbsc-select-icon-underline {
      top: 0.25em;
    }

    &.mbsc-select-icon-underline.mbsc-ltr {
      right: 0;
    }

    &.mbsc-select-icon-underline.mbsc-rtl {
      left: 0;
    }

    &.mbsc-select-icon-underline.mbsc-select-icon-right.mbsc-ltr {
      right: 2.25em;
    }

    &.mbsc-select-icon-underline.mbsc-select-icon-left.mbsc-rtl {
      left: 2.25em;
    }

    &.mbsc-select-icon-underline.mbsc-select-icon-floating,
    &.mbsc-select-icon-underline.mbsc-select-icon-stacked {
      top: 1.125em;
    }

    /* Textarea */

    &.mbsc-textarea {
      height: 1.875em;
      padding-bottom: 0.375em;
    }

    &.mbsc-textarea.mbsc-textfield-stacked,
    &.mbsc-textarea.mbsc-textfield-floating {
      padding-top: 0;
    }

    &.mbsc-textarea.mbsc-textfield-outline {
      height: 1.5em;
      padding-top: 0;
      padding-bottom: 0;
    }

    &.mbsc-textarea.mbsc-textfield-underline {
      padding-bottom: 0.3125em;
    }

    &.mbsc-textarea-inner.mbsc-textfield-inner-box {
      padding-top: 0.375em;
    }

    &.mbsc-textarea-inner.mbsc-textfield-inner-stacked,
    &.mbsc-textarea-inner.mbsc-textfield-inner-floating {
      padding-top: 1.625em;
    }

    &.mbsc-textarea-inner.mbsc-textfield-inner-outline {
      padding-top: 1em;
      padding-bottom: 1em;
    }

    &.mbsc-textarea-inner.mbsc-textfield-inner-underline {
      padding-top: 0.25em;
    }

    &.mbsc-textarea-inner.mbsc-textfield-inner-underline.mbsc-textfield-inner-stacked,
    &.mbsc-textarea-inner.mbsc-textfield-inner-underline.mbsc-textfield-inner-floating {
      padding-top: 1.125em;
    }

    /* Input tags */

    &.mbsc-textfield-tags-inner.mbsc-textfield-inner-underline,
    &.mbsc-textfield-tags-inner.mbsc-textfield-inner-inline {
      padding-top: 0;
    }

    &.mbsc-textfield-tags-inner.mbsc-textfield-inner-outline {
      padding-top: 0.625em;
      padding-bottom: 0.625em;
    }

    &.mbsc-textfield-tags.mbsc-textfield {
      padding-bottom: 0;
      min-height: 2.25em;
    }

    &.mbsc-textfield-tags.mbsc-textfield-outline {
      padding-top: 1px;
      padding-bottom: 1px;
    }

    &.mbsc-textfield-tag {
      border-radius: 1em;
    }
  }

  @include mbsc-material-input('material', $mbsc-material-colors);
  @include mbsc-material-input('material-dark', $mbsc-material-dark-colors);
}



@if ($mbsc-windows-theme and $mbsc-input) {
  .mbsc-windows {
    /* Wrapper */

    &.mbsc-textfield-wrapper {
      margin: 1em;
    }

    &.mbsc-textfield-wrapper-inline {
      margin: 1.75em 1em;
    }

    &.mbsc-textfield-wrapper-floating {
      padding-top: 1.75em;
    }

    /* Form element */

    &.mbsc-textfield {
      height: 2em;
      padding: 0 0.5em;
      border: 1px solid;
      border-radius: 2px;
    }

    /* Icons */

    &.mbsc-textfield-icon {
      top: 0.4375em;
      width: 1.125em;
      height: 1.125em;
      line-height: 1.125em;
      margin: 0 1px; // For border
    }

    &.mbsc-textfield-has-icon-right {
      padding-right: 2em;
    }

    &.mbsc-textfield-has-icon-left {
      padding-left: 2em;
    }

    &.mbsc-textfield-icon-left {
      left: 0.4375em;
    }

    &.mbsc-textfield-icon-right {
      right: 0.4375em;
    }

    /* Error  */

    &.mbsc-error-message {
      font-size: 0.75em;
    }

    /* Label */

    &.mbsc-label {
      line-height: 2em;
      font-size: 0.875em;
      font-weight: 600;
    }

    &.mbsc-label-inline {
      line-height: 2.285715em;
    }

    &.mbsc-label-floating {
      top: 2.142858em;
    }

    &.mbsc-label-stacked {
      position: static;
    }

    &.mbsc-label-floating.mbsc-ltr {
      transform: translateX(0.571429em);
    }

    &.mbsc-label-floating.mbsc-rtl {
      transform: translateX(-0.5em);
    }

    // TODO: check if simplify elsewhere too if there is a separate mbsc-label-{labeltype}-has-icon-{align} class?
    &.mbsc-label-underline-floating-has-icon-left.mbsc-ltr,
    &.mbsc-label-outline-floating-has-icon-left.mbsc-ltr,
    &.mbsc-label-box-floating-has-icon-left.mbsc-ltr {
      transform: translateX(2.285715em);
    }

    &.mbsc-label-underline-floating-has-icon-right.mbsc-rtl,
    &.mbsc-label-outline-floating-has-icon-right.mbsc-rtl,
    &.mbsc-label-box-floating-has-icon-right.mbsc-rtl {
      transform: translateX(-2.285715em);
    }

    &.mbsc-label.mbsc-label-floating-active {
      transform: translate(0, -2.142858em);
    }

    /* Underline input --------------------------------------------------------------------------- */

    /* Wrapper */
    &.mbsc-textfield-wrapper-underline {
      border-bottom: 1px solid;
    }

    /* Form element */
    &.mbsc-textfield-underline {
      border: 0;
      border-radius: 0;
    }

    /* ------------------------------------------------------------------------------------------- */

    /* Select */

    &.mbsc-select.mbsc-ltr {
      padding-right: 2em;
    }

    &.mbsc-select.mbsc-rtl {
      padding-left: 2em;
    }

    &.mbsc-select.mbsc-textfield-has-icon-right.mbsc-ltr {
      padding-right: 3.125em;
    }

    &.mbsc-select.mbsc-textfield-has-icon-left.mbsc-rtl {
      padding-left: 3.125em;
    }

    /* Select icon */

    &.mbsc-select-icon {
      top: 0.4375em;
      width: 1.125em;
      height: 1.125em;
      line-height: 1.125em;
      margin: 0 1px; // For border
    }

    &.mbsc-select-icon.mbsc-ltr {
      right: 0.4375em;
    }

    &.mbsc-select-icon.mbsc-rtl {
      left: 0.4375em;
    }

    &.mbsc-select-icon-right.mbsc-ltr {
      right: 2em;
    }

    &.mbsc-select-icon-left.mbsc-rtl {
      left: 2em;
    }

    /* Textarea */

    &.mbsc-textarea {
      height: 3.5em;
      padding-top: 0.1875em;
      padding-bottom: 0.1875em;
    }

    /* Input tags */

    &.mbsc-textfield.mbsc-textfield-tags {
      padding: 0.125em;
      min-height: 2em;
    }

    &.mbsc-textfield-tag {
      margin: 0.125em;
    }

    &.mbsc-textfield-tag.mbsc-ltr {
      margin-right: 0.125em;
    }

    &.mbsc-textfield-tag.mbsc-rtl {
      margin-left: 0.125em;
    }

    &.mbsc-textfield-tag-text {
      line-height: 1.571429em; // 22px
    }

    &.mbsc-textfield-tag-clear.mbsc-icon {
      width: 1.375em;
      height: 1.375em;
      border-width: 0.125em;
    }

    &.mbsc-textfield-tags-placeholder {
      padding: 0 0.375em;
      line-height: 1.625em;
    }
  }

  @include mbsc-windows-input('windows', $mbsc-windows-colors);
  @include mbsc-windows-input('windows-dark', $mbsc-windows-dark-colors);
}


@if $mbsc-input {
  // Dummy animation to detect initial autofill in webkit browsers
  @keyframes autofill {
    from {
      opacity: 1;
    }

    to {
      opacity: 1;
    }
  }

  /* Wrapper */

  .mbsc-textfield-wrapper {
    position: relative;
    display: block;
    margin: 0;
    z-index: 0;
    user-select: none;
  }

  .mbsc-textfield-wrapper-inline {
    display: flex;
    // align-items: center;
  }

  .mbsc-form-grid .mbsc-textfield-wrapper-box,
  .mbsc-form-grid .mbsc-textfield-wrapper-outline {
    margin: 0.75em 1em;
  }

  /* Inner wrapper */

  .mbsc-textfield-inner {
    box-sizing: border-box;
    position: relative;
    display: block;
  }

  .mbsc-textfield-inner-inline {
    flex: 1 auto;
  }

  /* Form element */

  .mbsc-textfield {
    display: block;
    width: 100%;
    font-size: 1em;
    margin: 0;
    padding: 0;
    border: 0;
    border-radius: 0;
    outline: 0;
    font-family: inherit;
    box-sizing: border-box;
    appearance: none;
    -webkit-appearance: none;
  }

  .mbsc-textfield:-webkit-autofill {
    animation-name: autofill;
  }

  .mbsc-textfield::-webkit-inner-spin-button {
    height: 2em;
    align-self: center;
  }

  .mbsc-textfield::-moz-placeholder {
    opacity: 0.5;
    color: inherit;
  }

  .mbsc-textfield::-webkit-input-placeholder {
    opacity: 0.5;
    color: inherit;
  }

  /* Floating label */

  .mbsc-textfield-floating:-ms-input-placeholder {
    color: transparent;
  }

  .mbsc-textfield-floating::-moz-placeholder {
    opacity: 0;
    transition: opacity 0.2s;
  }

  .mbsc-textfield-floating::-webkit-input-placeholder {
    opacity: 0;
    transition: opacity 0.2s;
  }

  .mbsc-textfield-floating::-webkit-datetime-edit {
    color: transparent;
  }

  .mbsc-textfield-floating-active:-ms-input-placeholder {
    color: inherit;
  }

  .mbsc-textfield-floating-active::-moz-placeholder {
    opacity: 0.5;
  }

  .mbsc-textfield-floating-active::-webkit-input-placeholder {
    opacity: 0.5;
  }

  .mbsc-textfield-floating-active::-webkit-datetime-edit {
    color: inherit;
  }

  .mbsc-textfield-floating .mbsc-textfield-tags-placeholder {
    opacity: 0;
    transition: opacity 0.2s;
  }

  .mbsc-textfield-floating-active .mbsc-textfield-tags-placeholder {
    opacity: 0.5;
  }

  .mbsc-textfield-fieldset {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: 0;
    padding: 0 1em;
    pointer-events: none;
  }

  /* Icon */

  .mbsc-textfield-icon {
    position: absolute;
    top: 0;
    text-align: center;
  }

  .mbsc-textfield-icon-left {
    left: 0;
  }

  .mbsc-textfield-icon-right {
    right: 0;
  }

  /* Label */

  .mbsc-label {
    position: absolute;
    z-index: 1;
    top: 0;
    line-height: 1em;
    white-space: nowrap;
    text-overflow: ellipsis;
    pointer-events: none;
  }

  .mbsc-label-inline {
    position: static;
    overflow: hidden;
    flex: 0 0 auto;
    width: 30%;
    max-width: 12.5em;
    // box-sizing: content-box;
    box-sizing: border-box;
    pointer-events: auto;
  }

  .mbsc-label-floating-animate {
    transition: transform 0.2s;
  }

  .mbsc-label-floating.mbsc-ltr {
    transform-origin: top left;
  }

  .mbsc-label-floating.mbsc-rtl {
    transform-origin: top right;
  }

  /* Error message */

  .mbsc-error-message {
    position: absolute;
    top: 100%;
    font-size: 0.75em;
    line-height: 1.5em;
  }

  /* File input */

  .mbsc-textfield-file {
    position: absolute;
    left: 0;
    opacity: 0;
  }

  /* Select */

  .mbsc-select {
    cursor: pointer;
  }

  .mbsc-select-icon {
    position: absolute;
    text-align: center;
    pointer-events: none;
  }

  /* Textarea */

  .mbsc-textarea {
    resize: none;
    overflow: hidden;
    line-height: 1.5em;
  }

  /* Password toggle */

  .mbsc-toggle-icon:hover {
    cursor: pointer;
  }

  /* Input tags */

  .mbsc-textfield.mbsc-textfield-hidden.mbsc-textarea {
    padding: 0;
    width: 100%;
    height: 100%;
    border: 0;
    position: absolute;
    z-index: -1;
    top: 0;
    left: 0;
    opacity: 0;
  }

  .mbsc-textfield.mbsc-textfield-tags.mbsc-textarea {
    display: block;
    overflow: auto;
    height: auto;
    max-height: 5.5em;
  }

  .mbsc-textfield-tag {
    display: inline-block;
    margin: 0.25em 0;
    line-height: normal;
  }

  .mbsc-textfield-tag.mbsc-ltr {
    margin-right: 0.5em;
    padding-left: 0.75em;
  }

  .mbsc-textfield-tag.mbsc-rtl {
    margin-left: 0.5em;
    padding-right: 0.75em;
  }

  .mbsc-textfield-tag-text {
    font-size: 0.875em;
    line-height: 1.857143em; // 26px
  }

  .mbsc-textfield-tag-clear.mbsc-icon {
    width: 1.625em;
    height: 1.625em;
    vertical-align: top;
    border: 0.25em solid transparent;
    box-sizing: border-box;
    cursor: pointer;
  }

  .mbsc-textfield-tags-placeholder {
    opacity: 0.5;
    line-height: 2.125em;
  }
}



@if ($mbsc-ios-theme and $mbsc-datepicker) {
  .mbsc-ios {
    &.mbsc-picker {
      .mbsc-popup-overlay-top,
      .mbsc-popup-overlay-bottom {
        background: none;
      }
    }

    &.mbsc-datepicker-inline {
      border-top: 1px solid;
      border-bottom: 1px solid;
      margin-top: -1px;
      margin-bottom: -1px;
      z-index: 2;
      position: relative;
    }

    &.mbsc-datepicker .mbsc-calendar-grid,
    &.mbsc-datepicker .mbsc-calendar-cell,
    &.mbsc-datepicker .mbsc-calendar-cell-inner {
      border-color: transparent;
    }

    &.mbsc-datepicker .mbsc-selected .mbsc-calendar-day-text,
    &.mbsc-datepicker .mbsc-highlighted .mbsc-calendar-day-text,
    &.mbsc-datepicker .mbsc-range-hover-start .mbsc-calendar-day-text,
    &.mbsc-datepicker .mbsc-range-hover-end .mbsc-calendar-day-text,
    &.mbsc-datepicker .mbsc-hover .mbsc-calendar-day-text {
      width: 1.444445em;
      height: 1.444445em;
      margin: 0.16667em;
      font-size: 1.125em;
      line-height: 1.444445em;
    }

    &.mbsc-picker-header {
      line-height: 1.25em;
      padding: 0.75em 4.375em;
      font-weight: bold;
      text-align: center;
      border-bottom: 1px solid;
    }

    /* Range Controls */

    &.mbsc-range-control-wrapper {
      padding: 0 0.75em;
      overflow: hidden;
    }

    &.mbsc-range-control-wrapper .mbsc-segmented {
      width: 17em; // 272px
      margin-left: auto;
      margin-right: auto;
      max-width: 100%;
      box-sizing: border-box;
    }

    // Segmented width based on controls

    &.mbsc-datepicker-control-calendar.mbsc-datepicker-control-timegrid .mbsc-range-control-wrapper .mbsc-segmented,
    &.mbsc-datepicker-control-calendar.mbsc-datepicker-control-time .mbsc-range-control-wrapper .mbsc-segmented,
    &.mbsc-datepicker-control-date.mbsc-datepicker-control-time .mbsc-range-control-wrapper .mbsc-segmented,
    &.mbsc-datepicker-control-datetime .mbsc-range-control-wrapper .mbsc-segmented {
      width: 25em; // 400px: default format can show -> "09/25/2020 12:30 AM"
    }

    &.mbsc-range-control-wrapper .mbsc-segmented-button {
      display: block;
      padding: 0 0.5em;
    }

    &.mbsc-range-control-wrapper .mbsc-range-value-nonempty .mbsc-segmented-button {
      padding: 0 2.0625em 0 0.5em;
    }

    &.mbsc-range-control-wrapper .mbsc-range-value-nonempty .mbsc-segmented-button.mbsc-rtl {
      padding: 0 0.5em 0 2.0625em;
    }

    &.mbsc-range-control-label,
    &.mbsc-range-control-value {
      text-align: left;
      line-height: 1.538462em; // 20px

      &.active {
        font-weight: 600;
      }

      &.mbsc-rtl {
        text-align: right;
      }
    }

    &.mbsc-range-control-label {
      padding: 0.615385em 0.615385em 0 0.615385em; // 8px
    }

    &.mbsc-range-control-value {
      padding: 0 0.615385em 0.615385em 0.615385em; // 8px
      text-overflow: ellipsis;
      overflow: hidden;

      &.active {
        padding: 0 0.571429em 0.571429em 0.571429em; // 8px
        font-size: 1.076923em; // 14px
        line-height: 1.428572em; // 20px
      }
    }

    &.mbsc-range-label-clear {
      margin-top: -0.692308em; // 9px
      width: 1.307693em; // 17px
      height: 1.307693em;

      &.mbsc-ltr {
        right: 1em;
      }

      &.mbsc-rtl {
        left: 1em;
      }
    }
  }

  @include mbsc-ios-datepicker('ios', $mbsc-ios-colors);
  @include mbsc-ios-datepicker('ios-dark', $mbsc-ios-dark-colors);
}



@if ($mbsc-material-theme and $mbsc-datepicker) {
  .mbsc-material {
    &.mbsc-picker-header {
      font-size: 1.25em;
      font-weight: 500;
      padding: 0.8em 0.8em 0 0.8em;
      line-height: 1.4em;
      text-align: center;
    }

    /* Range Control */

    // Overrides for the segmented
    &.mbsc-range-control-wrapper {
      .mbsc-segmented {
        padding: 0;
        max-width: 100%;
        width: 18.5em; // 296px
      }

      .mbsc-segmented-button.mbsc-button.mbsc-font {
        background: none;
        border: 0;
        border-bottom: 2px solid transparent;
        border-radius: 0;
        padding: 0.8571422em 1.142857em; // 12px 16px (14px based)
        display: block;
      }

      .mbsc-segmented-button.mbsc-ltr {
        text-align: left;
      }

      .mbsc-segmented-button.mbsc-rtl {
        text-align: right;
      }

      .mbsc-range-value-nonempty .mbsc-segmented-button.mbsc-ltr {
        padding-right: 2.642857em; // 37px (14px based)
      }

      .mbsc-range-value-nonempty .mbsc-segmented-button.mbsc-rtl {
        padding-left: 2.642857em; // 37px (14px based)
      }
    }

    // Segmented width based on controls

    &.mbsc-datepicker-control-calendar.mbsc-datepicker-control-timegrid .mbsc-range-control-wrapper .mbsc-segmented,
    &.mbsc-datepicker-control-calendar.mbsc-datepicker-control-time .mbsc-range-control-wrapper .mbsc-segmented,
    &.mbsc-datepicker-control-date.mbsc-datepicker-control-time .mbsc-range-control-wrapper .mbsc-segmented,
    &.mbsc-datepicker-control-datetime .mbsc-range-control-wrapper .mbsc-segmented {
      width: 25em; // 400px: default format can show -> "09/25/2020 12:30 AM"
    }

    &.mbsc-range-control-label {
      text-transform: uppercase;
    }

    &.mbsc-range-control-value {
      font-weight: bold;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &.mbsc-range-label-clear {
      margin-top: -0.625em; // 10px
      right: 0.75em; // 12px
      width: 1.3125em; // 21px
      height: 1.3125em;

      &.mbsc-ltr {
        right: 0.75em;
      }

      &.mbsc-rtl {
        left: 0.75em;
      }
    }

    // Reduce calendar title font size

    &.mbsc-datepicker .mbsc-calendar-title {
      font-size: 1.142858em;
      line-height: 1.75em;
    }

    &.mbsc-calendar-grid .mbsc-calendar-title {
      font-size: 1.428572em;
      line-height: 1.4em;
    }
  }

  @include mbsc-material-datepicker('material', $mbsc-material-colors);
  @include mbsc-material-datepicker('material-dark', $mbsc-material-dark-colors);
}



@if ($mbsc-windows-theme and $mbsc-datepicker) {
  .mbsc-windows {
    &.mbsc-picker-header {
      padding: 0.5em;
      font-weight: bold;
      text-align: center;
      border-bottom: 1px solid;
    }

    &.mbsc-datepicker-tab {
      border-top: 1px solid;
      margin-top: -1px;
    }

    /* Range Control */

    // overwrites for the segmented
    &.mbsc-range-control-wrapper {
      .mbsc-segmented {
        padding: 0;
        max-width: 100%;
        width: 18.5em; // 296px
      }

      .mbsc-segmented-button.mbsc-button.mbsc-font {
        background: none;
        border: 0;
        border-bottom: 2px solid transparent;
        padding: 0.625em 1em; // 10px 8px;
        line-height: 1.25em; // 20px
        display: block;
        text-align: left;
      }

      .mbsc-segmented-button.mbsc-rtl {
        text-align: right;
      }

      .mbsc-range-value-nonempty .mbsc-segmented-button.mbsc-ltr {
        padding-right: 1.875em; // 10px 28px 10px 8px;
      }

      .mbsc-range-value-nonempty .mbsc-segmented-button.mbsc-rtl {
        padding-left: 1.875em;
      }
    }

    // Segmented width based on controls

    &.mbsc-datepicker-control-calendar.mbsc-datepicker-control-timegrid .mbsc-range-control-wrapper .mbsc-segmented,
    &.mbsc-datepicker-control-calendar.mbsc-datepicker-control-time .mbsc-range-control-wrapper .mbsc-segmented,
    &.mbsc-datepicker-control-date.mbsc-datepicker-control-time .mbsc-range-control-wrapper .mbsc-segmented,
    &.mbsc-datepicker-control-datetime .mbsc-range-control-wrapper .mbsc-segmented {
      width: 26.25em; // 420px: default format can show -> "09/25/2020 12:30 AM"
    }

    &.mbsc-range-control-label {
      font-size: 0.9375em; // 15px
      line-height: 1.6em; // 24px
    }

    &.mbsc-range-control-value {
      font-size: 1.0625em; // 17px
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &.mbsc-range-label-clear {
      width: 1em; // 16px
      height: 1em;
      right: 0.4375em; // 7px;
      margin-top: -0.5em;

      &.mbsc-ltr {
        right: 0.4375em; // 7px;
      }

      &.mbsc-rtl {
        left: 0.4375em; // 7px
      }
    }
  }

  @include mbsc-windows-datepicker('windows', $mbsc-windows-colors);
  @include mbsc-windows-datepicker('windows-dark', $mbsc-windows-dark-colors);
}


@if $mbsc-datepicker {
  /* Needed for angular */
  mbsc-datetime,
  mbsc-date,
  mbsc-time {
    display: block;
    width: 100%;
  }

  .mbsc-datepicker-inline {
    height: 100%;
  }

  .mbsc-datepicker .mbsc-calendar {
    height: 100%;
    max-width: 100%;
    padding-bottom: 0.5em;
    box-sizing: border-box;
    // For IE11
    display: block;
  }

  .mbsc-datepicker .mbsc-calendar-header .mbsc-calendar-week-days {
    padding: 0 0.5em;
  }

  /* Start/end controls */

  .mbsc-range-control-wrapper .mbsc-segmented-input {
    // v4 compatibility
    width: 1px;
  }

  .mbsc-range-label-clear {
    position: absolute;
    top: 50%;
  }

  /* Tabs */

  .mbsc-datepicker-tab-wrapper {
    position: relative;
    flex-wrap: wrap;
    justify-content: center;
    overflow: hidden;
  }

  .mbsc-datepicker-tab {
    visibility: hidden;
    max-width: 100%;
    align-items: center;
    justify-content: center;
  }

  .mbsc-datepicker-tab-expand {
    height: 100%;
  }

  .mbsc-datepicker-tab-active {
    visibility: visible;
  }

  .mbsc-datepicker-time-modal {
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    right: 0;
    max-width: none;
    height: 100%;
  }

  .mbsc-datepicker .mbsc-calendar-slide {
    padding: 0 0.5em;
  }
}



@if ($mbsc-ios-theme and $mbsc-forms) {
  .mbsc-ios {
    &.mbsc-checkbox-right {
      padding: 0.875em 3.75em 0.875em 1em;
    }

    &.mbsc-checkbox-left {
      padding: 0.875em 1em 0.875em 3.75em;
    }

    &.mbsc-checkbox-box {
      width: 1.75em;
      height: 1.75em;
      margin-top: -0.875em;
      border: 0.125em solid currentColor;
      border-radius: 2em;
    }

    &.mbsc-checkbox-box-right {
      right: 1em;
    }

    &.mbsc-checkbox-box-left {
      left: 1em;
    }

    &.mbsc-checkbox-box:after {
      top: 32%;
      left: 26%;
      width: 0.75em;
      height: 0.375em;
      border: 0.125em solid currentColor;
      border-top: 0;
      border-right: 0;
      transition: opacity 0.2s ease-in-out;
    }

    &.mbsc-checkbox-box:before {
      content: '';
      position: absolute;
      top: -0.5em;
      left: -0.5em;
      z-index: -1;
      width: 2.5em;
      height: 2.5em;
      opacity: 0;
      border-radius: 2.5em;
      background: #ccc;
      transition: opacity 0.2s ease-in-out;
    }

    &.mbsc-checkbox-box.mbsc-focus:before {
      opacity: 0.12;
    }

    &.mbsc-checkbox-box.mbsc-disabled {
      opacity: 0.3;
    }

    /* Color presets */

    &.mbsc-checkbox-box.mbsc-checkbox-box-primary {
      color: $mbsc-ios-primary;
    }

    &.mbsc-checkbox-box.mbsc-checkbox-box-secondary {
      color: $mbsc-ios-secondary;
    }

    &.mbsc-checkbox-box.mbsc-checkbox-box-success {
      color: $mbsc-ios-success;
    }

    &.mbsc-checkbox-box.mbsc-checkbox-box-danger {
      color: $mbsc-ios-danger;
    }

    &.mbsc-checkbox-box.mbsc-checkbox-box-warning {
      color: $mbsc-ios-warning;
    }

    &.mbsc-checkbox-box.mbsc-checkbox-box-info {
      color: $mbsc-ios-info;
    }
  }

  @include mbsc-ios-checkbox('ios', $mbsc-ios-colors);
  @include mbsc-ios-checkbox('ios-dark', $mbsc-ios-dark-colors);
}



@if ($mbsc-material-theme and $mbsc-forms) {
  .mbsc-material {
    &.mbsc-checkbox-right {
      padding: 0.9375em 3.5em 0.9375em 1em;
    }

    &.mbsc-checkbox-left {
      padding: 0.9375em 1em 0.9375em 3.5em;
    }

    &.mbsc-checkbox-box {
      border-radius: 0.1875em;
      width: 1.125em;
      height: 1.125em;
      margin-top: -0.5625em;
      border: 0.125em solid;
      transition: background-color 0.1s ease-out;
    }

    &.mbsc-checkbox-box-right {
      right: 1.25em;
    }

    &.mbsc-checkbox-box-left {
      left: 1.25em;
    }

    &.mbsc-checkbox-box:after {
      top: 0.125em;
      left: 0.0625em;
      width: 0.8125em;
      height: 0.4375em;
      opacity: 1;
      border: 0.125em solid;
      border-top: 0;
      border-right: 0;
      transform: scale(0) rotate(-45deg);
      transition: transform 0.1s ease-out;
    }

    &.mbsc-checkbox-box:before {
      content: '';
      position: absolute;
      top: -0.8125em;
      left: -0.8125em;
      z-index: -1;
      width: 2.5em;
      height: 2.5em;
      opacity: 0;
      border-radius: 2.5em;
      background: currentColor;
      transition: opacity 0.2s ease-in-out;
    }

    &.mbsc-checkbox-box.mbsc-focus:before {
      opacity: 0.12;
    }

    &.mbsc-checkbox-box.mbsc-active:before {
      opacity: 0.2;
    }

    &.mbsc-checkbox-box.mbsc-checked {
      background: currentColor;
      border-color: currentColor;
    }

    &.mbsc-checkbox-box.mbsc-checked:after {
      transform: scale(1) rotate(-45deg);
    }

    &.mbsc-checkbox-box.mbsc-disabled {
      opacity: 0.3;
    }

    /* Color presets */

    &.mbsc-checkbox-box.mbsc-checkbox-box-primary {
      color: $mbsc-material-primary;
    }

    &.mbsc-checkbox-box.mbsc-checkbox-box-secondary {
      color: $mbsc-material-secondary;
    }

    &.mbsc-checkbox-box.mbsc-checkbox-box-success {
      color: $mbsc-material-success;
    }

    &.mbsc-checkbox-box.mbsc-checkbox-box-danger {
      color: $mbsc-material-danger;
    }

    &.mbsc-checkbox-box.mbsc-checkbox-box-warning {
      color: $mbsc-material-warning;
    }

    &.mbsc-checkbox-box.mbsc-checkbox-box-info {
      color: $mbsc-material-info;
    }
  }

  @include mbsc-material-checkbox('material', $mbsc-material-colors);
  @include mbsc-material-checkbox('material-dark', $mbsc-material-dark-colors);
}



@if ($mbsc-windows-theme and $mbsc-forms) {
  .mbsc-windows {
    &.mbsc-checkbox-left {
      padding: 1.125em 1em 1.125em 2.875em;
    }

    &.mbsc-checkbox-right {
      padding: 1.125em 2.875em 1.125em 1em;
    }

    &.mbsc-checkbox-box {
      margin-top: -0.6875em;
      width: 1.3125em;
      height: 1.3125em;
      border: 0.125em solid;
    }

    &.mbsc-checkbox-box-left {
      left: 1em;
    }

    &.mbsc-checkbox-box-right {
      right: 1em;
    }

    &.mbsc-checkbox-box:after {
      top: 16%;
      left: 10%;
      width: 0.875em;
      height: 0.475em;
      border: 0.125em solid;
      border-top: 0;
      border-right: 0;
    }

    &.mbsc-checkbox-box:before {
      content: '';
      position: absolute;
      top: -0.75em;
      left: -0.75em;
      z-index: -1;
      width: 2.5em;
      height: 2.5em;
      opacity: 0;
      border-radius: 2.5em;
      transition: opacity 0.2s ease-in-out;
    }

    &.mbsc-checkbox-box.mbsc-focus:before {
      opacity: 0.12;
    }

    &.mbsc-checkbox-box.mbsc-checked {
      background: currentColor;
      border-color: currentColor;
    }

    &.mbsc-checkbox-box.mbsc-disabled {
      opacity: 0.3;
    }

    /* Color presets */

    &.mbsc-checkbox-box.mbsc-checkbox-box-primary {
      color: $mbsc-windows-primary;
    }

    &.mbsc-checkbox-box.mbsc-checkbox-box-secondary {
      color: $mbsc-windows-secondary;
    }

    &.mbsc-checkbox-box.mbsc-checkbox-box-success {
      color: $mbsc-windows-success;
    }

    &.mbsc-checkbox-box.mbsc-checkbox-box-danger {
      color: $mbsc-windows-danger;
    }

    &.mbsc-checkbox-box.mbsc-checkbox-box-warning {
      color: $mbsc-windows-warning;
    }

    &.mbsc-checkbox-box.mbsc-checkbox-box-info {
      color: $mbsc-windows-info;
    }
  }

  @include mbsc-windows-checkbox('windows', $mbsc-windows-colors);
  @include mbsc-windows-checkbox('windows-dark', $mbsc-windows-dark-colors);
}


@if $mbsc-forms {
  .mbsc-checkbox {
    line-height: 1.25em;
    position: relative;
    display: block;
    margin: 0;
    z-index: 0;
    user-select: none;
  }

  .mbsc-checkbox-box {
    box-sizing: border-box;
    position: absolute;
    top: 50%;
    display: block;
    width: 1.375em;
    height: 1.375em;
  }

  .mbsc-checkbox-box:after {
    content: '';
    box-sizing: border-box;
    position: absolute;
    display: block;
    opacity: 0;
    transform: rotate(-45deg);
  }

  .mbsc-checkbox-box.mbsc-checked:after {
    opacity: 1;
  }

  .mbsc-description {
    display: block;
    font-size: 0.75em;
    opacity: 0.6;
  }
}



@if ($mbsc-ios-theme and $mbsc-forms) {
  .mbsc-ios {
    .mbsc-block-title,
    .mbsc-form-group-title {
      padding: 0.5em 1.333334em;
      font-size: 0.75em;
      line-height: 1.5em;
      text-transform: uppercase;
    }
  }

  @include mbsc-ios-page('ios', $mbsc-ios-colors);
  @include mbsc-ios-page('ios-dark', $mbsc-ios-dark-colors);
}



@if ($mbsc-material-theme and $mbsc-forms) {
  .mbsc-material {
    .mbsc-block-title,
    .mbsc-form-group-title {
      padding: 0.666667em 1.333334em;
      font-size: 0.75em;
      text-transform: uppercase;
    }
  }

  @include mbsc-material-page('material', $mbsc-material-colors);
  @include mbsc-material-page('material-dark', $mbsc-material-dark-colors);
}



@if ($mbsc-windows-theme and $mbsc-forms) {
  .mbsc-windows {
    .mbsc-block-title,
    .mbsc-form-group-title {
      font-size: 1.5em;
      padding: 0 0.666667em;
      line-height: 2em;
    }
  }

  @include mbsc-windows-page('windows', $mbsc-windows-colors);
  @include mbsc-windows-page('windows-dark', $mbsc-windows-dark-colors);
}


@if $mbsc-forms {
  .mbsc-page {
    display: block;
    min-height: 100%;
    padding: var(--mbsc-safe-top) var(--mbsc-safe-right) var(--mbsc-safe-bottom) var(--mbsc-safe-left);
  }

  .mbsc-page:before,
  .mbsc-page:after {
    content: '';
    display: table;
  }

  /* Block */

  .mbsc-block,
  .mbsc-form-group {
    margin: 1.5em 0;
  }

  .mbsc-form-group-inset {
    margin: 2em 1.5em;
  }

  .mbsc-form-grid .mbsc-form-group-title {
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
  }

  /* Typography */

  .mbsc-page h1,
  .mbsc-page h2,
  .mbsc-page h3,
  .mbsc-page h4,
  .mbsc-page h5,
  .mbsc-page h6,
  .mbsc-h1,
  .mbsc-h2,
  .mbsc-h3,
  .mbsc-h4,
  .mbsc-h5,
  .mbsc-h6 {
    margin: 0;
    padding: 0;
    color: inherit;
    font-weight: normal;
    font-family: inherit;
  }

  .mbsc-page p,
  .mbsc-p {
    margin: 1em 0;
    padding: 0;
    line-height: 1.5;
  }

  .mbsc-page a,
  .mbsc-a {
    text-decoration: none;
  }

  .mbsc-page a:hover,
  .mbsc-a:hover {
    text-decoration: underline;
  }

  .mbsc-page a.mbsc-btn:hover,
  .mbsc-a.mbsc-btn:hover {
    text-decoration: none;
  }

  .mbsc-page h1,
  .mbsc-h1 {
    margin: 0.347826em 0;
    font-size: 2.875em;
  }

  .mbsc-page h2,
  .mbsc-h2 {
    margin: 0.470588em 0;
    font-size: 2.125em;
  }

  .mbsc-page h3,
  .mbsc-h3 {
    margin: 0.666666em 0;
    font-size: 1.5em;
  }

  .mbsc-page h4,
  .mbsc-h4 {
    margin: 0.8em 0;
    font-size: 1.25em;
  }

  .mbsc-page h5,
  .mbsc-h5 {
    margin: 1.066666em 0;
    font-size: 0.9375em;
  }

  .mbsc-page h6,
  .mbsc-h6 {
    margin: 1.333333em 0;
    font-size: 0.75em;
  }

  /* Padding, margin */

  .mbsc-padding {
    padding: 1em;
  }

  .mbsc-padding > p:first-child {
    margin-top: 0;
  }

  .mbsc-padding > p:last-child {
    margin-bottom: 0;
  }

  .mbsc-margin {
    margin: 1em 0;
  }

  .mbsc-margin:first-child {
    margin-top: 0;
  }

  .mbsc-margin:last-child {
    margin-bottom: 0;
  }

  /* Lists */

  .mbsc-page ul,
  .mbsc-page ol,
  .mbsc-ul,
  .mbsc-ol {
    padding: 0;
    margin: 1em 0 1em 1.25em;
    line-height: 1.5;
  }

  .mbsc-page ul ul,
  .mbsc-page ol ol,
  .mbsc-ul .mbsc-ul,
  .mbsc-ol .mbsc-ol {
    margin: 0 0 0 1.25em;
  }

  /* Font sizes */

  .mbsc-txt-xs {
    font-size: 0.625em;
  }

  .mbsc-txt-s {
    font-size: 0.75em;
  }

  .mbsc-txt-m {
    font-size: 1.25em;
  }

  .mbsc-txt-l {
    font-size: 1.5em;
  }

  .mbsc-txt-xl {
    font-size: 2em;
  }

  .mbsc-txt-muted {
    opacity: 0.6;
  }

  /* Line heights */
  .mbsc-line-height-xs {
    line-height: 1;
  }

  .mbsc-line-height-s {
    line-height: 1.25;
  }

  .mbsc-line-height-m {
    line-height: 1.5;
  }

  .mbsc-line-height-l {
    line-height: 1.75;
  }

  .mbsc-line-height-xl {
    line-height: 2;
  }

  /* Font weights */

  .mbsc-ultra-bold {
    font-weight: 900;
  }

  .mbsc-bold {
    font-weight: bold;
  }

  .mbsc-medium {
    font-weight: 500;
  }

  .mbsc-light {
    font-weight: 300;
  }

  .mbsc-thin {
    font-weight: 100;
  }

  .mbsc-italic {
    font-style: italic;
  }

  /* Text align */

  .mbsc-align-left {
    text-align: left;
  }

  .mbsc-align-right {
    text-align: right;
  }

  .mbsc-align-center {
    text-align: center;
  }

  .mbsc-align-justify {
    text-align: justify;
  }

  /* Float */

  .mbsc-pull-right {
    float: right;
  }

  .mbsc-pull-left {
    float: left;
  }

  /* Image section */

  .mbsc-media-fluid {
    display: block;
    width: 100%;
  }

  .mbsc-img-thumbnail {
    width: 6em;
    height: 6em;
    margin: 1em;
  }

  /* Avatar image */

  .mbsc-avatar {
    width: 2.5em;
    height: 2.5em;
    padding: 0;
    border-radius: 1.25em;
  }

  /* Note */

  .mbsc-note {
    position: relative;
    padding: 0.75em 1.25em;
    margin: 1em;
    border: 1px solid transparent;
    font-size: 0.875em;
  }

  @media (max-width: 600px) {
    .mbsc-note {
      text-align: center;
    }
  }
}



@if ($mbsc-ios-theme and $mbsc-forms) {
  .mbsc-ios {
    &.mbsc-radio-left {
      padding: 0.875em 1em 0.875em 3.75em;
    }

    &.mbsc-radio-right {
      padding: 0.875em 3.75em 0.875em 1em;
    }

    &.mbsc-radio-box:after {
      top: 44%;
      left: 23%;
      width: 0.875em;
      height: 0.375em;
      border: 0.125em solid;
      border-top: 0;
      border-right: 0;
      border-radius: 0;
      transform: rotate(-45deg);
      transition: opacity 0.2s ease-in-out;
    }

    &.mbsc-radio-box:before {
      content: '';
      position: absolute;
      top: -0.75em;
      left: -0.75em;
      z-index: -1;
      width: 2.5em;
      height: 2.5em;
      opacity: 0;
      border-radius: 2.5em;
      transition: opacity 0.2s ease-in-out;
      background: #ccc;
    }

    &.mbsc-radio-box.mbsc-focus:before {
      opacity: 0.12;
    }

    &.mbsc-radio-box-left {
      left: 1.125em;
    }

    &.mbsc-radio-box-right {
      right: 1.125em;
    }

    &.mbsc-radio-box.mbsc-disabled {
      opacity: 0.3;
    }

    /* Color presets */

    &.mbsc-radio-box.mbsc-radio-box-primary:after {
      border-color: $mbsc-ios-primary;
    }

    &.mbsc-radio-box.mbsc-radio-box-secondary:after {
      border-color: $mbsc-ios-secondary;
    }

    &.mbsc-radio-box.mbsc-radio-box-success:after {
      border-color: $mbsc-ios-success;
    }

    &.mbsc-radio-box.mbsc-radio-box-danger:after {
      border-color: $mbsc-ios-danger;
    }

    &.mbsc-radio-box.mbsc-radio-box-warning:after {
      border-color: $mbsc-ios-warning;
    }

    &.mbsc-radio-box.mbsc-radio-box-info:after {
      border-color: $mbsc-ios-info;
    }
  }

  @include mbsc-ios-radio('ios', $mbsc-ios-colors);
  @include mbsc-ios-radio('ios-dark', $mbsc-ios-dark-colors);
}



@if ($mbsc-material-theme and $mbsc-forms) {
  .mbsc-material {
    &.mbsc-radio-left {
      padding: 0.9375em 1em 0.9375em 3.5em;
    }

    &.mbsc-radio-right {
      padding: 0.9375em 3.5em 0.9375em 1em;
    }

    &.mbsc-radio-box {
      border: 0.125em solid;
      transition: background-color 0.1s ease-out;
    }

    &.mbsc-radio-box-left {
      left: 1.125em;
    }

    &.mbsc-radio-box-right {
      right: 1.125em;
    }

    &.mbsc-radio-box:before {
      content: '';
      position: absolute;
      top: -0.75em;
      left: -0.75em;
      z-index: -1;
      width: 2.5em;
      height: 2.5em;
      opacity: 0;
      border-radius: 2.5em;
      transition: opacity 0.2s ease-in-out;
      background: currentColor;
    }

    &.mbsc-radio-box.mbsc-focus:before {
      opacity: 0.12;
    }

    &.mbsc-radio-box.mbsc-active:before {
      opacity: 0.2;
    }

    &.mbsc-radio-box:after {
      background: currentColor;
      transform: scale(0);
      transition: transform 0.1s ease-out;
    }

    &.mbsc-radio-box.mbsc-checked {
      border-color: currentColor;
    }

    &.mbsc-radio-box.mbsc-checked:after {
      transform: scale(1);
    }

    &.mbsc-radio-box.mbsc-disabled {
      opacity: 0.3;
    }

    /* Color presets */

    &.mbsc-radio-box.mbsc-radio-box-primary {
      color: $mbsc-material-primary;
    }

    &.mbsc-radio-box.mbsc-radio-box-secondary {
      color: $mbsc-material-secondary;
    }

    &.mbsc-radio-box.mbsc-radio-box-success {
      color: $mbsc-material-success;
    }

    &.mbsc-radio-box.mbsc-radio-box-danger {
      color: $mbsc-material-danger;
    }

    &.mbsc-radio-box.mbsc-radio-box-warning {
      color: $mbsc-material-warning;
    }

    &.mbsc-radio-box.mbsc-radio-box-info {
      color: $mbsc-material-info;
    }
  }

  @include mbsc-material-radio('material', $mbsc-material-colors);
  @include mbsc-material-radio('material-dark', $mbsc-material-dark-colors);
}



@if ($mbsc-windows-theme and $mbsc-forms) {
  .mbsc-windows {
    &.mbsc-radio-left {
      padding: 1.125em 1em 1.125em 3.25em;
    }

    &.mbsc-radio-right {
      padding: 1.125em 3.25em 1.125em 1em;
    }

    &.mbsc-radio-box-left {
      left: 1em;
    }

    &.mbsc-radio-box-right {
      right: 1em;
    }

    &.mbsc-radio-box:before {
      content: '';
      position: absolute;
      top: -0.75em;
      left: -0.75em;
      z-index: -1;
      width: 2.5em;
      height: 2.5em;
      opacity: 0;
      border-radius: 2.5em;
      transition: opacity 0.2s ease-in-out;
      background: #ccc;
    }

    &.mbsc-radio-box.mbsc-focus:before {
      opacity: 0.12;
    }

    /* Color presets */

    &.mbsc-radio-box.mbsc-radio-box-primary.mbsc-checked {
      border-color: $mbsc-windows-primary;
    }

    &.mbsc-radio-box.mbsc-radio-box-secondary.mbsc-checked {
      border-color: $mbsc-windows-secondary;
    }

    &.mbsc-radio-box.mbsc-radio-box-success.mbsc-checked {
      border-color: $mbsc-windows-success;
    }

    &.mbsc-radio-box.mbsc-radio-box-danger.mbsc-checked {
      border-color: $mbsc-windows-danger;
    }

    &.mbsc-radio-box.mbsc-radio-box-warning.mbsc-checked {
      border-color: $mbsc-windows-warning;
    }

    &.mbsc-radio-box.mbsc-radio-box-info.mbsc-checked {
      border-color: $mbsc-windows-info;
    }
  }

  @include mbsc-windows-radio('windows', $mbsc-windows-colors);
  @include mbsc-windows-radio('windows-dark', $mbsc-windows-dark-colors);
}


@if $mbsc-forms {
  .mbsc-radio {
    position: relative;
    display: block;
    margin: 0;
    z-index: 0;
    line-height: 1.25em;
    user-select: none;
  }

  .mbsc-radio-box {
    position: absolute;
    top: 50%;
    display: block;
    width: 1.25em;
    height: 1.25em;
    margin-top: -0.625em;
    border-radius: 1.25em;
    box-sizing: border-box;
  }

  .mbsc-radio-box:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0.625em;
    height: 0.625em;
    margin-top: -0.3125em;
    margin-left: -0.3125em;
    border-radius: 0.625em;
    opacity: 0;
  }

  .mbsc-radio-box.mbsc-checked:after {
    opacity: 1;
  }
}



@if ($mbsc-ios-theme and $mbsc-forms) {
  .mbsc-ios {
    &.mbsc-stepper-input {
      margin-left: -1px;
      margin-right: -1px;
      z-index: 1;
      border: 1px solid;
    }

    &.mbsc-stepper-button.mbsc-button {
      line-height: 1.625em;
    }

    &.mbsc-stepper-button.mbsc-disabled {
      opacity: 1;
    }

    &.mbsc-stepper-inner {
      font-weight: normal;
    }

    &.mbsc-disabled .mbsc-stepper-inner {
      opacity: 0.2;
    }

    &.mbsc-stepper-plus:before {
      content: '';
      position: absolute;
      border-left: 1px solid;
      top: 0.4125em;
      bottom: 0.4125em;
    }

    &.mbsc-stepper-plus.mbsc-ltr:before {
      left: 0;
    }

    &.mbsc-stepper-plus.mbsc-rtl:before {
      right: 0;
    }

    &.mbsc-stepper-center .mbsc-stepper-plus:before {
      display: none;
    }

    // button colors

    &.mbsc-primary .mbsc-stepper-button {
      color: $mbsc-ios-primary;
    }

    &.mbsc-secondary .mbsc-stepper-button {
      color: $mbsc-ios-secondary;
    }

    &.mbsc-success .mbsc-stepper-button {
      color: $mbsc-ios-success;
    }

    &.mbsc-danger .mbsc-stepper-button {
      color: $mbsc-ios-danger;
    }

    &.mbsc-warning .mbsc-stepper-button {
      color: $mbsc-ios-warning;
    }

    &.mbsc-info .mbsc-stepper-button {
      color: $mbsc-ios-info;
    }
  }

  @include mbsc-ios-stepper('ios', $mbsc-ios-colors);
  @include mbsc-ios-stepper('ios-dark', $mbsc-ios-dark-colors);
}



@if ($mbsc-material-theme and $mbsc-forms) {
  .mbsc-material {
    &.mbsc-stepper-control {
      height: 2.25em;
    }

    &.mbsc-stepper-inner {
      font-size: 2.142857em;
      font-weight: 750;
    }

    &.mbsc-stepper-button.mbsc-button {
      line-height: 1em;
      box-shadow: none;
      border: 2px solid currentColor;
    }

    &.mbsc-stepper-minus.mbsc-ltr,
    &.mbsc-stepper-plus.mbsc-rtl {
      border-right: 0;
    }

    &.mbsc-stepper-minus.mbsc-rtl,
    &.mbsc-stepper-plus.mbsc-ltr {
      border-left: 0;
    }

    &.mbsc-stepper-button.mbsc-disabled {
      opacity: 1;
    }

    &.mbsc-stepper-input {
      z-index: 1;
      border-top: 2px solid;
      border-bottom: 2px solid;
      border-left: 0;
      border-right: 0;
      width: 4em;
    }

    // button colors

    &.mbsc-primary .mbsc-stepper-button {
      background: $mbsc-material-primary;
      border-color: $mbsc-material-primary;
    }

    &.mbsc-secondary .mbsc-stepper-button {
      background: $mbsc-material-secondary;
      border-color: $mbsc-material-secondary;
    }

    &.mbsc-success .mbsc-stepper-button {
      background: $mbsc-material-success;
      border-color: $mbsc-material-success;
    }

    &.mbsc-danger .mbsc-stepper-button {
      background: $mbsc-material-danger;
      border-color: $mbsc-material-danger;
    }

    &.mbsc-warning .mbsc-stepper-button {
      background: $mbsc-material-warning;
      border-color: $mbsc-material-warning;
    }

    &.mbsc-info .mbsc-stepper-button {
      background: $mbsc-material-info;
      border-color: $mbsc-material-info;
    }

    // input border colors

    &.mbsc-primary .mbsc-stepper-input {
      border-color: $mbsc-material-primary;
    }

    &.mbsc-secondary .mbsc-stepper-input {
      border-color: $mbsc-material-secondary;
    }

    &.mbsc-success .mbsc-stepper-input {
      border-color: $mbsc-material-success;
    }

    &.mbsc-danger .mbsc-stepper-input {
      border-color: $mbsc-material-danger;
    }

    &.mbsc-warning .mbsc-stepper-input {
      border-color: $mbsc-material-warning;
    }

    &.mbsc-info .mbsc-stepper-input {
      border-color: $mbsc-material-info;
    }
  }

  @include mbsc-material-stepper('material', $mbsc-material-colors);
  @include mbsc-material-stepper('material-dark', $mbsc-material-dark-colors);
}



@if ($mbsc-windows-theme and $mbsc-forms) {
  .mbsc-windows {
    &.mbsc-stepper-button.mbsc-button {
      width: 3.625em;
      line-height: 1.5em;
      border-color: currentColor;
    }

    &.mbsc-stepper-button.mbsc-hover:before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
    }

    &.mbsc-stepper-button.mbsc-disabled {
      opacity: 1;
    }

    &.mbsc-stepper-minus.mbsc-ltr,
    &.mbsc-stepper-plus.mbsc-rtl {
      border-right: 0;
    }

    &.mbsc-stepper-minus.mbsc-rtl,
    &.mbsc-stepper-plus.mbsc-ltr {
      border-left: 0;
    }

    &.mbsc-stepper-inner {
      font-size: 1.75em;
      font-weight: 800;
    }

    &.mbsc-stepper-input {
      z-index: 1;
      border-top: 2px solid;
      border-bottom: 2px solid;
      border-left: 0;
      border-right: 0;
      width: 4em;
    }

    // button colors

    &.mbsc-primary .mbsc-stepper-button {
      background: $mbsc-windows-primary;
      border-color: $mbsc-windows-primary;
    }

    &.mbsc-secondary .mbsc-stepper-button {
      background: $mbsc-windows-secondary;
      border-color: $mbsc-windows-secondary;
    }

    &.mbsc-success .mbsc-stepper-button {
      background: $mbsc-windows-success;
      border-color: $mbsc-windows-success;
    }

    &.mbsc-danger .mbsc-stepper-button {
      background: $mbsc-windows-danger;
      border-color: $mbsc-windows-danger;
    }

    &.mbsc-warning .mbsc-stepper-button {
      background: $mbsc-windows-warning;
      border-color: $mbsc-windows-warning;
    }

    &.mbsc-info .mbsc-stepper-button {
      background: $mbsc-windows-info;
      border-color: $mbsc-windows-info;
    }

    // input border colors

    &.mbsc-primary .mbsc-stepper-input {
      border-color: $mbsc-windows-primary;
    }

    &.mbsc-secondary .mbsc-stepper-input {
      border-color: $mbsc-windows-secondary;
    }

    &.mbsc-success .mbsc-stepper-input {
      border-color: $mbsc-windows-success;
    }

    &.mbsc-danger .mbsc-stepper-input {
      border-color: $mbsc-windows-danger;
    }

    &.mbsc-warning .mbsc-stepper-input {
      border-color: $mbsc-windows-warning;
    }

    &.mbsc-info .mbsc-stepper-input {
      border-color: $mbsc-windows-info;
    }
  }

  @include mbsc-windows-stepper('windows', $mbsc-windows-colors);
  @include mbsc-windows-stepper('windows-dark', $mbsc-windows-dark-colors);
}


@if $mbsc-forms {
  .mbsc-stepper {
    line-height: 1.25em;
    position: relative;
    display: block;
    margin: 0;
    z-index: 0;
    user-select: none;
    box-sizing: border-box;
    padding: 1.5em 11.75em 1.5em 1em;
  }

  .mbsc-stepper.mbsc-rtl {
    padding: 1.5em 1em 1.5em 11.75em;
  }

  .mbsc-stepper-label {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .mbsc-stepper-input {
    width: 3.571429em;
    padding: 0;
    margin: 0;
    box-shadow: none;
    border-radius: 0;
    font-size: 0.875em;
    text-align: center;
    opacity: 1;
    z-index: 3;
    background: transparent;
    appearance: textfield;
  }

  .mbsc-stepper-input::-webkit-outer-spin-button,
  .mbsc-stepper-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .mbsc-stepper-input:focus {
    outline-width: 0;
  }

  .mbsc-stepper-input.mbsc-disabled {
    cursor: not-allowed;
  }

  .mbsc-stepper-control {
    position: absolute;
    z-index: 2;
    margin-top: -1em;
    top: 50%;
    height: 2em;
  }

  .mbsc-stepper-control.mbsc-ltr {
    right: 1em;
  }

  .mbsc-stepper-control.mbsc-rtl {
    left: 1em;
  }

  .mbsc-stepper-start .mbsc-stepper-input {
    border: none;
    order: -1;
  }

  .mbsc-stepper-start.mbsc-ltr .mbsc-stepper-minus.mbsc-button {
    margin-right: -1px;
  }

  .mbsc-stepper-start.mbsc-rtl .mbsc-stepper-minus.mbsc-button {
    margin-left: -1px;
  }

  .mbsc-stepper-end .mbsc-stepper-input {
    border: none;
    order: 2;
  }

  .mbsc-stepper-end.mbsc-ltr .mbsc-stepper-minus.mbsc-button {
    margin-right: -1px;
  }

  .mbsc-stepper-end.mbsc-rtl .mbsc-stepper-minus.mbsc-button {
    margin-left: -1px;
  }

  .mbsc-stepper-button.mbsc-button.mbsc-font {
    width: 3.25em;
    margin: 0;
    display: inline-block;
  }

  .mbsc-stepper-inner {
    font-size: 2em;
    font-weight: bold;
  }

  .mbsc-stepper-minus.mbsc-button.mbsc-ltr {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .mbsc-stepper-plus.mbsc-button.mbsc-ltr {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .mbsc-stepper-minus.mbsc-button.mbsc-rtl {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .mbsc-stepper-plus.mbsc-button.mbsc-rtl {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
}



@if ($mbsc-ios-theme and $mbsc-forms) {
  .mbsc-ios {
    &.mbsc-switch-right {
      padding: 0.875em 5em 0.875em 1em;
    }

    &.mbsc-switch-left {
      padding: 0.875em 1em 0.875em 5em;
    }

    // Track

    &.mbsc-switch-track {
      width: 1.25em;
      height: 2em;
      padding: 0;
      margin: -0.96875em 0.875em 0 0.875em;
      border-radius: 1.25em;
    }

    &.mbsc-switch-track-right {
      right: 1em;
    }

    &.mbsc-switch-track-left {
      left: 1em;
    }

    &.mbsc-switch-track:after {
      content: '';
      position: absolute;
      z-index: 1;
      top: 0;
      right: -1em;
      bottom: 0;
      left: -1em;
      border-radius: 1.25em;
      transition: background-color 0.2s ease-out;
    }

    &.mbsc-switch-track.mbsc-disabled {
      opacity: 0.3;
    }

    // Handle

    &.mbsc-switch-handle {
      z-index: 2;
      top: 50%;
      left: 50%;
      width: 1.75em;
      height: 1.75em;
      margin: -0.875em 0 0 -0.875em;
      border-radius: 1.75em;
      box-shadow: 0 0.1875em 0.75em rgba(0, 0, 0, 0.16), 0 0.1875em 0.0625em rgba(0, 0, 0, 0.1);
      background: #fff;
    }

    // Color presets

    &.mbsc-switch-track.mbsc-switch-primary.mbsc-checked:after {
      background: $mbsc-ios-primary;
    }

    &.mbsc-switch-track.mbsc-switch-secondary.mbsc-checked:after {
      background: $mbsc-ios-secondary;
    }

    &.mbsc-switch-track.mbsc-switch-success.mbsc-checked:after {
      background: $mbsc-ios-success;
    }

    &.mbsc-switch-track.mbsc-switch-danger.mbsc-checked:after {
      background: $mbsc-ios-danger;
    }

    &.mbsc-switch-track.mbsc-switch-warning.mbsc-checked:after {
      background: $mbsc-ios-warning;
    }

    &.mbsc-switch-track.mbsc-switch-info.mbsc-checked:after {
      background: $mbsc-ios-info;
    }
  }

  @include mbsc-ios-switch('ios', $mbsc-ios-colors);
  @include mbsc-ios-switch('ios-dark', $mbsc-ios-dark-colors);
}



@if ($mbsc-material-theme and $mbsc-forms) {
  .mbsc-material {
    &.mbsc-switch-right {
      padding: 0.9375em 4em 0.9375em 1em;
    }

    &.mbsc-switch-left {
      padding: 0.9375em 1em 0.9375em 4em;
    }

    // Track

    &.mbsc-switch-track {
      width: 1.75em;
      height: 0.875em;
      padding: 0 0.25em;
      margin-top: -0.4375em;
      border-radius: 1.25em;
    }

    &.mbsc-switch-track-left {
      left: 1.25em;
    }

    &.mbsc-switch-track-right {
      right: 1.25em;
    }

    // Handle

    &.mbsc-switch-handle {
      width: 1.25em;
      height: 1.25em;
      border-radius: 1.25em;
      top: 50%;
      margin-left: -0.625em;
      margin-top: -0.625em;
    }

    &.mbsc-switch-handle:before {
      content: '';
      display: block;
      position: absolute;
      z-index: -1;
      opacity: 0;
      transition: opacity 0.2s ease-in-out;
      border-radius: 2.875em;
      top: -0.625em;
      left: -0.625em;
      width: 2.5em;
      height: 2.5em;
    }

    &.mbsc-switch-handle.mbsc-focus:before {
      opacity: 0.5;
    }

    &.mbsc-switch-handle.mbsc-active:before {
      opacity: 1;
    }

    /* Color presets */

    &.mbsc-switch-handle.mbsc-switch-primary.mbsc-checked {
      background: $mbsc-material-primary;
    }

    &.mbsc-switch-track.mbsc-switch-primary.mbsc-checked {
      background: lighten($mbsc-material-primary, 20%);
    }

    &.mbsc-switch-handle.mbsc-switch-secondary.mbsc-checked {
      background: $mbsc-material-secondary;
    }

    &.mbsc-switch-track.mbsc-switch-secondary.mbsc-checked {
      background: lighten($mbsc-material-secondary, 20%);
    }

    &.mbsc-switch-handle.mbsc-switch-success.mbsc-checked {
      background: $mbsc-material-success;
    }

    &.mbsc-switch-track.mbsc-switch-success.mbsc-checked {
      background: lighten($mbsc-material-success, 20%);
    }

    &.mbsc-switch-handle.mbsc-switch-danger.mbsc-checked {
      background: $mbsc-material-danger;
    }

    &.mbsc-switch-track.mbsc-switch-danger.mbsc-checked {
      background: lighten($mbsc-material-danger, 20%);
    }

    &.mbsc-switch-handle.mbsc-switch-warning.mbsc-checked {
      background: $mbsc-material-warning;
    }

    &.mbsc-switch-track.mbsc-switch-warning.mbsc-checked {
      background: lighten($mbsc-material-warning, 20%);
    }

    &.mbsc-switch-handle.mbsc-switch-info.mbsc-checked {
      background: $mbsc-material-info;
    }

    &.mbsc-switch-track.mbsc-switch-info.mbsc-checked {
      background: lighten($mbsc-material-info, 20%);
    }
  }

  @include mbsc-material-switch('material', $mbsc-material-colors);
  @include mbsc-material-switch('material-dark', $mbsc-material-dark-colors);
}



@if ($mbsc-windows-theme and $mbsc-forms) {
  .mbsc-windows {
    &.mbsc-switch-right {
      padding: 1.125em 5.25em 1.125em 1em;
    }

    &.mbsc-switch-left {
      padding: 1.125em 1em 1.125em 5.25em;
    }

    // Track

    &.mbsc-switch-track {
      width: 1.5em;
      height: 1em;
      margin-top: -0.5em;
    }

    &.mbsc-switch-track-right {
      right: 1.75em; // .125 padding + .625 visible track relative position
    }

    &.mbsc-switch-track-left {
      left: 1.75em; // .125 padding + .625 visible track relative position
    }

    &.mbsc-switch-track:before,
    &.mbsc-switch-track:after {
      content: '';
      position: absolute;
      z-index: 1;
      border: 0.125em solid transparent;
    }

    &.mbsc-switch-track:before {
      top: -0.125em; // .125em border
      bottom: -0.125em; // .125em border
      left: -0.625em; // .125em border + .5em space
      right: -0.625em; // .125em border + .5em space
      border-radius: 0.625em;
      transition: background-color 0.2s ease-in-out, border 0.2s ease-in-out;
    }

    // used for the focus outline
    &.mbsc-switch-track:after {
      top: -0.25em;
      bottom: -0.25em;
      left: -0.75em;
      right: -0.75em;
      border-radius: 0.75em;
    }

    &.mbsc-switch-track.mbsc-disabled {
      opacity: 0.2;
    }

    // Handle

    &.mbsc-switch-handle {
      z-index: 2;
      top: 50%;
      left: 50%;
      right: auto;
      height: 0.625em;
      width: 0.625em;
      border-radius: 10px;
      margin: -0.3125em 0 0 -0.3125em;
    }

    // Color presets

    &.mbsc-switch-track.mbsc-switch-primary.mbsc-checked:before {
      border-color: $mbsc-windows-primary;
      background: $mbsc-windows-primary;
    }

    &.mbsc-switch-track.mbsc-switch-secondary.mbsc-checked:before {
      border-color: $mbsc-windows-secondary;
      background: $mbsc-windows-secondary;
    }

    &.mbsc-switch-track.mbsc-switch-success.mbsc-checked:before {
      border-color: $mbsc-windows-success;
      background: $mbsc-windows-success;
    }

    &.mbsc-switch-track.mbsc-switch-danger.mbsc-checked:before {
      border-color: $mbsc-windows-danger;
      background: $mbsc-windows-danger;
    }

    &.mbsc-switch-track.mbsc-switch-warning.mbsc-checked:before {
      border-color: $mbsc-windows-warning;
      background: $mbsc-windows-warning;
    }

    &.mbsc-switch-track.mbsc-switch-info.mbsc-checked:before {
      border-color: $mbsc-windows-info;
      background: $mbsc-windows-info;
    }
  }

  @include mbsc-windows-switch('windows', $mbsc-windows-colors);
  @include mbsc-windows-switch('windows-dark', $mbsc-windows-dark-colors);
}


@if $mbsc-forms {
  .mbsc-switch {
    position: relative;
    display: block;
    margin: 0;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    line-height: 1.25em;
  }

  .mbsc-switch-track {
    position: absolute;
    top: 50%;
    z-index: 4;
    display: block;
    box-sizing: border-box;
  }

  .mbsc-switch-handle {
    display: block;
    position: absolute;
    cursor: pointer;

    &.mbsc-disabled {
      cursor: not-allowed;
    }
  }

  .mbsc-switch-handle-animate {
    transition: left 0.1s ease-in-out;
  }

  .mbsc-description {
    display: block;
    font-size: 0.75em;
    opacity: 0.6;
  }
}
