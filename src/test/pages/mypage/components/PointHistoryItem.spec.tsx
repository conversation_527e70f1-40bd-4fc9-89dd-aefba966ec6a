// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen } from '@testing-library/react';
import { POINT_HISTORIES } from '@/mocks/point-histories';
import PointHistoryItem from '@/pages/mypage/point-history/components/PointHistoryItem';

describe('pages/mypage/components/PointHistoryItem.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      const pointHistory = POINT_HISTORIES.point_histories[0];
      render(
        <PointHistoryItem
          companyLogo={pointHistory.company_image}
          companyName={pointHistory.company_name}
          gotAt={pointHistory.event_datetime}
          pointAmount={pointHistory.amount}
          reason={pointHistory.event_name}
          event_type={pointHistory.event_type}
        />
      );
      const list = screen.getByRole('listitem');

      expect(list).toBeInTheDocument();
    });
  });
});
