// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
/** @type {import('next').NextConfig} */
const nextConfig = {
  pageExtensions: ['page.tsx', 'page.ts'],
  reactStrictMode: false,
  swcMinify: true,
  env: {
    APP_ENV: process.env.APP_ENV,
    LIFF_ID: process.env.LIFF_ID,
    LIFF_BASE_URL: process.env.LIFF_BASE_URL,
    LINE_OA_URL: process.env.LINE_OA_URL,
    LINE_LOGIN_URL: process.env.LINE_LOGIN_URL,
    CLIENT_BASE_URL: process.env.CLIENT_BASE_URL,
    INTERNAL_BASE_URL: process.env.INTERNAL_BASE_URL,
    INTERNAL_BASE_API: process.env.INTERNAL_BASE_API,
    INTERNAL_WEB_BASE_URL: process.env.INTERNAL_WEB_BASE_URL,
    LINE_BASE_URL: process.env.LINE_BASE_URL,
    HASH_SECRET_KEY: process.env.HASH_SECRET_KEY,
    GTM_KEY: process.env.GTM_KEY,
    APPINSIGHTS_CONNECTION_STRING: process.env.APPINSIGHTS_CONNECTION_STRING,
  },
  webpack(config) {
    config.module.rules.push({
      test: /\.svg$/i,
      issuer: /\.[jt]sx?$/,
      use: ['@svgr/webpack'],
    });

    return config;
  },
  images: {
    unoptimized: true,
  },
  output: 'standalone',
  sassOptions: {
    includePaths: [path.join(__dirname, 'styles')],
  },
};

module.exports = nextConfig;
