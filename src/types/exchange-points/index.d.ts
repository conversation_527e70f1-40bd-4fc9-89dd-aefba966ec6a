// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
export type TGiftType = 'box' | 'fixed' | 'choosable' | 'coupon';

// export interface IExchangeableGiftDetailResponse {
//   gift_card_config_code: string;
//   gift_company_name: string;
//   gift_id_in_pc: string;
//   gift_name: string;
//   gift_description?: string;
//   gift_image?: string;
//   is_deleted: string;
//   point_amount: number;
//   type: string;
//   gift_type: TGiftType;
//   pause_flag: string;
//   pk: string;
// }

export interface IExchangeableGiftInListResponse {
  gift_id_in_pc: string;
  gift_name: string;
  gift_company_name: string;
  gift_image: string;
}

export interface IExchangeableGiftDetailResponse {
  exchange_points: boolean;
  gift_about: string;
  gift_company_name: string;
  gift_how_to_use: string;
  gift_id_in_pc: string;
  gift_image: string;
  gift_name: string;
  gift_notes: string;
  gift_notes_confirmation: string;
  gift_type: TGiftType;
  point_amount: number;
}

export interface IExchangedGiftListParams {
  is_available: '0' | '1';
}

export interface IExchangeableGiftListResponse {
  gift_required_amount: number;
  gift_list: IExchangeableGiftInListResponse[];
}

export interface IConfirmExchangeGiftPayload {
  transaction_token: string;
}

export interface IConfirmExchangeGiftResponse {
  available_period: string;
  choosable_begin_at?: string;
  choosable_end_at?: string;
  gift_type: string;
  gift_url: string;
  code?: string;
  expired_at?: string;
}

export interface IExchangedGiftDetailResponse {
  exchange_id: string;
  exchanged_at: string;
  gift: IExchangedGiftInfoResponse;
  gift_type: TGiftType;
  point_amount: number;
}

export interface IExchangedGiftDetail extends IExchangedGiftDetailResponse {
  uid: string;
  loading: boolean;
}

export interface IExchangedGiftInfoResponse {
  available_period?: string;
  expired_at?: string;
  choosable_begin_at?: string;
  choosable_end_at?: string;
  gift_company_name: string;
  gift_id_in_pc: string;
  gift_name: string;
  gift_description?: string;
  gift_image?: string;
  gift_url: string;
  code?: string;
}

export interface IExchangedGiftTransactionResponse {
  issue_identity: string;
  transaction_id: string;
}

export interface IToggleGiftUsagePayload {
  is_used: '0' | '1';
}

export interface IGetCouponDetailParams {
  id: string;
  code: string;
}

export interface IGetCouponDetailResponse {
  gift_company_name: string;
  gift_name: string;
  gift_about: string;
  code: string;
  expired_at: string;
  service_url: string;
  gift_image?: string;
  coupon_about?: string;
  button_text?: string;
}
export interface IToggleGiftUsageResponse {
  status: string;
}
