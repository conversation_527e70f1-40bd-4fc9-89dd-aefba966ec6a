// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { act, render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import NoWifiModal from '@/components/Modal/NoWifiModal';
import { Button } from '@/components/Button';
import { useState } from 'react';
import { IModalProps } from '@/components/Modal';

const TestModal = ({ root }: Pick<IModalProps, 'root'>): JSX.Element => {
  const [open, setOpen] = useState(false);

  return (
    <>
      <Button onClick={() => setOpen(true)}>Toggle modal</Button>
      <NoWifiModal root={root} isShowModal={open} />
    </>
  );
};

describe('components/Modal/NoWifiModal.spec.tsx', () => {
  describe('A11y', () => {
    it('should have `role="dialog"`', async () => {
      const user = userEvent.setup();
      const root = document.createElement('div');

      render(<TestModal root={root} />);

      const openButton = screen.getByRole('button');

      await act(() => user.click(openButton));

      const modal = within(root).getByRole('dialog');

      expect(modal).toHaveAttribute('aria-hidden', 'false');
    });

    it('should be removed from DOM and garbage collected', async () => {
      const root = document.createElement('div');

      const { unmount } = render(<TestModal root={root} />);

      unmount();

      await waitFor(() => expect(root.childNodes.length).toBe(0));
    });
  });
});
