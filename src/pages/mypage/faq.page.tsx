// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { FAQ_MENU } from '@/constants/mypage';
import MypageLayout from './components/MypageLayout';
import CollapseComponent from './components/CollapseComponent';

const FAQScreen: FC = () => {
  return (
    <>
      <MypageLayout spacingTop="none">
        <div className="main">
          {FAQ_MENU.map((item, index) => (
            <CollapseComponent
              key={index}
              categogy={item.categogy}
              content={item.content}
            />
          ))}
        </div>
      </MypageLayout>
    </>
  );
};

export default FAQScreen;
