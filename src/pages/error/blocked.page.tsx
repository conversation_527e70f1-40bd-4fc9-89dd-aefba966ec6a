// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { Logo } from '@/components/Logo';
import { Button } from '@/components/Button';
import { BoxContent } from '@/components/BoxContent';
import { useError } from '@/hooks/use-error';

const BlockedErrorScreen: FC = () => {
  const { handler } = useError();

  return (
    <>
      <div className="container mx-auto">
        <BoxContent>
          <BoxContent.Body>
            <div className="pt-[96px]">
              <Logo box className="mb-10" />
              <div className="text-center">
                <h3 className="text-base font-bold mb-2">
                  本サービスをご利用いただく為には
                  <br />
                  ブロックを解除する必要があります
                </h3>
              </div>
            </div>
          </BoxContent.Body>
          <BoxContent.Footer>
            <Button
              fullSized
              type="submit"
              size="xl"
              color="primary"
              onClick={handler}
            >
              認証画面に戻る
            </Button>
          </BoxContent.Footer>
        </BoxContent>
      </div>
    </>
  );
};

export default BlockedErrorScreen;
