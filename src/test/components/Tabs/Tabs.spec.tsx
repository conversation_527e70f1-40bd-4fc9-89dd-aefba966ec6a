// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { act, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Tabs, TabsProps } from '@/components/Tabs';

const TabsTest: FC<Omit<TabsProps, 'children'>> = props => {
  return (
    <Tabs defaultValue="tab-1" {...props}>
      <Tabs.List aria-label="test-tabs">
        <Tabs.Tab value="tab-1">tab-1</Tabs.Tab>
        <Tabs.Tab value="tab-2">tab-2</Tabs.Tab>
        <Tabs.Tab value="tab-3">tab-3</Tabs.Tab>
      </Tabs.List>

      <Tabs.Panel value="tab-1">tab-1 panel</Tabs.Panel>
      <Tabs.Panel value="tab-2">tab-2 panel</Tabs.Panel>
      <Tabs.Panel value="tab-3">tab-3 panel</Tabs.Panel>
    </Tabs>
  );
};

const TAB_VALUES = ['tab-1', 'tab-2', 'tab-3'] as const;
type TabValue = typeof TAB_VALUES[number];

const expectActiveTab = (value: TabValue | null) => {
  const hidden = ['tab-1', 'tab-2', 'tab-3'].filter(panel => panel !== value);
  hidden.forEach(panel => {
    expect(screen.getByText(`${panel} panel`)).not.toBeVisible();
  });

  if (value) {
    expect(screen.getByText(`${value} panel`)).toBeVisible();
  }
};

const getTab = (value: TabValue) => {
  const index = TAB_VALUES.indexOf(value);
  const tabs = screen.getAllByRole('tab');
  return tabs[index];
};

const clickTab = async (value: TabValue) => {
  const user = userEvent.setup();
  await act(() => user.click(getTab(value)));
};

describe('components/Tabs/Tabs.spec.tsx', () => {
  describe('A11y', () => {
    it('should component render ui', () => {
      render(<TabsTest data-testid="tabs" />);
      const tabs = screen.getByTestId('tabs');

      expect(tabs).toBeInTheDocument();
    });

    it('should not display any tab if value in null', async () => {
      render(<TabsTest value={null} />);
      expectActiveTab(null);
    });
  });

  describe('User interactions', () => {
    it('should supports controlled state', async () => {
      const spy = jest.fn();
      render(<TabsTest value="tab-1" onTabChange={spy} />);
      expectActiveTab('tab-1');

      await clickTab('tab-2');
      expectActiveTab('tab-1');

      expect(spy).toHaveBeenCalledTimes(1);
      expect(spy).toHaveBeenCalledWith('tab-2');
    });

    it('should supports uncontrolled state', async () => {
      const spy = jest.fn();
      render(<TabsTest defaultValue="tab-1" onTabChange={spy} />);
      expectActiveTab('tab-1');

      await clickTab('tab-2');
      expectActiveTab('tab-2');

      expect(spy).toHaveBeenCalledWith('tab-2');
    });

    it('should handles tab key correctly (without selected tab)', async () => {
      const user = userEvent.setup();
      render(<TabsTest />);
      expect(document.body).toHaveFocus();

      await user.tab();
      expect(getTab('tab-1')).toHaveFocus();

      await user.tab();
      expect(getTab('tab-2')).toHaveFocus();

      await user.tab();
      expect(getTab('tab-3')).toHaveFocus();

      await user.tab();
      expect(document.body).toHaveFocus();
    });
  });
});
