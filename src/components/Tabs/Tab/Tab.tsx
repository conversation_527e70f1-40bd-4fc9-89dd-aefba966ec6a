// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { twMerge } from 'tailwind-merge';
import React, { forwardRef } from 'react';
import { IThemeBoolean } from '@/types/theme';
import { styles } from './Tab.styles';
import { TabsVariant } from '../Tabs';
import { useTabsContext } from '../Tabs.context';

export interface IThemeTabsTab {
  base: string;
  styles: Record<TabsVariant, TabStyleItemProps>;
  tabIcon: string;
  tabLabel: string;
  tabRightSection: string;
}

type TabStyleItemProps = {
  base: string;
  active: IThemeBoolean;
};

export interface TabProps extends React.ComponentPropsWithoutRef<'button'> {
  /** Value that is used to connect Tab with associated panel */
  value: string;

  /** Section of content displayed after label */
  rightSection?: React.ReactNode;

  /** Section of content displayed before label */
  icon?: React.ReactNode;

  /** Tab label */
  children?: React.ReactNode;

  /** Tab class */
  className?: string;
}

export const Tab = forwardRef<HTMLButtonElement, TabProps>((props, ref) => {
  const { value, children, onClick, className, icon, rightSection, ...others } =
    props;
  const ctx = useTabsContext();
  const isActive = value === ctx.value;

  const activateTab = (
    event: React.MouseEvent<HTMLButtonElement, MouseEvent>
  ) => {
    ctx.onTabChange(value);
    onClick?.(event);
  };

  return (
    <button
      {...others}
      ref={ref}
      role="tab"
      type="button"
      id={ctx.getTabId(value)}
      className={twMerge(
        styles.base,
        styles.styles[ctx.variant].base,
        styles.styles[ctx.variant].active[isActive ? 'on' : 'off'],
        className
      )}
      onClick={activateTab}
    >
      {icon && <span className={twMerge(icon && styles.tabIcon)}>{icon}</span>}
      {children && <span className={twMerge(styles.tabLabel)}>{children}</span>}
      {rightSection && (
        <span className={twMerge(rightSection && styles.tabRightSection)}>
          {rightSection}
        </span>
      )}
    </button>
  );
});

Tab.displayName = 'Tab';
