// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { ICompany, IGroupedCompanies } from '@/types/message-delivery-settings';
import service from '..';

const baseUrl = '/members';

const messageDeliverySettingsApi = {
  getListCompany: () =>
    service.get<ICompany[]>(
      `${baseUrl}/get-list-company-message-delivery-setting`
    ),
  getMessageDeliverySettingV2: () =>
    service.get<IGroupedCompanies>(`${baseUrl}/message-delivery-setting-v2`),
  changeMessageDeliverySetting: (payload: { companies: string[] }) =>
    service.post(`${baseUrl}/change-message-delivery-setting`, {
      companyIds: payload.companies,
    }),
};

export default messageDeliverySettingsApi;
