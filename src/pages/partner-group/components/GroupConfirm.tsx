// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React from 'react';

interface GroupInfoProps {
  className: string;
  companyName?: string;
  partnerGroup: string[];
}
const GroupInfo: React.FC<GroupInfoProps> = ({
  companyName,
  className,
  partnerGroup,
}) => {
  return (
    <div className={`flex flex-wrap ${className}`}>
      {partnerGroup.includes('G0001') ? (
        <h3 className="font-normal">
          {companyName}はヘルスケア・育児・働き方グループの企業です。
          <br />
          <br />
          あなたは、ピアコネの「ペット」グループに登録しています。
          <br />
          <br />
          「ヘルスケア・育児・働き方」グループにも登録しますか。
          <br />
          <br />
          登録することで、「ヘルスケア・育児・働き方」のサービスメニューが表示されるようになります。
        </h3>
      ) : (
        <h3 className="font-normal">
          {companyName}はペットグループの企業です。
          <br />
          <br />
          あなたは、ピアコネの「ヘルスケア・育児・働き方」グループに登録しています。
          <br />
          <br />
          「ペット」グループにも登録しますか。
          登録することで、「ペット」のサービスメニューが表示されるようになります。
        </h3>
      )}
    </div>
  );
};

export default GroupInfo;
