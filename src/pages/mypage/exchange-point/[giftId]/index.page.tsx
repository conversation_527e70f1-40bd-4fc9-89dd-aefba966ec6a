// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { isArray } from 'lodash';
import { AxiosError } from 'axios';
import { useRouter } from 'next/router';
import React, { FC, useEffect, useState } from 'react';
import { useNetwork } from '@/hooks/use-network';
import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import { useLiff } from '@/contexts/liff';
import { routers } from '@/constants/routes';
import {
  IConfirmExchangeGiftResponse,
  IExchangeableGiftDetailResponse,
} from '@/types/exchange-points';
import pointApi from '@/services/internal/modules/point';
import transactionsApi from '@/services/internal/modules/transactions';
import exchangePointsApi from '@/services/internal/modules/exchange-points';
import { Spinner } from '@/components/Spinner';
import MypageLayout from '../../components/MypageLayout';
import ErrorExchangeGiftModal from './components/ErrorExchangeGiftModal';
import ConfirmExchangeGiftModal from './components/ConfirmExchangeGiftModal';
import SuccessExchangeGiftModal from './components/SuccessExchangeGiftModal';
import { ExchangeableGiftDetail } from './components/ExchangeableGiftDetail';
import { ExchangeableGiftDetailError } from './components/ExchangeableGiftDetailError';
import { ExchangeableGiftDetailLoading } from './components/ExchangeableGiftDetailLoading';
import { GIFT_EXCHANGE_TRANSACTION_STATUS } from '@/constants/exchange-points';

type PostDetailDataProps = {
  giftId: string;
};

type PostDetailProps = FC<
  InferGetServerSidePropsType<typeof getServerSideProps>
>;

const GiftDetailScreen: PostDetailProps = ({ giftId }) => {
  const router = useRouter();
  const { online } = useNetwork();
  const { userInfo } = useLiff();

  // State save fetching gift detail
  const [isFetching, setIsFetching] = useState(false);
  const [isErrorFetching, setIsErrorFetching] = useState(false);
  // State save gift detail
  const [exchangeableGiftDetail, setExchangeableGiftDetail] =
    useState<IExchangeableGiftDetailResponse>();
  const [isEnoughPointExchange, setIsEnoughPointExchange] = useState(false);
  // State save status exchange gift
  const [isShowConfirmExchangeGift, setIsShowConfirmExchangeGift] =
    useState(false);
  const [isLoadingSubmit, setIsLoadingSubmit] = useState(false);
  const [isExchangeGiftError, setIsExchangeGiftError] = useState(false);
  const [isExchangeGiftSuccess, setIsExchangeGiftSuccess] = useState(false);
  const [exchangeGiftSuccess, setExchangeGiftSuccess] =
    useState<IConfirmExchangeGiftResponse>();
  const [transactionToken, setTransactionToken] = useState<string>('');
  const [isFetchingGetTransactionStatus, setIsFetchingGetTransactionStatus] =
    useState(false);
  // Flag when call api lose internet
  const [isCallApiLoseInternet, setIsCallApiLoseInternet] = useState(false);

  const handleError = (error: AxiosError) => {
    error?.code === 'ERR_NETWORK'
      ? setIsCallApiLoseInternet(true)
      : setIsExchangeGiftError(true);
  };

  const handleResetState = () => {
    setTransactionToken('');
    setIsCallApiLoseInternet(false);
  };

  const submitHandler = async () => {
    try {
      if (!isEnoughPointExchange || !exchangeableGiftDetail)
        throw new Error('You have not eligible to exchange this gift');
      setIsLoadingSubmit(true);

      const { transactionToken: token } =
        await transactionsApi.generateTransactionTokenExchangeGift({
          giftId,
        });
      // Keep transactionToken to call transactionsApi.getTransactionStatus
      setTransactionToken(token);

      const response = await exchangePointsApi.confirmExchangeGift({
        transaction_token: token,
      });

      setExchangeGiftSuccess(response);
      setIsExchangeGiftSuccess(true);

      // Reset state
      handleResetState();
    } catch (error: unknown) {
      handleError(error as AxiosError);
    } finally {
      setIsLoadingSubmit(false);
      setIsShowConfirmExchangeGift(false);
    }
  };

  const getExchangeableGiftDetailHandler = async () => {
    try {
      setIsFetching(true);
      const [giftDetail, currentPoint] = await Promise.all([
        exchangePointsApi.getExchangeableGiftDetail(giftId),
        pointApi.getCurrentPoint(),
      ]);
      setExchangeableGiftDetail(giftDetail);
      setIsEnoughPointExchange(currentPoint >= giftDetail.point_amount);
    } catch {
      setIsErrorFetching(true);
    } finally {
      setIsFetching(false);
    }
  };

  const getTransactionStatus = async () => {
    try {
      if (!transactionToken)
        throw new Error('You have not confirmed to exchange this gift');

      setIsFetchingGetTransactionStatus(true);
      const res = await transactionsApi.getTransactionStatus({
        token: transactionToken,
        pcId: userInfo.peer_conne_id,
      });

      switch (res.status) {
        case GIFT_EXCHANGE_TRANSACTION_STATUS.READY:
          setIsShowConfirmExchangeGift(true);
          break;
        case GIFT_EXCHANGE_TRANSACTION_STATUS.COMPLETED:
          setIsExchangeGiftSuccess(true);
          setExchangeGiftSuccess(res?.transaction);
          break;
        default:
          setIsExchangeGiftError(true);
          break;
      }

      // Reset state
      handleResetState();
    } catch (error: unknown) {
      handleError(error as AxiosError);
    } finally {
      setIsFetchingGetTransactionStatus(false);
    }
  };

  const redirectTo = (url: string) => {
    router.push(url);
  };

  useEffect(() => {
    getExchangeableGiftDetailHandler();
  }, []);

  useEffect(() => {
    /**
     * When you exchange points, you need to call 2 api
     * 1. API transactionsApi.generateTransactionTokenExchangeGift -> get token to exchange
     * 2. API exchangePointsApi.confirmExchangeGift -> confirm exchange
     * If you called API 1 and sent payload to API 2, but because lose internet you can not receive response API 2 from Server
     * So, We need to confirm the exchange is successful or not? When reconnect internet we check it base on transactionToken received from API 1
     */
    if (online && isCallApiLoseInternet) {
      setIsExchangeGiftError(false);
      setIsExchangeGiftSuccess(false);
      getTransactionStatus();
    }
  }, [online, isCallApiLoseInternet]);

  return (
    <>
      <MypageLayout spacingTop="lg">
        {isFetching ? (
          <ExchangeableGiftDetailLoading />
        ) : isErrorFetching ? (
          <ExchangeableGiftDetailError
            retry={getExchangeableGiftDetailHandler}
          />
        ) : exchangeableGiftDetail ? (
          <>
            <ExchangeableGiftDetail
              gift={exchangeableGiftDetail}
              isEnoughPointExchange={isEnoughPointExchange}
              submit={() => setIsShowConfirmExchangeGift(true)}
            />

            <ConfirmExchangeGiftModal
              submit={submitHandler}
              gift={exchangeableGiftDetail}
              loading={isLoadingSubmit}
              isShowModal={isShowConfirmExchangeGift}
              setIsShowModal={setIsShowConfirmExchangeGift}
            />

            <SuccessExchangeGiftModal
              submit={redirectTo}
              gift={exchangeableGiftDetail}
              redirectUrl={
                exchangeGiftSuccess?.gift_type === 'coupon'
                  ? `${routers.couponDetail}?couponId=${giftId}&code=${exchangeGiftSuccess?.code}`
                  : exchangeGiftSuccess?.gift_url || routers.exchangePoint
              }
              isShowModal={isExchangeGiftSuccess}
            />

            <ErrorExchangeGiftModal
              submit={redirectTo}
              isShowModal={isExchangeGiftError}
            />
          </>
        ) : (
          <ExchangeableGiftDetailError
            retry={getExchangeableGiftDetailHandler}
          />
        )}

        {isFetchingGetTransactionStatus && (
          <div className="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50">
            <Spinner size="3xl" />
          </div>
        )}
      </MypageLayout>
    </>
  );
};

export const getServerSideProps: GetServerSideProps<
  PostDetailDataProps
> = async context => {
  const { giftId } = context.query;
  const exchangeableGift = isArray(giftId) ? giftId[0] : giftId;

  if (!exchangeableGift) {
    return {
      redirect: { permanent: false, destination: routers.exchangePoint },
    };
  }

  return {
    props: {
      giftId: exchangeableGift,
    },
  };
};

export default GiftDetailScreen;
