// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { IGroupedCompanies } from '@/types/message-delivery-settings';
import messageDeliverySettingsApi from '@/services/internal/modules/message-delivery-settings';
import MessageDeliverySettingsForm from '@/pages/mypage/account-settings/components/MessageDeliverySettingsForm';

describe('pages/mypage/account-settings/components/ChangeProfileInformationForm.spec.tsx', () => {
  const props: IGroupedCompanies = {
    G0001: [
      {
        company_id: 'C0002',
        company_name: 'カロミル',
        groups: ['G0001'],
        status: 'on',
      },
      {
        company_id: 'C0074',
        company_name: 'ThaoNTT0811',
        groups: ['G0001'],
        status: 'off',
      },
    ],
    G0002: [
      {
        company_id: 'C0002',
        company_name: 'カロミル',
        groups: ['G0001'],
        status: 'on',
      },
      {
        company_id: 'C0074',
        company_name: 'ThaoNTT0811',
        groups: ['G0001'],
        status: 'off',
      },
    ],
  };

  describe('A11y', () => {
    it('should render in document', async () => {
      render(
        <MessageDeliverySettingsForm
          companies={props}
          myPartnerGroup={[]}
          reCallApi={function (): void {
            throw new Error('Function not implemented.');
          }}
          companyOn={[]}
        />
      );

      const checkbox = screen.getByRole('checkbox', {
        name: 'test company name',
      });

      await waitFor(() => {
        expect(checkbox).toBeInTheDocument();
      });
    });
  });

  describe('User interaction', () => {
    const response = jest.spyOn(
      messageDeliverySettingsApi,
      'changeMessageDeliverySetting'
    );

    it('should render in document', async () => {
      response.mockResolvedValue({ success: false });
      const { container } = render(
        <MessageDeliverySettingsForm
          companies={props}
          myPartnerGroup={[]}
          reCallApi={function (): void {
            throw new Error('Function not implemented.');
          }}
          companyOn={[]}
        />
      );

      const checkbox = screen.getByRole('checkbox', {
        name: 'test company name 2',
      }) as HTMLInputElement;

      const button = screen.getByRole('button', {
        name: '変更する',
      });

      fireEvent.click(checkbox);

      fireEvent.submit(button);

      // eslint-disable-next-line testing-library/no-container, testing-library/no-node-access
      const svg = container.querySelector(
        '[data-file-name="SvgCheckboxChecked"]'
      );

      await waitFor(() => {
        expect(svg).toBeInTheDocument();
      });
    });
  });
});
