// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { renderHook } from '@testing-library/react';
import { useId } from '@/hooks/use-id';
import { WEBSITE_NAME } from '@/constants/common';

describe('hooks/use-id/use-id.spec.tsx', () => {
  describe('A11y', () => {
    it('should returns static id', () => {
      const { result } = renderHook(() => useId('test-id'));
      expect(result.current).toBe('test-id');
    });

    it('should returns random id if static id is not provided', () => {
      const { result } = renderHook(() => useId());
      expect(typeof result.current).toBe('string');
      expect(result.current.includes(WEBSITE_NAME.toLocaleLowerCase())).toBe(
        true
      );
      expect(result.current !== renderHook(() => useId()).result.current).toBe(
        true
      );
    });
  });
});
