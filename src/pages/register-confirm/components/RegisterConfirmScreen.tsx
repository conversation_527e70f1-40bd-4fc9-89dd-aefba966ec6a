// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC, useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useLiff } from '@/contexts/liff';
import { IUserRegisterForm, IUserRegisterPayload } from '@/types/user';
import { IErrorsResponse, IIErrorDetailResponse } from '@/types/errors';
import { useSessionStorage } from '@/hooks/use-storage';
import authApi from '@/services/internal/modules/auth';
import { routers } from '@/constants/routes';
import {
  GENDER_FEMALE_LABEL,
  GENDER_FEMALE_VALUE,
  GENDER_MALE_LABEL,
  GENDER_MALE_VALUE,
  GENDER_OTHER_LABEL,
  USER_ACCESS_TOKEN,
  USER_ACCESS_TOKEN_EXPIRES,
  USER_COMPANY,
  USER_INFO_REGISTER,
  USER_PREFECTURE_SELECTED,
  USER_REGISTER_ERRORS,
} from '@/constants/user';
import { Logo } from '@/components/Logo';
import { Button } from '@/components/Button';
import { Spinner } from '@/components/Spinner';
import { BoxContent } from '@/components/Layout';
import { decrypt } from '@/utils/crypto';
import { destroyCookieApp, setCookieApp } from '@/utils/cookie';
import { redirectToLineChatRoomOA } from '@/utils/redirect';
import { convertToISOFormat } from '@/utils/time';
import { SvgIcon } from '@/components/SvgIcon';

interface RegisterConfirmScreenProps {
  companyId: string;
}

const RegisterConfirmScreen: FC<RegisterConfirmScreenProps> = ({
  companyId,
}) => {
  const router = useRouter();
  const [isLineFriend, setIsLineFriend] = useState(false);
  const [isLoadingRegister, setIsLoadingRegister] = useState(false);
  const [isLoadingGetLineFriend, setIsLoadingGetLineFriend] = useState(false);
  const { isLoggedIn, liff, userInfo, userLineInfo } = useLiff();

  const handleWhenNotUserRegisterInfo = async (value: unknown) => {
    Object.keys(userInfo).length
      ? // User registered
        await router.replace(routers.registered)
      : // User not enter input on register screen
      !value
      ? await router.replace(routers.register)
      : // Not handler
        '';
  };

  const [userInfoRegister, setUserInfoRegister, removeUserInfoRegister] =
    useSessionStorage<IUserRegisterForm>({
      key: USER_INFO_REGISTER,
      callback: async (value: IUserRegisterForm) => {
        await handleWhenNotUserRegisterInfo(value);
      },
    });

  const [prefectureSelected, , removePrefectureSelected] =
    useSessionStorage<string>({
      key: USER_PREFECTURE_SELECTED,
      callback: async (value: string) => {
        await handleWhenNotUserRegisterInfo(value);
      },
    });

  const [, setUserRegisterErrors] = useSessionStorage<IIErrorDetailResponse[]>({
    key: USER_REGISTER_ERRORS,
  });

  const getStatusLineFriend = async () => {
    try {
      setIsLoadingGetLineFriend(true);
      const { friendFlag } = await liff.getFriendship();
      setIsLineFriend(friendFlag);
    } finally {
      setIsLoadingGetLineFriend(false);
    }
  };

  const userRegisterHandler = async () => {
    try {
      setIsLoadingRegister(true);

      if (!userInfoRegister) {
        router.push(routers.register);
        return;
      }

      const payload: IUserRegisterPayload = {
        memberLineId: userLineInfo.userId,
        companyId: companyId,
        email: userInfoRegister.email,
        password: decrypt(userInfoRegister.password),
        birthday: convertToISOFormat(
          userInfoRegister.birthMonth,
          userInfoRegister.birthYear
        ),
        gender: userInfoRegister.gender || GENDER_FEMALE_VALUE,
        prefecture: userInfoRegister.prefecture,
        isFollow: isLineFriend,
      };
      const result = await authApi.registerUser(payload);
      setCookieApp(null, USER_ACCESS_TOKEN, result.accessToken, {
        maxAge: USER_ACCESS_TOKEN_EXPIRES,
      });
      destroyCookieApp(null, USER_COMPANY);
      removeUserInfoRegister();
      removePrefectureSelected();
      redirectToLineChatRoomOA(liff);
    } catch (e: unknown) {
      const errorsDetail = e as IErrorsResponse;
      if (errorsDetail.errors && errorsDetail.errors.length > 0) {
        const errorsArr = errorsDetail.errors;
        setUserRegisterErrors(errorsArr);
        router.push(routers.register);
      }
    } finally {
      setIsLoadingRegister(false);
    }
  };

  const backToRegisterScreenHandler = () => {
    userInfoRegister &&
      setUserInfoRegister({
        ...userInfoRegister,
        password: '',
        confirmPassword: '',
      });
    router.push(routers.register);
  };

  useEffect(() => {
    if (isLoggedIn) {
      getStatusLineFriend();
    }
  }, [isLoggedIn]);

  return (
    <>
      <Logo />
      <div className="space-y-6 !mb-auto">
        <BoxContent
          label="メールアドレス"
          value={userInfoRegister?.email || ''}
        />
        <BoxContent
          label="パスワード"
          value="セキュリティ保護のため、表示していません。"
        />
        <BoxContent
          label="生まれた年・月"
          value={
            `${userInfoRegister?.birthYear}年${userInfoRegister?.birthMonth}月` ||
            ''
          }
        />
        <BoxContent label="お住いの都道府県" value={prefectureSelected || ''} />
        <BoxContent
          label="性別"
          value={
            userInfoRegister?.gender === GENDER_MALE_VALUE
              ? GENDER_MALE_LABEL
              : userInfoRegister?.gender === GENDER_FEMALE_VALUE
              ? GENDER_FEMALE_LABEL
              : GENDER_OTHER_LABEL
          }
        />
      </div>
      <div className="action space-y-2">
        <Button
          fullSized
          size="xl"
          color="primary"
          disabled={isLoadingRegister || isLoadingGetLineFriend}
          onClick={userRegisterHandler}
        >
          {isLoadingRegister && (
            <div className="mr-3">
              <Spinner size="lg" color="primary" />
            </div>
          )}
          登録する
        </Button>
        <Button
          outline
          fullSized
          size="xl"
          color="primary"
          onClick={backToRegisterScreenHandler}
        >
          <span className="mr-3">
            <SvgIcon name="ChevronDoubleLeftIcon" />
          </span>
          入力画面に戻って変更する
        </Button>
      </div>
    </>
  );
};

export default RegisterConfirmScreen;
