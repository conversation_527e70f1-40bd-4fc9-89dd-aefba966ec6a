// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import {
  ITransactionStatusParams,
  ITransactionStatusResponse,
  ITransactionTokenExchangeGiftPayload,
  ITransactionTokenExchangeGiftResponse,
} from '@/types/transactions';
import service from '..';

const basePath = '/transaction';

const transactionsApi = {
  generateTransactionTokenExchangeGift: (
    payload: ITransactionTokenExchangeGiftPayload
  ) =>
    service.post<ITransactionTokenExchangeGiftResponse>(
      `${basePath}/generate-transaction-token`,
      payload
    ),
  getTransactionStatus: (params: ITransactionStatusParams) =>
    service.get<ITransactionStatusResponse>(
      `${basePath}/transaction-status/${params.pcId}/${params.token}`
    ),
};

export default transactionsApi;
