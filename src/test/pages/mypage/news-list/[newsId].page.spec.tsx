// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen } from '@testing-library/react';
import AnnouncementDetailScreen from '@/pages/mypage/news-list/[newsId].page';

jest.mock('next/router', () => require('next-router-mock'));

describe('pages/mypage/news-list/[newsId].page.spec.tsx', () => {
  describe('A11y', () => {
    it('Should render in document', async () => {
      render(<AnnouncementDetailScreen />);
      const button = screen.getByRole('link', {
        name: '＼マイページTOPに戻る／',
      });

      expect(button).toBeInTheDocument();
    });
  });
});
