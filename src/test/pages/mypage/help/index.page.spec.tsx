// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { act, render, screen, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import type { Liff } from '@line/liff';
import HelpScreen from '@/pages/mypage/help.page';
import { LiffContextMockProviders } from '@/test/mocks';

jest.mock('next/router', () => require('next-router-mock'));

const TestHelpScreen = (): JSX.Element => {
  const liffContextValue = {
    liff: { openWindow: () => ({}) } as unknown as Liff,
  };

  return (
    <LiffContextMockProviders contextValue={liffContextValue}>
      <HelpScreen />
    </LiffContextMockProviders>
  );
};

describe('pages/mypage/help/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<TestHelpScreen />);
      const links = screen.getAllByRole('button');

      expect(links.length).toEqual(4);
    });
  });

  describe('User interactions', () => {
    it('should trigger register button', async () => {
      const user = userEvent.setup();
      render(<TestHelpScreen />);
      // get all buttons
      const buttons = screen.getAllByRole('button');

      // click button to redirect to faq page
      await act(() => user.click(buttons[0]));
      expect(buttons[0]).toHaveFocus();

      // click button to open terms modal
      await act(() => user.click(buttons[1]));
      expect(buttons[1]).toHaveFocus();
      const modalTerms = screen.getByRole('dialog');
      const closeButtonTerms = within(modalTerms).getAllByRole('button')[0];
      await act(() => user.click(closeButtonTerms));

      // click button to open policies modal
      await act(() => user.click(buttons[2]));
      expect(buttons[2]).toHaveFocus();
      const modalPolicies = screen.getByRole('dialog');
      const closeButtonPolicies =
        within(modalPolicies).getAllByRole('button')[0];
      await act(() => user.click(closeButtonPolicies));

      // click button to open external link
      await act(() => user.click(buttons[3]));
      expect(buttons[3]).toHaveFocus();
    });
  });
});
