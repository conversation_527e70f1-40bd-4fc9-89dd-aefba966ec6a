// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { OptionType } from '@/components/Select2';
import { useFetch } from '@/hooks/use-fetch';
import authApi from '@/services/internal/modules/auth';
import messageDeliverySettingsApi from '@/services/internal/modules/message-delivery-settings';
import { IGroupedCompanies } from '@/types/message-delivery-settings';
import { useState } from 'react';

export const useMessageDeliverySettingsGet = () => {
  const [companyOn, setCompanyOn] = useState<string[]>([]);
  const [count, setCount] = useState<number>(0);
  const { data: companies } = useFetch<IGroupedCompanies>({
    fetcher: messageDeliverySettingsApi.getMessageDeliverySettingV2,
    initialValue: {},
    handleResponse: (res: IGroupedCompanies) => {
      if (Object.keys(res).length > 0) {
        setCompanyOn(
          Object.values(res)
            .flat()
            .filter(company => company.status === 'on')
            .map(company => company.company_id)
        );
      }
      return res;
    },
    dependencies: [count],
  });

  const { data: myPartnerGroup } = useFetch<OptionType[]>({
    fetcher: authApi.myPartnerGroup,
    initialValue: [],
  });

  return {
    companyOn,
    myPartnerGroup,
    companies,
    setCount,
    count,
  };
};
