// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import dayjs, { UnitType } from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import {
  CONVERT_TO_ISO_FORMAT_REGEX,
  CONVERT_TO_YYYY_M_FORMAT_REGEX,
  EXTRACT_YEAR_AND_MONTH_REGEX,
  SINGLE_DIGIT_REGEX,
} from '@/constants/regex';

dayjs.extend(isSameOrBefore);

interface FormatTimeProps {
  time?: string | Date;
  format?: string;
}

export const getDate = (dateString = '') => {
  const to2digits = (num: number) =>
    String(num).replace(SINGLE_DIGIT_REGEX, '0$1');

  const d = new Date(dateString);

  const month = (d.getMonth() + 1).toString();
  const date = d.getDate().toString();
  const year = d.getFullYear().toString();
  const dayOfWeek = d.getDay();
  const hours = d.getHours().toString();
  const minutes = to2digits(d.getMinutes());

  return {
    month,
    date,
    year,
    dayOfWeek,
    hours,
    minutes,
  };
};

export const convertToSlashSeparatedFormat = (dateString = '') => {
  const { date, month, year } = getDate(dateString);

  return `${year}年${month}月${date}日`;
};

export const convertToCharacterSeparatedFormat = (
  dateString = '',
  type: 'dot' | 'slash' = 'slash'
) => {
  const { date, month, year } = getDate(dateString);
  const separate = type === 'dot' ? '.' : '/';
  return `${year + separate + month + separate + date}`;
};

export const convertToISOFormat = (
  month: string | number,
  year: string | number
) => `${year}-${month}`.replace(CONVERT_TO_ISO_FORMAT_REGEX, '$10$2');

export const extractYearAndMonth = (time = ''): [string, string] => {
  const formatedTime = time.replace(CONVERT_TO_YYYY_M_FORMAT_REGEX, '$1$2');

  const [, year, month] = EXTRACT_YEAR_AND_MONTH_REGEX.exec(formatedTime) ?? [];

  return [year, month];
};

export const convertDateWithFormat = (
  dateString: string,
  type:
    | 'YearMonthDateWithSlash'
    | 'YearMonthDateWithString'
    | 'YearMonthDateWithoutMinute' = 'YearMonthDateWithSlash'
) => {
  const daysInWeek = ['日', '月', '火', '水', '木', '金', '土']; // Element 0 is Sunday

  const { date, month, year, dayOfWeek, hours, minutes } = getDate(dateString);

  switch (type) {
    case 'YearMonthDateWithSlash':
      return `${year}/${month}/${date}（${daysInWeek[dayOfWeek]}）${hours}:${minutes}`;
    case 'YearMonthDateWithString':
      return `${year}年${month}月${date}日（${daysInWeek[dayOfWeek]}）`;
    case 'YearMonthDateWithoutMinute':
      return `${year}/${month}/${date}（${daysInWeek[dayOfWeek]}）`;
    default:
      return `${year}/${month}/${date}（${daysInWeek[dayOfWeek]}）${hours}:${minutes}`;
  }
};

export const getPrevMonth = (current: Date) => {
  const newDate = new Date(current);
  newDate.setDate(1);
  newDate.setMonth(current.getMonth() - 1);

  return newDate;
};

export const formatTime = ({
  time = new Date(),
  format = 'YYYY/MM/DD',
}: FormatTimeProps): string => {
  return dayjs(time).format(format);
};

export const isSameBefore = (
  time?: string | Date | null,
  unit?: UnitType
): boolean => {
  return dayjs().isSameOrBefore(time, unit);
};
