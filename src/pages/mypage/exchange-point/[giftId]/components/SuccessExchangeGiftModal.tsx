// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import Image from 'next/image';
import { Modal } from '@/components/Modal';
import { Button } from '@/components/Button';
import { SvgIcon } from '@/components/SvgIcon';
import { routers } from '@/constants/routes';
import { TAB_EXCHANGED_GIFT } from '@/constants/mypage';
import { IExchangeableGiftDetailResponse } from '@/types/exchange-points';

interface SuccessExchangeGiftModalProps {
  isShowModal: boolean;
  submit: (url: string) => void;
  redirectUrl: string;
  gift: IExchangeableGiftDetailResponse;
}

const SuccessExchangeGiftModal: FC<SuccessExchangeGiftModalProps> = ({
  gift,
  redirectUrl,
  submit,
  isShowModal,
}) => {
  return (
    <Modal
      size="md"
      show={isShowModal}
      data-testid="success-exchange-gift-modal"
      innerClass="relative w-full rounded-modal bg-white shadow max-h-full overflow-y-auto"
      innerStyle={{ maxHeight: '80vh', paddingBlock: 0 }}
    >
      <Modal.Header>
        <h3 className="text-theme-primary text-lg text-center font-bold">
          交換完了しました
        </h3>
      </Modal.Header>
      <Modal.Body>
        <div className="mt-2.5 mb-2">
          {gift.gift_image ? (
            <figure className="relative pt-[100%]">
              <Image
                fill
                quality={100}
                src={gift.gift_image}
                alt={gift.gift_name}
                className="w-full object-cover rounded"
              />
            </figure>
          ) : (
            <div className="relative w-full pt-[100%] bg-[#E2E2E2] rounded">
              <div className="flex flex-col justify-center items-center gap-3 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                <SvgIcon name="ImageXLarge" />
                <p className="text-sm text-gray-3 leading-[22px] font-bold min-w-[200px] text-center">
                  画像が表示できませんでした。
                </p>
              </div>
            </div>
          )}

          <div className="space-y-1 mt-6">
            <h3
              title={gift.gift_company_name}
              className="text-sm mb-1.5 break-words line-clamp-2"
            >
              {gift.gift_company_name}
            </h3>
            <p className="text-base font-bold break-words line-clamp-3">
              {gift.gift_name}
            </p>
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer className="space-y-4">
        <Button
          customSpan="!py-3 !px-5"
          fullSized
          size="lg"
          color="primary"
          onClick={() => submit(redirectUrl)}
        >
          受け取りページを開く
        </Button>
        <Button
          customSpan="!py-3 !px-5"
          outline
          fullSized
          size="lg"
          color="primary"
          onClick={() =>
            submit(`${routers.pointHistory}/?tab=${TAB_EXCHANGED_GIFT}`)
          }
        >
          交換ギフト一覧に戻る
        </Button>
        <div className="text-xs text-gray-3 leading-[18px]">
          交換したギフトはポイント履歴・交換ページの「交換履歴」からご確認いただけます
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default SuccessExchangeGiftModal;
