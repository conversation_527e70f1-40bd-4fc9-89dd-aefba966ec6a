// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
/* eslint-disable @typescript-eslint/ban-ts-comment */
/* eslint-disable testing-library/no-wait-for-side-effects */
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import type { Liff } from '@line/liff';
import { LiffContextMockProviders } from '@/test/mocks';
import { LOTTERY_DETAIL_RESPONSE } from '@/mocks/lottery';
import lotteryApi from '@/services/internal/modules/lottery';
import LotteryScreen from '@/pages/lottery/[rmAliasId].page';
import { IGetLotteryResponse } from '@/types/lottery';
import { compressImage, getMimeType } from '@/utils/file';

jest.mock('next/router', () => require('next-router-mock'));
jest.mock('@/utils/file', () => ({
  getMimeType: jest.fn(),
  compressImage: jest.fn(),
}));
// eslint-disable-next-line @typescript-eslint/no-var-requires
const useRouter = jest.spyOn(require('next/router'), 'useRouter');

const useMockRouter = (query: Record<string, string | string[]> = {}) => {
  return useRouter.mockReturnValue({
    query,
    push: jest.fn(),
  });
};

const useMockService = (data: Partial<IGetLotteryResponse> = {}) => {
  return jest
    .spyOn(lotteryApi, 'getLottery')
    .mockResolvedValue({ ...LOTTERY_DETAIL_RESPONSE, ...data });
};

const useMockFile = (file: File) => {
  URL.createObjectURL = jest.fn();
  // @ts-ignore
  getMimeType.mockReturnValue(file.type);
  // @ts-ignore
  compressImage.mockResolvedValue(file);

  return file;
};

const TestLotteryScreen = (): JSX.Element => {
  const liffContextValue = {
    liff: {
      getContext: () => ({}),
      openWindow: () => ({}),
      closeWindow: () => ({}),
    } as unknown as Liff,
  };
  return (
    <LiffContextMockProviders contextValue={liffContextValue}>
      <LotteryScreen appConfig={{ liffId: 'lorem', companyId: 'ipsum' }} />
    </LiffContextMockProviders>
  );
};

describe('pages/lottery/[rmAliasId].page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<TestLotteryScreen />);
      const status = screen.getByRole('status');

      expect(status).toBeInTheDocument();
    });
  });

  describe('Fetching success', () => {
    it('should call api return response without lotteryId', async () => {
      useMockService({ lotteryId: '' });

      render(<TestLotteryScreen />);

      await waitFor(() => {
        const images = screen.getAllByRole('img');
        expect(images.length).toEqual(1);
      });
    });

    it('should call api return response without lottery detail', async () => {
      useMockService({ lottery: undefined });

      render(<TestLotteryScreen />);

      await waitFor(() => {
        const images = screen.getAllByRole('img');
        expect(images.length).toEqual(1);
      });
    });

    it('should call api return response with isExist=false', async () => {
      useMockService({ isExist: false });

      render(<TestLotteryScreen />);

      await waitFor(() => {
        const images = screen.getAllByRole('img');
        expect(images.length).toEqual(1);
      });
    });

    it('should call api return response with isExpired=true and isPause=true', async () => {
      useMockService({ isExpired: true, isPause: true });

      render(<TestLotteryScreen />);

      await waitFor(() => {
        const images = screen.getAllByRole('img');
        expect(images.length).toEqual(1);
      });
    });

    it('should call api return response with isJoin=true', async () => {
      useMockService({ isJoin: true });

      render(<TestLotteryScreen />);

      await waitFor(() => {
        const images = screen.getAllByRole('img');
        expect(images.length).toEqual(1);
      });
    });

    it('should call api return response with isJoin=true', async () => {
      useMockService();

      render(<TestLotteryScreen />);

      await waitFor(() => {
        const images = screen.getAllByRole('img');
        expect(images.length).toEqual(2);
      });
    });
  });

  describe('Fetching failure', () => {
    it('should call api return statusCode=500', async () => {
      jest
        .spyOn(lotteryApi, 'getLottery')
        .mockRejectedValue({ statusCode: 500 });

      render(<TestLotteryScreen />);
      const status = screen.getByRole('status');

      await waitFor(() => {
        expect(status).toBeInTheDocument();
      });
    });
  });

  describe('Redirect', () => {
    it('should redirect to chat room LINE OA', async () => {
      useMockRouter({ is_active: 'true' });

      render(<TestLotteryScreen />);
      const status = screen.getByRole('status');

      await waitFor(() => {
        expect(status).toBeInTheDocument();
      });
    });

    it('should redirect to lottery screen', async () => {
      useMockRouter({ image: 'uploaded' });

      render(<TestLotteryScreen />);
      const status = screen.getByRole('status');

      await waitFor(() => {
        expect(status).toBeInTheDocument();
      });
    });
  });

  describe('Upload file', () => {
    it('should upload file', async () => {
      const fileMaxSize = new File(['lorem'], 'lorem.png', {
        type: 'image/png',
      });
      Object.defineProperty(fileMaxSize, 'size', { value: 1024 * 1024 * 10 });

      const fileGIF = new File(['lorem'], 'lorem.png', {
        type: 'image/gif',
      });
      const filePNG = new File(['lorem'], 'lorem.png', {
        type: 'image/png',
      });

      useMockService();

      render(<TestLotteryScreen />);

      await waitFor(() => {
        const button = screen.getByRole('button');

        fireEvent.click(button);

        // not upload file
        fireEvent.change(screen.getByTestId('upload-lottery-file'), {
          target: { files: [] },
        });

        // upload file max size
        jest.clearAllMocks();
        useMockFile(fileMaxSize);
        fireEvent.change(screen.getByTestId('upload-lottery-file'), {
          target: { files: [fileMaxSize] },
        });

        // upload file gif
        jest.clearAllMocks();
        useMockFile(fileGIF);
        fireEvent.change(screen.getByTestId('upload-lottery-file'), {
          target: { files: [fileGIF] },
        });

        // upload file png
        jest.clearAllMocks();
        useMockFile(filePNG);
        fireEvent.change(screen.getByTestId('upload-lottery-file'), {
          target: { files: [filePNG] },
        });

        expect(button).toBeInTheDocument();
      });
    });
  });
});
