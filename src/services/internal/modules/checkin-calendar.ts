// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import {
  ICheckinCalendarParams,
  ICheckinCalendarResponse,
  ICalendarMemoPayload,
} from '@/types/checkin-calendar';
import service from '..';

const basePathCheckinCalendar = '/calendar/checkin';
const basePathCalendarMemo = '/calendar/memo';

const checkinCalendarApi = {
  getCheckinCalendar: (params: ICheckinCalendarParams) =>
    service.get<ICheckinCalendarResponse>(`${basePathCheckinCalendar}`, {
      yearMonth: params.yearMonth,
      company_id: params.companyId,
    }),
  editCheckinCalendarMemo: (payload: ICalendarMemoPayload) =>
    service.post(`${basePathCalendarMemo}`, payload),
};

export default checkinCalendarApi;
