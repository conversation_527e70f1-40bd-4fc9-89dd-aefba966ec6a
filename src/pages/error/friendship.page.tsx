// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { Button } from '@/components/Button';
import { BoxContent } from '@/components/BoxContent';
import { useError } from '@/hooks/use-error';

const FriendshipErrorScreen: FC = () => {
  const { handler } = useError();

  return (
    <>
      <div className="container mx-auto">
        <BoxContent>
          <BoxContent.Body className="pt-[51px]">
            <div className="space-y-6">
              <h2 className="text-center text-lg text-theme-primary font-bold">
                ピアコネ会員登録の流れ
              </h2>

              <p className="text-sm leading-5">
                ピアコネ会員登録をするとLINE公式アカウントを通じてお得なサービスが受けられます。
              </p>

              <div className="text-sm space-y-2">
                <h3 className="font-bold leading-6">
                  ステップ１：LINE友だち追加
                </h3>
                <p className="leading-5">
                  LINEの認証画面で、各種認証（メッセージ送信など）を許可し、友だち追加またはブロック解除してください。
                </p>
              </div>

              <div className="text-sm space-y-2">
                <h3 className="font-bold leading-6">
                  ステップ２：ピアコネ会員登録
                </h3>
                <p className="leading-5">
                  会員登録画面で、ユーザー情報を入力してください。
                </p>
              </div>
            </div>
          </BoxContent.Body>
          <BoxContent.Footer>
            <Button
              fullSized
              type="submit"
              size="xl"
              color="primary"
              onClick={handler}
            >
              認証画面に戻る
            </Button>
          </BoxContent.Footer>
        </BoxContent>
      </div>
    </>
  );
};

export default FriendshipErrorScreen;
