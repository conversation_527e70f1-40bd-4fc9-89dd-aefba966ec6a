// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen } from '@testing-library/react';
import HowToGetPointsScreen from '@/pages/mypage/how-to-get-points.page';

jest.mock('next/router', () => require('next-router-mock'));

describe('pages/mypage/how-to-get-points/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<HowToGetPointsScreen />);
      const h3 = screen.getByRole('heading');

      expect(h3).toBeInTheDocument();
    });
  });
});
