// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import Link from 'next/link';
import classNames from 'classnames';
import { forwardRef, type ComponentProps } from 'react';
import type {
  IThemeAligns,
  IThemeBoolean,
  IThemeColors,
  IThemeSizes,
} from '@/types/theme';
import { styles } from './Button.styles';

export interface IButtonColors extends IThemeColors {
  [key: string]: string;
  gray: string;
  'primary-gray': string;
  'gray-accent': string;
  'gray-10': string;
  link: string;
  'link-primary': string;
}

export interface IButtonOutlineColors extends IThemeColors {
  [key: string]: string;
  gray: string;
  'primary-gray': string;
  'gray-accent': string;
  'gray-10': string;
  'link-primary': string;
}

export interface IButtonSizes
  extends Pick<IThemeSizes, 'xs' | 'sm' | 'md' | 'lg' | 'xl'> {
  [key: string]: string;
}

export interface IButtonTheme {
  base: string;
  fullSized: string;
  color: IButtonColors;
  disabled: string;
  inner: {
    base: string;
    outline: string;
  };
  outline: IThemeBoolean & {
    color: IButtonOutlineColors;
    pill: IThemeBoolean;
  };
  pill: IThemeBoolean;
  size: IButtonSizes;
  align: IThemeAligns;
}

export interface IButtonProps
  extends Omit<ComponentProps<'button'>, 'color' | 'ref'> {
  color?: keyof IButtonColors;
  href?: string;
  outline?: boolean;
  fullSized?: boolean;
  pill?: boolean;
  size?: keyof IButtonSizes;
  align?: keyof IThemeAligns;
  customSpan?: string;
}

export const Button = forwardRef<
  HTMLButtonElement | HTMLAnchorElement,
  IButtonProps
>(
  (
    {
      children,
      color = 'info',
      disabled = false,
      href,
      outline = false,
      pill = false,
      fullSized,
      size = 'md',
      align = 'center',
      customSpan,
      className,
      ...props
    },
    ref
  ) => {
    const isLink = typeof href !== 'undefined';

    const theme = styles;

    const Component = isLink ? Link : 'button';
    const theirProps = props as object;

    return (
      <Component
        className={classNames(
          disabled && theme.disabled,
          theme.color[color],
          outline &&
            (theme.outline.color[color] ?? theme.outline.color.default),
          theme.base,
          theme.pill[pill ? 'on' : 'off'],
          fullSized && theme.fullSized,
          className
        )}
        disabled={disabled}
        href={href as string}
        type={isLink ? undefined : 'button'}
        ref={ref as never}
        {...theirProps}
      >
        <span
          className={classNames(
            theme.inner.base,
            theme.outline[outline ? 'on' : 'off'],
            theme.outline.pill[outline && pill ? 'on' : 'off'],
            theme.size[size],
            theme.align[align],
            outline && !theme.outline.color[color] && theme.inner.outline,
            customSpan
          )}
        >
          {typeof children !== 'undefined' && children}
        </span>
      </Component>
    );
  }
);

Button.displayName = 'Button';
