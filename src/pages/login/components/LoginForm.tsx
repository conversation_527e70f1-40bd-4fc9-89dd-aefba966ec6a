// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React, { FC, useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { Message } from '@/constants/message';
import {
  EMAIL,
  PASSWORD,
  USER_ACCESS_TOKEN,
  USER_ACCESS_TOKEN_EXPIRES,
  USER_REGISTER_ERRORS,
} from '@/constants/user';
import punycode from 'punycode';
import isEmail from 'validator/lib/isEmail';
import { Button } from '@/components/Button';
import { Label } from '@/components/Label';
import { TextInput } from '@/components/TextInput';
import { ShowHiddenPassword } from '@/components/Layout/ShowHiddenPassword';
import { Logo } from '@/components/Logo';
import { IUserLoginPayload } from '@/types/user';
import { useLiff } from '@/contexts/liff';
import { setCookieA<PERSON> } from '@/utils/cookie';
import authApi from '@/services/internal/modules/auth';
import { redirectToLineChatRoomOA } from '@/utils/redirect';
import { Spinner } from '@/components/Spinner';
import { IBaseResponseError, IIErrorDetailResponse } from '@/types/errors';
import { BAD_REQUEST } from '@/constants/status-response';
import classNames from 'classnames';
import { templateString } from '@/utils/text';
import { EMAIL_EXCLUDE_NON_ASCII_REGEX } from '@/constants/regex';
import { useSessionStorage } from '@/hooks/use-storage';

const schema = yup.object({
  email: yup
    .string()
    .required(templateString(Message.validation.required, { field: EMAIL }))
    .test('email', Message.validation.emailFormatInvalid, (value?: string) => {
      return isEmail(value || '');
    })
    .test('email', Message.validation.emailFormatInvalid, (value?: string) => {
      return EMAIL_EXCLUDE_NON_ASCII_REGEX.test(
        // resolve bug https://stackoverflow.com/questions/32117497/input-type-email-value-in-chrome-with-accented-characters-wrong
        punycode.toUnicode(value || '')
      );
    }),
  password: yup
    .string()
    .required(templateString(Message.validation.required, { field: PASSWORD }))
    .min(8, Message.validation.passwordLengthInvalid)
    .max(20, Message.validation.passwordLengthInvalid),
});

const LoginForm: FC = () => {
  const router = useRouter();
  const [isError, setIsError] = useState(false);
  const [isShowPassword, setIsShowPassword] = useState(false);
  const [isLoadingLogin, setIsLoadingLogin] = useState(false);
  const [blockTimeout, setBlockTimeout] = useState(0);
  const [isDisableEmail, setIsDisableEmail] = useState(false);
  const { liff, userInfo, setUserInfo } = useLiff();
  const [userRegisterErrors, , removeUserRegisterErrors] = useSessionStorage<
    IIErrorDetailResponse[]
  >({
    key: USER_REGISTER_ERRORS,
  });

  const {
    register,
    setValue,
    handleSubmit,
    setError,
    formState: { errors },
    reset,
    watch,
  } = useForm<IUserLoginPayload>({
    mode: 'onChange',
    resolver: yupResolver(schema),
  });

  useEffect(() => {
    if (router.query.email) {
      setValue('email', String(router.query.email));
    }
  }, [router.query.email, setValue]);

  const loginHandler = async (data: { email: string; password: string }) => {
    try {
      setIsLoadingLogin(true);

      const payload: IUserLoginPayload = {
        email: data.email,
        password: data.password,
        auth_method: 'line',
        line_access_token: liff.getAccessToken() as string,
      };

      const result = await authApi.login(payload);
      setCookieApp(null, USER_ACCESS_TOKEN, result.access_token, {
        maxAge: USER_ACCESS_TOKEN_EXPIRES,
      });
      setUserInfo({ ...userInfo, ...result.profile });
      removeUserRegisterErrors();
      redirectToLineChatRoomOA(liff);
    } catch (e: unknown) {
      const errData = (e as IBaseResponseError)?.errors || {};
      const { message, statusCode, block_time } = errData;
      const isIncorrectEmail =
        statusCode === BAD_REQUEST && message?.includes('email');
      const isIncorrectPassword =
        statusCode === BAD_REQUEST && message?.includes('password');

      if (isIncorrectEmail || isIncorrectPassword) {
        setError('email', {
          type: 'server',
        });
        setError('password', {
          type: 'server',
        });
      }

      if (block_time && block_time > 0) {
        reset();
        setBlockTimeout(block_time);
      } else {
        setIsError(true);
      }
    } finally {
      setIsLoadingLogin(false);
    }
  };

  const email = watch('email');
  const password = watch('password');

  useEffect(() => {
    if (email && password) setIsError(false);
  }, [email, password]);

  useEffect(() => {
    if (blockTimeout > 0) {
      const blockTimeDate = new Date(blockTimeout * 1000);
      const timeDiff = blockTimeDate.getTime() - new Date().getTime();
      if (timeDiff > 0) {
        const timeoutId = setTimeout(() => {
          window.location.reload();
        }, timeDiff);
        return () => clearTimeout(timeoutId);
      }
    }
  }, [blockTimeout]);

  useEffect(() => {
    if (Object.keys(userInfo).length) {
      redirectToLineChatRoomOA(liff);
    }
  }, [router, userInfo]);

  useEffect(() => {
    if (userRegisterErrors && userRegisterErrors.length > 0) {
      userRegisterErrors.forEach((err: IIErrorDetailResponse) => {
        if (err.property === 'email') {
          const errConstraintsEmail = err.constraints;
          const errorMsg = errConstraintsEmail.IsValidEmail;
          const matchedEmail =
            typeof errorMsg === 'string'
              ? errorMsg.match(
                  /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/
                )?.[0]
              : null;
          if (matchedEmail && matchedEmail === router.query.email) {
            setIsDisableEmail(true);
          }
        }
      });
    }
  }, [setError, userRegisterErrors, router.query.email]);

  return (
    <>
      <Logo />
      {blockTimeout > 0 && (
        <p className="!text-theme-error-primary text-sm !mt-10 !mb-3">
          メールアドレスかパスワードが正しくありません。
          安全のため、一時的にログインができません。
          しばらく時間をおいてから、再度お試しください。
        </p>
      )}

      {isError && (
        <p className="!text-theme-error-primary text-sm !mt-10 !mb-3">
          メールアドレスかパスワードが正しくありません。ご確認ください。
        </p>
      )}

      <form
        noValidate
        className="space-y-6"
        onSubmit={handleSubmit(loginHandler)}
      >
        {/* Email */}
        <div>
          <Label htmlFor="email" className="block mb-2">
            メールアドレス
          </Label>
          <TextInput
            disabled={blockTimeout > 0 || isDisableEmail}
            type="email"
            id="email"
            placeholder="（例）<EMAIL>"
            {...register('email')}
            color={errors.email ? 'failure' : 'default'}
            helperText={errors.email?.message as string}
          />
        </div>
        {/* Password */}
        <div className="mb-10">
          <Label htmlFor="password" className="block mb-2">
            パスワード（半角英数字8文字以上20文字以内）
          </Label>
          <TextInput
            disabled={blockTimeout > 0}
            type={isShowPassword ? 'text' : 'password'}
            id="password"
            autoComplete="off"
            {...register('password')}
            color={errors.password ? 'failure' : 'default'}
            helperText={errors.password?.message as string}
            interactableIcon={
              <ShowHiddenPassword
                isShow={isShowPassword}
                setIsShow={setIsShowPassword}
              />
            }
          />
        </div>
        {/* Submit */}
        <Button
          fullSized
          type="submit"
          size="xl"
          color={'primary'}
          disabled={isLoadingLogin || blockTimeout > 0}
          className={classNames({
            'pointer-events-none': blockTimeout > 0,
          })}
        >
          {isLoadingLogin && (
            <div className="mr-3">
              <Spinner size="lg" color="primary" />
            </div>
          )}
          ログイン
        </Button>
      </form>
    </>
  );
};

export default LoginForm;
