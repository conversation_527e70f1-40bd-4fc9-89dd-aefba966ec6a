// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import service from '..';
import { IPointHistoriesParams, IPointHistoriesResponse } from '@/types/point';

const basePath = '/point-histories';

const pointApi = {
  getCurrentPoint: () => service.get<number>(`${basePath}/get-current-point`),
  getPointHistories: (params: IPointHistoriesParams) =>
    service.get<IPointHistoriesResponse>(
      `${basePath}/me?page=${params.page}&limit=${params.limit}`
    ),
};

export default pointApi;
