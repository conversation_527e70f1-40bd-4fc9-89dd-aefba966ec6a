// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import type { Liff } from '@line/liff';
import ChangeRichMenuScreen from '@/pages/changerm/index.page';
import richMenuApi from '@/services/internal/modules/richmenu';
import { LiffContextMockProviders } from '@/test/mocks';

const TestChangeRichMenuScreen = (): JSX.Element => {
  const liffContextValue = {
    liff: { openWindow: () => ({}) } as unknown as Liff,
  };
  return (
    <LiffContextMockProviders contextValue={liffContextValue}>
      <ChangeRichMenuScreen />
    </LiffContextMockProviders>
  );
};

describe('pages/changerm/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in document', async () => {
      render(<TestChangeRichMenuScreen />);

      expect(screen.getAllByRole('img').length).toEqual(4);
    });
  });

  describe('User interactions', () => {
    let state: jest.SpyInstance<[unknown, React.Dispatch<unknown>], []>;

    beforeAll(() => {
      state = jest.spyOn(React, 'useState');
      const response = jest.spyOn(richMenuApi, 'linkRichMenuWithCompanyId');
      response.mockResolvedValue({
        isSuccess: true,
      });
    });

    it('should trigger `onClick` rich menu with loading = false', async () => {
      state.mockReturnValue([false, jest.fn()]);

      render(<TestChangeRichMenuScreen />);

      const element = screen.getByTestId('C0001');
      fireEvent.click(element);

      expect(element).toHaveTextContent('みんチャレ');
    });

    it('should trigger `onClick` rich menu with loading = true', async () => {
      state.mockReturnValue([true, jest.fn()]);

      render(<TestChangeRichMenuScreen />);
      const element = screen.getByTestId('C0001');
      fireEvent.click(element);

      expect(element).toHaveTextContent('みんチャレ');
    });
  });
});
