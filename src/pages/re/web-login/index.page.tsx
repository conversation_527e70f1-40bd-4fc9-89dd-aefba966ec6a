// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import Loading from '@/components/Spinner/Loading';
import { UNKNOWN } from '@/constants/define';
import { useLiff } from '@/contexts/liff';
import Head from 'next/head';
import { useEffect } from 'react';

const RedirectWebLoginScreen = () => {
  const { userInfo, liff } = useLiff();
  const webBaseUrl = process.env.INTERNAL_WEB_BASE_URL || UNKNOWN;

  useEffect(() => {
    if (webBaseUrl && webBaseUrl !== UNKNOWN) {
      const encodedEmail = encodeURIComponent(userInfo.email || '');
      const externalUrl = `${webBaseUrl}/login?email=${encodedEmail}`;

      if (liff) {
        liff.openWindow({
          url: externalUrl,
          external: true,
        });
        liff.closeWindow();
      } else {
        window.open(externalUrl, '_blank');
      }
    }
  }, [webBaseUrl, liff]);

  return (
    <>
      <Head>
        <title>ログイン</title>
      </Head>
      <Loading />
    </>
  );
};

export default RedirectWebLoginScreen;
