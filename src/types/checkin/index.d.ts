// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { EStatusChecking } from '@/constants/enum';
export interface IScanQrResult {
  value: string;
}

export interface ICheckinPayload {
  company_id: string;
  agency_id: string;
  agency_token: string;
}

export interface ICheckinCompanyResponse {
  id: string;
  name: string;
  agency: string;
}

export interface ICheckinResponse {
  success: boolean;
  company_name: string;
  company_id: string;
  company_image: string;
  current_total_point: number;
  point_amount_checkin: number;
  stamp_status: EStatusChecking;
}
