// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { RichMenuContent } from '@/components/Richmenu';
import Loading from '@/components/Spinner/Loading';
import { useLiff } from '@/contexts/liff';
import { useFetch } from '@/hooks/use-fetch';
import { ICompany } from '@/types/company';
import { IRichMenu } from '@/types/richmenu';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';
import MypageLayout from './components/MypageLayout';
import companyApi from '@/services/internal/modules/company';

const PartnerListScreen: FC = () => {
  const { isLoggedIn } = useLiff();
  const router = useRouter();
  const [isLoadingLinkRichMenu, setIsLoadingLinkRichMenu] = useState(false);
  const [richMenuIdSelected, setRichMenuIdSelected] = useState<string>('');

  const onSelectRichMenu = async ({
    company_id: companyId,
    url_liff,
  }: IRichMenu) => {
    setIsLoadingLinkRichMenu(true);
    setRichMenuIdSelected(companyId);
    url_liff && (await router.push(url_liff));
  };

  const { data: companies } = useFetch<ICompany>({
    fetcher: companyApi.getCompanyPartnerList,
    initialValue: [] as unknown as ICompany,
  });

  return (
    <>
      {!isLoggedIn ? (
        <Loading />
      ) : (
        <MypageLayout spacingBottom="4xl">
          <RichMenuContent
            richmenus={Array.isArray(companies) ? companies : [companies]}
            data-testid="partner"
            loading={isLoadingLinkRichMenu}
            richMenuIdSelected={richMenuIdSelected}
            onSelectRichMenu={onSelectRichMenu}
          />
        </MypageLayout>
      )}
    </>
  );
};

export default PartnerListScreen;
