// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { useState } from 'react';
import { render, screen } from '@testing-library/react';
import { useIsomorphicEffect } from '@/hooks/use-isomorphic-effect';

const Target = (): JSX.Element => {
  const [mounted, setMounted] = useState(false);

  useIsomorphicEffect(() => {
    setMounted(true);
  }, []);

  return <div data-testid="target">{mounted ? 'mounted' : 'unmount'}</div>;
};

describe('hooks/use-isomorphic-effect/use-isomorphic-effect.spec.tsx', () => {
  describe('A11y', () => {
    it('should mounted', () => {
      render(<Target />);
      const target = screen.getByTestId('target');
      expect(target).toHaveTextContent('mounted');
    });
  });
});
