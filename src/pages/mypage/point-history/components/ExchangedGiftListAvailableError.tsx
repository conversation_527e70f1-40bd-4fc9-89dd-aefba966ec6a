// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React, { FC } from 'react';
import { MessageBox } from '@/components/MessageBox';

interface ExchangedGiftListAvailableErrorProps {
  retry?: () => void;
}

export const ExchangedGiftListAvailableError: FC<
  ExchangedGiftListAvailableErrorProps
> = ({ retry }) => {
  return (
    <MessageBox className="pt-[85px]">
      <MessageBox.Body className="max-w-[75vw]">
        <MessageBox.Icon name="AccessDenied" />
        <MessageBox.Content>
          現在ポイント交換履歴を読み込めないか、 ポイント交換履歴の準備中です。
          <br />
          時間を空けて再度お試しください。
        </MessageBox.Content>
      </MessageBox.Body>
      <MessageBox.Action onClick={retry}>リロード</MessageBox.Action>
    </MessageBox>
  );
};
