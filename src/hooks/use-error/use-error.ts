// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useLiff } from '@/contexts/liff';
import { destroyCookieApp } from '@/utils/cookie';
import { redirectToLineLoginWithoutCompany } from '@/utils/redirect';
import { routers } from '@/constants/routes';
import { USER_ACCESS_TOKEN } from '@/constants/user';

export function useError() {
  const router = useRouter();
  const { redirectTo, unblocked } = router.query;
  const { error } = useLiff();

  const handleClick = () => {
    const query: { redirectTo?: string; unblocked?: string } = {
      redirectTo: redirectTo ? redirectTo.toString() : routers.home,
    };
    if (unblocked) {
      query.unblocked = unblocked.toString();
    }

    destroyCookieApp(null, USER_ACCESS_TOKEN);
    redirectToLineLoginWithoutCompany(query);
  };

  useEffect(() => {
    if (!error) {
      router.push(routers.home);
    }
  }, [router, error]);

  return { handler: handleClick };
}
