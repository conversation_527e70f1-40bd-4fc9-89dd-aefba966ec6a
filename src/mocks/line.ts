// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
export const LINE_IS_LOGGED_IN_TRUE = true;
export const LINE_GET_FRIENDSHIP_TRUE = { friendFlag: true };
export const LINE_GET_FRIENDSHIP_FALSE = { friendFlag: false };
export const LINE_GET_ACCESS_TOKEN_SUCCESS = 'Lorem';
export const LINE_GET_CONTEXT = {
  type: 'utou',
  userId: 'U193c264c37035c8f7e3646f40e58b7bf',
};
export const LINE_GET_PROFILE = {
  userId: 'U193c264c37035c8f7e3646f40e58b7bf',
  displayName: 'Lorem',
};
export const LINE_PERMISSION_MESSAGE_GRANTED = { state: 'granted' };
export const LINE_PERMISSION_MESSAGE_PROMPT = { state: 'prompt' };
