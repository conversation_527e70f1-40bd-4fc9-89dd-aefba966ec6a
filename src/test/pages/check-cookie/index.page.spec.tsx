// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import CheckCookieScreen from '@/pages/check-cookie/index.page';

jest.mock('next/router', () => require('next-router-mock'));

const useMockCookie = (value = '') => {
  Object.defineProperty(window.document, 'cookie', {
    value,
    writable: true,
  });
};

interface CheckCookieScreenTestProps {
  cookies?: { [key: string]: string };
}

const CheckCookieScreenTest: FC<CheckCookieScreenTestProps> = ({ cookies }) => {
  const appConfig = { liffId: 'liffId', companyId: 'companyId', cookies };

  return <CheckCookieScreen appConfig={appConfig} />;
};

describe('pages/check-cookie/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<CheckCookieScreenTest cookies={{ accessToken: 'Lorem' }} />);

      expect(screen.getByText('accessToken')).toBeInTheDocument();
    });
  });

  describe('User interactions', () => {
    it('should delete cookie', async () => {
      useMockCookie('accessToken=lorem');

      render(<CheckCookieScreenTest />);
      const action = screen.getAllByTestId('delete-cookie')[0];
      fireEvent.click(action);

      expect(action).toBeInTheDocument();
    });

    it('should delete all cookie', async () => {
      useMockCookie('accessToken=lorem');

      render(<CheckCookieScreenTest />);
      const action = screen.getByRole('button');
      fireEvent.click(action);

      expect(action).toBeInTheDocument();
    });

    it('should reload screen', async () => {
      useMockCookie();

      render(<CheckCookieScreenTest />);
      const action = screen.getByRole('button');
      fireEvent.click(action);

      expect(action).toBeInTheDocument();
    });
  });
});
