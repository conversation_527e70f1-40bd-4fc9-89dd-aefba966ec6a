// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { Button } from '@/components/Button';
import { Modal } from '@/components/Modal';
import React, { Dispatch, SetStateAction } from 'react';
interface ModalSubmitProps {
  setIsOpenModalConfirm: Dispatch<SetStateAction<boolean>>;
  isOpenModalConfirm: boolean;
  onCancelEdit: (allowEdit: boolean) => void;
}

const ModalSubmit: React.FC<ModalSubmitProps> = ({
  isOpenModalConfirm,
  setIsOpenModalConfirm,
  onCancelEdit,
}) => {
  return (
    <Modal
      size="sm"
      show={isOpenModalConfirm}
      innerClass="max-h-[75%] overflow-y-auto"
    >
      <Modal.Header>
        <h3 className="text-lg text-center font-bold">
          変更中の設定があります
        </h3>
      </Modal.Header>
      <Modal.Body>
        <div className="p-5">
          <p className="text-sm font-medium">
            設定を適用せずにグループ表示を変更しますか？
          </p>
        </div>
      </Modal.Body>
      <Modal.Footer className=" pt-4 px-5 pb-5">
        <div className="flex items-center justify-between">
          <Button
            outline
            size="xl"
            color="dark"
            onClick={() => onCancelEdit(false)}
          >
            適用せずに表示変更
          </Button>
          <Button
            color="primary"
            size="xl"
            type="submit"
            onClick={() => onCancelEdit(true)}
          >
            適用して表示変更
          </Button>
        </div>
        <Button
          className="mt-4 block mx-auto"
          customSpan="!py-0 !px-0 hover:text-theme-main"
          size="xl"
          color="link"
          onClick={() => setIsOpenModalConfirm(false)}
        >
          そのまま設定画面に戻る
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ModalSubmit;
