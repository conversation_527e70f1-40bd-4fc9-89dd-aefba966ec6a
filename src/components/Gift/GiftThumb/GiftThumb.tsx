// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import classNames from 'classnames';
import Image, { ImageProps } from 'next/image';
import { styles } from './GiftThumb.styles';
import { SvgIcon } from '@/components/SvgIcon';

export interface GiftThumbTheme {
  withImage: {
    base: string;
  };
  withNoImage: {
    base: string;
    content: string;
  };
}

export interface GiftThumbProps extends Omit<ImageProps, 'src'> {
  src?: string;
}

export const GiftThumb: FC<GiftThumbProps> = ({
  src,
  alt,
  width = 80,
  height = width,
  style,
  className,
  ...props
}) => {
  return (
    <>
      {src ? (
        <Image
          src={src}
          alt={alt}
          width={width}
          height={height}
          quality={100}
          className={classNames(styles.withImage.base, className)}
          style={{ width: `${width}px`, height: `${height}px`, ...style }}
          {...props}
        />
      ) : (
        <div
          className={classNames(styles.withNoImage.base, className)}
          style={{ width: `${width}px`, height: `${height}px`, ...style }}
        >
          <SvgIcon name="ImageXSmall" />
          <p className={styles.withNoImage.content}>
            no image
            <br />
            画像準備中
          </p>
        </div>
      )}
    </>
  );
};
