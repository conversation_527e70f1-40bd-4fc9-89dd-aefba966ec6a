{
  "parserOptions": {
    "ecmaVersion": 2022,
    "sourceType": "module",
    "project": ["./tsconfig.json"],
    "ecmaFeatures": {
      "jsx": true
    }
  },
  "plugins": [
    "unused-imports",
    "@typescript-eslint",
    "react-hooks",
    "testing-library"
  ],
  "parser": "@typescript-eslint/parser",
  "extends": [
    "next/core-web-vitals",
    "airbnb-typescript",
    "prettier",
    "plugin:prettier/recommended",
    "plugin:@typescript-eslint/recommended"
  ],
  "overrides": [
    // Only uses Testing Library lint rules in test files
    {
      "files": ["**/test/**/*.[jt]s?(x)", "**/?(*.)+(spec|test).[jt]s?(x)"],
      "extends": ["plugin:testing-library/react"]
    }
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/no-unused-expressions": "off",
    "react-hooks/exhaustive-deps": "off",
    "@typescript-eslint/naming-convention": "off"
  },
  "env": {
    "es2022": true,
    "browser": true
  }
}
