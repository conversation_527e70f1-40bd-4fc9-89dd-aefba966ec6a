// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { act, renderHook } from '@testing-library/react';
import { useDisclosure } from '@/hooks/use-disclosure';

describe('hooks/use-disclosure/use-disclosure.spec.tsx', () => {
  describe('A11y', () => {
    it('should initial state correctly', () => {
      const { result } = renderHook(() => useDisclosure());
      expect(result.current[0]).toBe(false);
    });
  });

  describe('User interactions', () => {
    it('should handles close correctly', () => {
      const { result } = renderHook(() => useDisclosure(true));
      expect(result.current[0]).toBe(true);

      act(() => result.current[1].close());
      expect(result.current[0]).toBe(false);
    });

    it('should handles open correctly', () => {
      const { result } = renderHook(() => useDisclosure(false));
      expect(result.current[0]).toBe(false);

      act(() => result.current[1].open());
      expect(result.current[0]).toBe(true);
    });

    it('should handles toggle correctly', () => {
      const { result } = renderHook(() => useDisclosure(false));
      expect(result.current[0]).toBe(false);

      act(() => result.current[1].toggle());
      expect(result.current[0]).toBe(true);

      act(() => result.current[1].toggle());
      expect(result.current[0]).toBe(false);
    });

    it('should calls onClose when close is called', () => {
      const spy = jest.fn();
      const { result } = renderHook(() =>
        useDisclosure(true, { onClose: spy })
      );
      expect(spy).toHaveBeenCalledTimes(0);

      act(() => result.current[1].close());
      expect(spy).toHaveBeenCalledTimes(1);

      act(() => result.current[1].close());
      expect(spy).toHaveBeenCalledTimes(1);
    });

    it('should calls onOpen when open is called', () => {
      const spy = jest.fn();
      const { result } = renderHook(() =>
        useDisclosure(false, { onOpen: spy })
      );
      expect(spy).toHaveBeenCalledTimes(0);

      act(() => result.current[1].open());
      expect(spy).toHaveBeenCalledTimes(1);

      act(() => result.current[1].open());
      expect(spy).toHaveBeenCalledTimes(1);
    });

    it('should calls onOpen and onClose correctly when toggle is called', () => {
      const onClose = jest.fn();
      const onOpen = jest.fn();
      const { result } = renderHook(() =>
        useDisclosure(false, { onOpen, onClose })
      );
      expect(onOpen).toHaveBeenCalledTimes(0);
      expect(onClose).toHaveBeenCalledTimes(0);

      act(() => result.current[1].toggle());
      expect(onOpen).toHaveBeenCalledTimes(1);
      expect(onClose).toHaveBeenCalledTimes(0);

      act(() => result.current[1].toggle());
      expect(onOpen).toHaveBeenCalledTimes(1);
      expect(onClose).toHaveBeenCalledTimes(1);

      act(() => result.current[1].toggle());
      expect(onOpen).toHaveBeenCalledTimes(2);
      expect(onClose).toHaveBeenCalledTimes(1);
    });
  });
});
