// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React, { ComponentProps, forwardRef } from 'react';
import { useId } from '@/hooks/use-id';
import { useUncontrolled } from '@/hooks/use-uncontrolled';
import { getSafeId } from '@/utils/random';
import { Tab } from './Tab/Tab';
import { TabsList } from './TabsList/TabsList';
import { TabsPanel } from './TabsPanel/TabsPanel';
import { TabsContextProvider } from './Tabs.context';

import { TABS_ERRORS } from './Tabs.errors';

export type TabsValue = string | null;
export type TabsPosition = 'left' | 'center' | 'right' | 'between';
export type TabsVariant = 'default' | 'underline' | 'pills' | 'separate';

export interface TabsProps
  extends Omit<ComponentProps<'div'>, 'ref' | 'defaultValue'> {
  /** Base id, used to generate ids that connect labels with controls, by default generated randomly */
  id?: string;

  /** Value for controlled component */
  value?: TabsValue;

  /** Controls component visuals
   * @default underline
   */
  variant?: TabsVariant;

  /** Default value for uncontrolled component */
  defaultValue?: TabsValue;

  /** Callback for controlled component */
  onTabChange?(value: TabsValue): void;

  /** Tabs content */
  children: React.ReactNode;
}

const TabsComponent = forwardRef<HTMLDivElement, TabsProps>(
  (
    {
      id,
      value,
      defaultValue,
      variant = 'underline',
      onTabChange,
      children,
      ...others
    },
    ref
  ) => {
    const uid = useId(id);
    const [_value, onChange] = useUncontrolled<TabsValue>({
      value,
      defaultValue,
      finalValue: null,
      onChange: onTabChange,
    });

    return (
      <TabsContextProvider
        value={{
          id: uid,
          value: _value,
          variant,
          getTabId: getSafeId(`${uid}-tab`, TABS_ERRORS.value),
          getPanelId: getSafeId(`${uid}-panel`, TABS_ERRORS.value),
          onTabChange: onChange,
        }}
      >
        <div ref={ref} {...others}>
          {children}
        </div>
      </TabsContextProvider>
    );
  }
);

TabsComponent.displayName = 'Tabs';
export const Tabs = Object.assign(TabsComponent, {
  Tab: Tab,
  List: TabsList,
  Panel: TabsPanel,
});
