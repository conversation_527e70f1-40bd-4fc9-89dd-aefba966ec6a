// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import service from '..';
import {
  IIsRegisteredPayload,
  IIsRegisteredResponse,
  IUpdateCompanyIdPayload,
  IUpdateCompanyIdResponse,
  IUserInfo,
  IUserRegisterPayload,
  IUserRegisterResponse,
  IUserUpdatePayload,
  IUserUpdateResponse,
  IVerifyUserPayload,
  IVerifyUserResponse,
  IWithDrawResponse,
  IVerifyUserV2Payload,
  IVerifyUserV2Response,
  IUserLoginPayload,
  IUserLoginResponse,
} from '@/types/user';
import { ICheckinPayload, ICheckinResponse } from '@/types/checkin';
import { OptionType } from '@/components/Select2';

const basePath = '/members';
const authPath = '/auth';

const authApi = {
  getUserInfo: () => service.get<IUserInfo>(`${basePath}`),
  verifyUser: (payload: IVerifyUserPayload) =>
    service.post<IVerifyUserResponse>(`${basePath}/verify-member`, payload),
  registerUser: (payload: IUserRegisterPayload) =>
    service.post<IUserRegisterResponse>(basePath, payload),
  updateUser: (payload: IUserUpdatePayload) =>
    service.patch<IUserUpdateResponse>(basePath, payload),
  checkin: (payload: ICheckinPayload) =>
    service.post<ICheckinResponse>(`${basePath}/checkin`, payload),
  updateCompanyId: (payload: IUpdateCompanyIdPayload) =>
    service.post<IUpdateCompanyIdResponse>(
      `${basePath}/update-company-id`,
      payload
    ),
  withdrawal: () => service.post<IWithDrawResponse>(`${basePath}/withdrawal`),
  isRegistered: (payload: IIsRegisteredPayload) =>
    service.post<IIsRegisteredResponse>(
      `${basePath}/is-register-member`,
      payload
    ),
  myPartnerGroup: () => service.get<OptionType[]>(`/group/my-groups`),
  verifyUserV2: (payload: IVerifyUserV2Payload) =>
    service.post<IVerifyUserV2Response>(
      `${basePath}/verify-member-v2`,
      payload
    ),
  login: (payload: IUserLoginPayload) =>
    service.post<IUserLoginResponse>(`${authPath}/login`, payload),
};

export default authApi;
