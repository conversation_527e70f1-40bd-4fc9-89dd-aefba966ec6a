// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
export const templateString = (template: string, params: object) =>
  // eslint-disable-next-line @typescript-eslint/no-implied-eval
  new Function(
    'params',
    `return (({${Object.keys(params).join(',')}}) => \`${template}\`)(params)`
  )(params);

interface StyleClasses {
  a: string;
  childPre: string;
}

export const transformText = (str: string, styles: StyleClasses) => {
  let counter = 1;

  // Replace `+` with index numbers
  let result = str.replace(/\+\s/g, () => `${counter++}. `);

  // Replace `-` with periods
  result = result.replace(/-\s/g, '・');

  // Replace `[[text>url]]` with HTML links
  result = result.replace(/\[\[(.*?)>(.*?)\]\]/g, (match, text, url) => {
    return `<a class="${styles.a}" href="${url}" target="_blank" rel="noopener noreferrer">${text}</a>`;
  });

  // Wrap lines that contain a number followed by a period or just a period in <pre> and not allow "<a class"
  result = result.replace(
    /^((?!<a class)[・+\d][^\n]*)\n?/gm,
    (match, p1) => `<pre class="${styles.childPre}">${p1}</pre>`
  );

  return result;
};

export const transformText2 = (str: string, styles: StyleClasses) => {
  if (!str) return '';

  let counter = 1;

  function preprocessText(text: string): string {
    let insideBrackets = false;

    return text
      .split('')
      .map((char, i, arr) => {
        if (arr.slice(i, i + 2).join('') === '[[') {
          insideBrackets = true;
        }
        if (arr.slice(i - 1, i + 1).join('') === ']]') {
          insideBrackets = false;
        }

        if (
          !insideBrackets &&
          /[+\*\-]/.test(char) &&
          arr[i - 1] !== '\n' &&
          i !== 0
        ) {
          return `\n${char}`;
        }

        return char;
      })
      .join('');
  }

  function wrapSpecificLinesInPre(text: string): string {
    const lines = preprocessText(text).split('\n');
    const processedLines = lines.map(line => {
      if (/^\s*[\+\*\-]/.test(line.trim())) {
        return `<pre class="${styles.childPre}">${line.trim()}</pre>`;
      } else if (line === '') {
        return `<pre class="min-h-5 opacity-0">x</pre>`;
      } else {
        return `<pre class="min-h-5 text-justify">${line.trim()}</pre>`;
      }
    });

    return processedLines.join('\n');
  }

  const newString = wrapSpecificLinesInPre(str);

  function replaceAll(
    // eslint-disable-next-line @typescript-eslint/no-shadow
    str: string,
    regex: RegExp,
    replacer: (match: RegExpExecArray) => string
  ) {
    let match;
    while ((match = regex.exec(str)) !== null) {
      str = str.replace(match[0], replacer(match));
    }
    return str;
  }
  const handleNumberedList = () => `${counter++}. `;
  const handleDash = () => `・`;
  const handleStar = () => `※`;

  const handleLink = (match: RegExpExecArray) => {
    const [, text, url] = match;
    return `<a class="${styles.a}" href="${url}" target="_blank" rel="noopener">${text}</a>`;
  };

  const handleCenter = (match: RegExpExecArray) =>
    `<div class="text-center text-base">${match[1]}</div>`;

  const handleBold = (match: RegExpExecArray) => `<strong>${match[1]}</strong>`;

  const handleCodeBlock = (match: RegExpExecArray) => {
    const language = match[1];
    const code = match[2];
    return `<pre class="${styles.childPre}"><code class="${language}">${code}</code></pre>`;
  };

  let result = newString;

  const transformationList = [
    { regex: /\[\[center\]\](.*?)\[\[\/center\]\]/gs, value: handleCenter },
    { regex: /\[\[bold\]\](.*?)\[\[\/bold\]\]/gs, value: handleBold },
    { regex: /`(.*)\n([\s\S]*?)\n`/g, value: handleCodeBlock },
    { regex: /\+\s/g, value: handleNumberedList },
    { regex: /-\s/g, value: handleDash },
    { regex: /\*\s?/g, value: handleStar },
    { regex: /\[\[(.*?)>(.*?)\]\]/g, value: handleLink },
  ];

  transformationList.forEach(item => {
    result = replaceAll(result, item.regex, item.value);
  });

  return result;
};
