// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { BoxContent } from '@/components/BoxContent';
import { Button } from '@/components/Button';
import Loading from '@/components/Spinner/Loading';
import { transformText2 } from '@/utils/text';
import { NextSeo } from 'next-seo';
import { FC, useEffect, useRef } from 'react';
import { styles } from './components/styles';
import Image from 'next/image';
import { useUploadFile } from './hooks/useUploadFile';
import { useLiff } from '@/contexts/liff';
import ModalError from './components/ModalError';
import ImageUploadSubmitted from './components/ImageSubmitted';
import { Spinner } from '@/components/Spinner';
import ImageUploadOutOfPeriod from './components/OutOfPeriod';
import {
  BUTTON_TEXT_IMAGE_UPLOAD_REPLACE,
  IMAGE_UPLOAD_ACCEPT,
} from '@/constants/img-form';
import EditImageButtons from './components/EditImageButtons';
import { useFetch } from '@/hooks/use-fetch';
import accessLogApi from '@/services/internal/modules/access-log';
import { ISaveLogPayload } from '@/types/access-log';
import { useRouter } from 'next/router';

const ImageUploadScreen: FC = () => {
  const router = useRouter();
  const {
    query: { id },
  } = router;

  const { liff } = useLiff();
  const uploadRef = useRef<HTMLInputElement>(null);

  const {
    uploadedImages,
    handleUploadImages,
    handleDeleteImage,
    checkMaximumImageUpload,
    imageFormResponse,
    isDisabledButtonSubmit,
    onSubmit,
    textError,
    setTextError,
    isLoadingUploadForm,
    isFetchingImageForm,
    uploadResponse,
    imageFormStatus,
    isLoadingChangeImage,
  } = useUploadFile(liff);

  const { fetch: calendarLogHandler } = useFetch({
    fetcher: accessLogApi.saveLog,
    dependencies: false,
  });

  const handleCalendarLog = async (data?: ISaveLogPayload) => {
    await calendarLogHandler(data);
  };

  useEffect(() => {
    if (imageFormStatus) {
      handleCalendarLog({
        screen_type: 'image-form',
        screen_path:
          imageFormStatus === 'failed'
            ? `/img-form/${id}/out-of-period`
            : `/img-form/${imageFormResponse?.id}`,
      });
    }
  }, [imageFormStatus]);

  const getButtonText = () => {
    if (imageFormStatus === 'unavailable')
      return BUTTON_TEXT_IMAGE_UPLOAD_REPLACE;

    if (!imageFormResponse?.text) return '';

    if (uploadedImages.length === 0) {
      return imageFormResponse.text.button_text_image_upload?.trim() || '';
    }

    if (uploadedImages.length === imageFormResponse.image_count_max) {
      return imageFormResponse.image_count_max > 1
        ? imageFormResponse.text.button_text_image_upload_max?.trim() || ''
        : '';
    }

    if (imageFormResponse.image_count_max === 1) {
      return imageFormResponse.text.button_text_image_upload_max?.trim() || '';
    }

    return (
      imageFormResponse.text.button_text_image_upload_another?.trim() || ''
    );
  };

  const buttonText = getButtonText();

  return (
    <>
      {isFetchingImageForm ? (
        <Loading />
      ) : (
        <>
          {!uploadResponse?.image_form_id ? (
            <>
              <NextSeo title={imageFormResponse?.text?.meta_title} />

              {imageFormStatus === 'failed' &&
              imageFormResponse?.text?.text_out_of_period ? (
                <ImageUploadOutOfPeriod submmittedRes={imageFormResponse} />
              ) : (
                imageFormResponse?.id && (
                  <div className="container mx-auto">
                    <BoxContent>
                      <BoxContent.Body>
                        <div className="mt-4">
                          {imageFormResponse?.image?.top_image && (
                            <div className="pointer relative mb-6">
                              <Image
                                src={
                                  (imageFormResponse.image
                                    .top_image as string) || ''
                                }
                                alt="top-image"
                                width={0}
                                height={220}
                                className="h-auto max-h-[220px] w-full  object-contain"
                              />
                            </div>
                          )}

                          <pre
                            dangerouslySetInnerHTML={{
                              __html: transformText2(
                                (uploadedImages.length > 0
                                  ? imageFormResponse?.text?.text_img_preview
                                  : imageFormResponse?.text
                                      ?.text_description) || '',
                                styles
                              ),
                            }}
                            style={{ display: 'flex', flexDirection: 'column' }}
                            className={styles.base}
                          />
                          {uploadedImages.length === 0 &&
                            imageFormResponse?.image?.sample_image && (
                              <div className="mt-6 flex flex-col items-center justify-center">
                                <Image
                                  src={
                                    (imageFormResponse.image
                                      .sample_image as string) || ''
                                  }
                                  alt="sample-image"
                                  width={0}
                                  height={135}
                                  className="w-full h-auto max-h-[135px] object-contain"
                                />
                              </div>
                            )}

                          {uploadedImages.length > 0 &&
                            uploadedImages.map((item, index) => (
                              <div
                                key={index}
                                className="mt-6 flex flex-col items-center justify-center"
                              >
                                <Image
                                  src={item.image as string}
                                  alt={`sample-${index}`}
                                  width={0}
                                  height={135}
                                  className="w-full h-auto max-h-[135px] object-contain"
                                />
                                <EditImageButtons
                                  handleDeleteImage={handleDeleteImage}
                                  handleUploadImages={handleUploadImages}
                                  index={index}
                                  disabled={
                                    isLoadingChangeImage || isLoadingUploadForm
                                  }
                                />
                              </div>
                            ))}
                        </div>
                      </BoxContent.Body>
                      <BoxContent.Footer style={{ paddingTop: 24 }}>
                        {buttonText && (
                          <Button
                            fullSized
                            type="submit"
                            size="xl"
                            color={
                              checkMaximumImageUpload &&
                              imageFormStatus === 'success'
                                ? 'primary-no-focus'
                                : 'gray-10'
                            }
                            onClick={() =>
                              imageFormStatus === 'success' &&
                              uploadRef?.current?.click()
                            }
                            className="mb-6"
                            disabled={
                              !checkMaximumImageUpload ||
                              isLoadingChangeImage ||
                              isLoadingUploadForm ||
                              imageFormStatus === 'unavailable'
                            }
                          >
                            {buttonText}
                            <input
                              hidden
                              ref={uploadRef}
                              type="file"
                              accept={IMAGE_UPLOAD_ACCEPT}
                              onClick={e => {
                                (e.target as HTMLInputElement).value = '';
                              }}
                              onChange={e =>
                                handleUploadImages(e, uploadedImages.length)
                              }
                            />
                          </Button>
                        )}

                        {uploadedImages.length > 0 && (
                          <Button
                            fullSized
                            type="submit"
                            size="xl"
                            onClick={() => {
                              !isDisabledButtonSubmit && onSubmit();
                            }}
                            color={
                              !isDisabledButtonSubmit ? 'primary' : 'gray-10'
                            }
                            disabled={
                              isDisabledButtonSubmit || isLoadingUploadForm
                            }
                          >
                            {isLoadingUploadForm && (
                              <div className="mr-3">
                                <Spinner size="lg" color="primary" />
                              </div>
                            )}
                            {imageFormResponse?.text?.button_text_submit}
                          </Button>
                        )}
                      </BoxContent.Footer>
                    </BoxContent>
                  </div>
                )
              )}
            </>
          ) : (
            uploadResponse && (
              <>
                <NextSeo title={imageFormResponse?.text?.meta_title} />
                <ImageUploadSubmitted
                  submmittedRes={uploadResponse}
                  imagesPreview={uploadedImages}
                />
              </>
            )
          )}
          <ModalError
            text={textError}
            setIsOpenError={() => setTextError(null)}
            isOpenError={textError ? true : false}
          />
        </>
      )}
    </>
  );
};

export default ImageUploadScreen;
