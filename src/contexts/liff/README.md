We use context provider to build and extend feature for LIFF (LINE Front-end Framework).

## Example

An example of `page/index.tsx`

```javascript
import { useLiff } from '@/contexts/liff';
import { useEffect, useState } from 'react';

export default function Home() {
  const [displayName, setDisplayName] = useState('');
  const { error, isLoggedIn, isReady, liff } = useLiff();

  useEffect(() => {
    if (!isLoggedIn) return;

    (async () => {
      const profile = await liff.getProfile();
      setDisplayName(profile.displayName);
    })();
  }, [liff, isLoggedIn]);

  const login = () => {
    liff.login();
  };

  const logout = () => {
    liff.logout();
  };

  const showDisplayName = () => {
    if (error) return <p>Something is wrong.</p>;
    if (!isReady) return <p>Loading...</p>;

    if (!isLoggedIn) {
      return (
        <button className="App-button" onClick={login}>
          Login
        </button>
      );
    }
    return (
      <>
        <p>Welcome to the react-liff demo app, {displayName}!</p>
        <button className="App-button" onClick={logout}>
          Logout
        </button>
      </>
    );
  };

  return <div>{showDisplayName()}</div>;
}
```

## useLiff return values

- `error`: `unknown` (is `LiffError | undefined` in many cases)
  - Returns an error if `liff.init()` was failed.
- `isLoggedIn`: `boolean`
  - Returns whether the user is logged in.
- `isReady`: `boolean`
  - Returns `true` after `liff.init()` has successfully completed. Otherwise, returns `false`.
- `liff`: `Liff`
  - Returns liff object.
- `userInfo`: `IUserInfo`
  - Returns registered user information.
- `setUserInfo`: `Dispatch< SetStateAction<IUserInfo>>`
  - Returns function to update registered user information.
- `userLineInfo`: `IUserLineInfo`
  - Returns LINE user information.
- `setUserLineInfo`: `Dispatch< SetStateAction<IUserLineInfo>>`
  - Returns function to update LINE user information.
