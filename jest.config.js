// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
// eslint-disable-next-line @typescript-eslint/no-var-requires
const nextJest = require('next/jest');

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files in your test environment
  dir: './',
});

// Add any custom config to be passed to Jest
const customJestConfig = {
  collectCoverage: true,
  testEnvironment: 'jest-environment-jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleFileExtensions: ['ts', 'js', 'tsx', 'jsx'],
  moduleNameMapper: {
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/pages/(.*)$': '<rootDir>/src/pages/$1',
    '^@/hooks/(.*)$': '<rootDir>/src/hooks/$1',
    '^@/constants/(.*)$': '<rootDir>/src/constants/$1',
    '^@/contexts/(.*)$': '<rootDir>/src/contexts/$1',
    '^@/services/(.*)$': '<rootDir>/src/services/$1',
    '^@/utils/(.*)$': '<rootDir>/src/utils/$1',
    '^@/layout/(.*)$': '<rootDir>/src/layout/$1',
    '^@/assets/(.*)$': '<rootDir>/src/assets/$1',
    '^@/mocks/(.*)$': '<rootDir>/src/mocks/$1',
    '^@/test/(.*)$': '<rootDir>/src/test/$1',
  },
  collectCoverageFrom: [
    '<rootDir>/src/components/**/*.tsx',
    '<rootDir>/src/pages/**/*.tsx',
    '<rootDir>/src/layout/**/*.tsx',
    '<rootDir>/src/contexts/**/*.tsx',
    '<rootDir>/src/hooks/**/*.ts',
  ],
  coveragePathIgnorePatterns: [
    'node_modules',
    '.module.ts',
    '.d.ts',
    '.style.ts',
    '.stories.tsx',
    '.mock.ts',
  ],
  transform: {
    '^.+\\.(svg)$': `jest-transformer-svg`,
  },
};

const getCustomConfig = async () => {
  /**
   * Delete next js module name mapper transform and use above svg
   * transformer to avoid errors with svg and styled components
   */
  const config = await createJestConfig(customJestConfig)();
  delete config.moduleNameMapper['^.+\\.(svg)$'];

  return config;
};

/** createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async */
module.exports = getCustomConfig();
