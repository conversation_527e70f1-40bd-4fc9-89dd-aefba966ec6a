// GKC: 01JPKNE8A7Z8AFWC717C0GBAA5
import localeAr from '../i18n/ar';
import localeBg from '../i18n/bg';
import localeCa from '../i18n/ca';
import localeCs from '../i18n/cs';
import localeDa from '../i18n/da';
import localeDe from '../i18n/de';
import localeEl from '../i18n/el';
import localeEnGB from '../i18n/en-GB';
import localeEs from '../i18n/es';
import localeFa from '../i18n/fa';
import localeFi from '../i18n/fi';
import localeFr from '../i18n/fr';
import localeHe from '../i18n/he';
import localeHi from '../i18n/hi';
import localeHr from '../i18n/hr';
import localeHu from '../i18n/hu';
import localeIt from '../i18n/it';
import localeJa from '../i18n/ja';
import localeKo from '../i18n/ko';
import localeLt from '../i18n/lt';
import localeNl from '../i18n/nl';
import localeNo from '../i18n/no';
import localePl from '../i18n/pl';
import localePtBR from '../i18n/pt-BR';
import localePtPT from '../i18n/pt-PT';
import localeRo from '../i18n/ro';
import localeRu from '../i18n/ru';
import localeRuUA from '../i18n/ru-UA';
import localeSk from '../i18n/sk';
import localeSr from '../i18n/sr';
import localeSv from '../i18n/sv';
import localeTh from '../i18n/th';
import localeTr from '../i18n/tr';
import localeUa from '../i18n/ua';
import localeVi from '../i18n/vi';
import localeZh from '../i18n/zh';
import { MbscLocale } from '../i18n/locale';
export * from '../i18n/locale';
declare const localeEn: MbscLocale;
declare const locale: {
    [key: string]: MbscLocale;
};
export { locale, localeAr, localeBg, localeCa, localeCs, localeDa, localeDe, localeEl, localeEn, localeEnGB, localeEs, localeFa, localeFi, localeFr, localeHe, localeHi, localeHr, localeHu, localeIt, localeJa, localeKo, localeLt, localeNl, localeNo, localePl, localePtBR, localePtPT, localeRo, localeRu, localeRuUA, localeSk, localeSr, localeSv, localeTh, localeTr, localeUa, localeVi, localeZh, };
export * from '../i18n/hijri';
export * from '../i18n/jalali';
