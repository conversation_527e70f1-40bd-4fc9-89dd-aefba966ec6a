// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
export enum PointType {
  CHECKIN = 'checkin',
  LINK_TAPPED = 'link_tapped',
  LAUNCH_APP = 'launch_app',
  QUESTIONNAIRE = 'questionnaire',
  COMPENSATION = 'compensation',
  DEDUCTION = 'deduction',
  MESSAGE_POINT = 'message-point',
  WELCOME = 'welcome',
  GIFT_EXCHANGE = 'gift-exchange',
  EXPIRE = 'expire',
}

export enum MinusPointType {
  DEDUCTION = 'deduction',
  GIFT_EXCHANGE = 'gift-exchange',
  EXPIRE = 'expire',
}

export enum EStatusChecking {
  first_stamp = 'first_stamp',
  time_locked = 'time_locked',
  with_only_stamp = 'with_only_stamp',
  all_collected = 'all_collected',
}

export enum EBrowserType {
  liff = 'liff',
  external = 'external',
}
