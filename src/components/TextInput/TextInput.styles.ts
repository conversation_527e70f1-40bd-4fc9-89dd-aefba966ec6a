// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { ITextInputTheme } from './TextInput';

export const styles: ITextInputTheme = {
  base: 'flex',
  field: {
    base: 'relative w-full',
    icon: {
      base: 'pointer-events-none absolute inset-y-0 left-0 flex items-center pl-4',
      svg: 'h-5 w-5 text-gray-500',
    },
    rightIcon: {
      base: 'pointer-events-none absolute inset-y-0 right-0 flex items-center pr-4',
      svg: 'h-5 w-5 text-gray-500',
    },
    input: {
      base: 'block w-full border rounded disabled:cursor-not-allowed disabled:opacity-50',
      sizes: {
        xs: 'p-2 text-xs',
        sm: 'p-2.5 text-sm',
        md: 'px-4 py-3 text-sm h-[44px]',
        lg: 'p-5 text-base',
      },
      colors: {
        default:
          'bg-white border-gray-10 text-theme-main placeholder-gray-50 focus:border-gray-20 focus:ring-gray-20',
        primary:
          'bg-theme-primary/10 border-theme-primary text-theme-main placeholder-theme-primary focus:border-theme-primary/90 focus:ring-theme-primary/90',
        secondary:
          'bg-theme-secondary/10 border-theme-secondary text-theme-main placeholder-theme-secondary focus:border-theme-secondary/90 focus:ring-theme-secondary/90',
        info: 'bg-theme-info-secondary border-theme-info-primary text-theme-main placeholder-theme-info-primary focus:border-theme-info-primary/90 focus:ring-theme-info-primary/90',
        failure:
          'bg-theme-error-secondary border-theme-error-primary text-theme-main placeholder-theme-error-primary focus:border-theme-error-primary/90 focus:ring-theme-error-primary/90',
        warning:
          'bg-theme-warning-secondary border-theme-warning-primary text-theme-main placeholder-theme-warning-primary focus:border-theme-warning-primary/90 focus:ring-theme-warning-primary/90',
        success:
          'bg-theme-success-secondary border-theme-success-primary text-theme-main placeholder-theme-success-primary focus:border-theme-success-primary/90 focus:ring-theme-success-primary/90',
      },
      withRightIcon: {
        on: 'pr-11',
        off: '',
      },
      withIcon: {
        on: 'pl-11',
        off: '',
      },
      withShadow: {
        on: 'shadow-sm',
        off: '',
      },
    },
    interactableIcon:
      'cursor-pointer absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5',
  },
};
