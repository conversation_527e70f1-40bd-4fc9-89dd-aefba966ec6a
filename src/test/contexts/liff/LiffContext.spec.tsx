// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-var-requires */
import { default as liff } from '@line/liff';
import { render, screen, waitFor } from '@testing-library/react';
import { IAppConfig, MetaSeoDetail } from '@/types/app';
import { LiffProvider, useLiff } from '@/contexts/liff';
import authApi from '@/services/internal/modules/auth';
import { USER_INFO } from '@/mocks/users';
import {
  LINE_GET_ACCESS_TOKEN_SUCCESS,
  LINE_GET_CONTEXT,
  LINE_GET_FRIENDSHIP_FALSE,
  LINE_GET_FRIENDSHIP_TRUE,
  LINE_GET_PROFILE,
  LINE_IS_LOGGED_IN_TRUE,
  LINE_PERMISSION_MESSAGE_GRANTED,
  LINE_PERMISSION_MESSAGE_PROMPT,
} from '@/mocks/line';

jest.mock('next/router', () => require('next-router-mock'));
const useRouter = jest.spyOn(require('next/router'), 'useRouter');
const useMockRouter = (query: Record<string, string | string[]> = {}) => {
  return useRouter.mockReturnValue({
    query,
    pathname: '/',
    push: jest.fn(),
  });
};

jest.mock('@line/liff', () => ({
  __esModule: true,
  default: { init: jest.fn(), logout: jest.fn() },
}));

interface UseMockLiffProps {
  init?: any;
  isLoggedIn?: any;
  getFriendship?: any;
  getContext?: any;
  getProfile?: any;
  permission?: any;
  getAccessToken?: any;
}

const useMockLiff = (props: UseMockLiffProps = {}) => {
  const {
    init,
    isLoggedIn,
    getFriendship,
    getContext,
    getProfile,
    getAccessToken,
    permission,
  } = props;

  liff.init = init || jest.fn();
  liff.isLoggedIn =
    isLoggedIn || jest.fn().mockReturnValue(LINE_IS_LOGGED_IN_TRUE);
  liff.getFriendship =
    getFriendship || jest.fn().mockReturnValue(LINE_GET_FRIENDSHIP_TRUE);
  liff.getContext = getContext || jest.fn().mockReturnValue(LINE_GET_CONTEXT);
  liff.getProfile = getProfile || jest.fn().mockReturnValue(LINE_GET_PROFILE);
  liff.getAccessToken =
    getAccessToken || jest.fn().mockReturnValue(LINE_GET_ACCESS_TOKEN_SUCCESS);
  liff.permission = permission || {
    requestAll: jest.fn(),
    query: jest.fn().mockReturnValue(LINE_PERMISSION_MESSAGE_GRANTED),
  };
};

const TestLiffContext = (): JSX.Element => {
  const appConfig: IAppConfig = {
    liffId: 'lorem-123',
    companyId: 'ipsum-456',
  };

  const metaSeo: MetaSeoDetail = {
    title: 'This is title',
    description: 'This is description',
  };

  const LiffConsumer = () => {
    const { error, isReady, liff: liffInit } = useLiff();

    return (
      <>
        <p data-testid="isReady">{isReady.toString()}</p>
        <p data-testid="error.message">
          {error instanceof Error && error.message}
        </p>
        <p data-testid="liff.id">{liffInit?.id}</p>
      </>
    );
  };

  return (
    <LiffProvider appConfig={appConfig} meta={metaSeo}>
      <LiffConsumer />
    </LiffProvider>
  );
};

describe('contexts/liff/LiffContext.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<TestLiffContext />);

      await waitFor(() => {
        expect(screen.getByRole('status')).toBeInTheDocument();
      });
    });
  });

  describe('Liff', () => {
    beforeEach(() => {
      useMockRouter({ redirectTo: '' });
    });

    it('should `liff.init()` succeeds', async () => {
      useMockLiff();

      render(<TestLiffContext />);

      await waitFor(() => {
        expect(liff.init).toHaveBeenCalledTimes(1);
      });
    });

    it('should `liff.isLoggedIn()` return false', async () => {
      useMockLiff({
        isLoggedIn: jest.fn().mockReturnValue(false),
      });

      render(<TestLiffContext />);

      await waitFor(() => {
        expect(liff.isLoggedIn).toHaveBeenCalledTimes(2);
      });
    });

    it('should `liff.getProfile()` return error', async () => {
      useMockLiff({
        getProfile: jest.fn().mockRejectedValue({ code: 400 }),
      });

      render(<TestLiffContext />);

      await waitFor(() => {
        expect(liff.getProfile).toHaveBeenCalledTimes(1);
      });
    });

    it('should reject add friend line OA', async () => {
      useMockLiff({
        getFriendship: jest.fn().mockReturnValue(LINE_GET_FRIENDSHIP_FALSE),
      });

      jest.spyOn(authApi, 'isRegistered').mockResolvedValue({
        isRegister: false,
      });

      render(<TestLiffContext />);

      await waitFor(() => {
        expect(liff.getFriendship).toHaveBeenCalledTimes(1);
      });
    });

    it('should reject permission send message line OA', async () => {
      useMockLiff({
        permission: {
          query: jest.fn().mockReturnValue(LINE_PERMISSION_MESSAGE_PROMPT),
        },
      });

      render(<TestLiffContext />);

      await waitFor(() => {
        expect(liff.permission.query).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('Fetching', () => {
    beforeEach(() => {
      useMockRouter({ redirectTo: '', company_id: 'C0001' });
      useMockLiff();
    });

    describe('Fetching verifyUser return isRegister=false', () => {
      beforeEach(() => {
        jest.spyOn(authApi, 'verifyUser').mockResolvedValue({
          isRegister: false,
          accessToken: 'lorem',
        });
      });

      it('should call api verifyUser success', async () => {
        jest
          .spyOn(authApi, 'updateCompanyId')
          .mockResolvedValue({ isSuccess: true });

        render(<TestLiffContext />);

        await waitFor(() => {
          expect(authApi.verifyUser).toHaveBeenCalledTimes(1);
        });
      });

      it('should call api updateCompanyId failure', async () => {
        jest
          .spyOn(authApi, 'updateCompanyId')
          .mockRejectedValue({ message: 'Failure' });

        render(<TestLiffContext />);

        await waitFor(() => {
          expect(authApi.updateCompanyId).toHaveBeenCalledTimes(1);
        });
      });
    });

    describe('Fetching verifyUser return isRegister=true', () => {
      beforeEach(() => {
        jest.spyOn(authApi, 'verifyUser').mockResolvedValue({
          isRegister: true,
          accessToken: 'lorem',
        });
        jest
          .spyOn(authApi, 'updateCompanyId')
          .mockResolvedValue({ isSuccess: true });
      });

      it('should call api getUserInfo success', async () => {
        jest.spyOn(authApi, 'getUserInfo').mockResolvedValue(USER_INFO);

        render(<TestLiffContext />);

        await waitFor(() => {
          expect(authApi.getUserInfo).toHaveBeenCalledTimes(1);
        });
      });

      it('should call api getUserInfo success return without email', async () => {
        jest
          .spyOn(authApi, 'getUserInfo')
          .mockResolvedValue({ ...USER_INFO, email: '' });

        render(<TestLiffContext />);

        await waitFor(() => {
          expect(authApi.getUserInfo).toHaveBeenCalledTimes(1);
        });
      });
    });
  });
});
