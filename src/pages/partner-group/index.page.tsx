/* eslint-disable react/no-unescaped-entities */
// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC, useEffect, useState } from 'react';
import { Logo } from '@/components/Logo';
import { Button } from '@/components/Button';
import { BoxContent } from '@/components/BoxContent';
import { useFetch } from '@/hooks/use-fetch';
import { redirectToLineChatRoomOA } from '@/utils/redirect';
import { useLiff } from '@/contexts/liff';
import GroupConfirm from './components/GroupConfirm';
import partnerGroupApi from '@/services/internal/modules/partner-group';
import {
  IRegisteredPartnerGroupPayload,
  IRegisteredPartnerGroupResponse,
} from '@/types/partner-group';
import { routers } from '@/constants/routes';
import { useRouter } from 'next/router';
import { toast } from 'react-toastify';
import Loading from '@/components/Spinner/Loading';
import GroupActivated from './components/GroupActivated';
import { destroyCookieApp, parseCookiesApp } from '@/utils/cookie';
import { COMPANY_ID } from '@/constants/user';

const JoinGroupScreen: FC = () => {
  const router = useRouter();
  const { liff } = useLiff();
  const { company_id } = router.query;
  const [isSubmitted, setIsSubmitted] = useState<boolean>(false);
  const { company_id: companyId } = parseCookiesApp();

  // replace url on browser when user access this page
  useEffect(() => {
    router.replace(
      `${routers.joinPartnerGroup}?company_id=${company_id || companyId}`,
      undefined,
      { shallow: true }
    );
  }, [company_id || companyId]);

  const { data: registerableGroup, isFetching } =
    useFetch<IRegisteredPartnerGroupResponse>({
      fetcher: () =>
        partnerGroupApi.getRegiserableGroup({
          company_id: (company_id || companyId) as string,
        }),
      handleResponse: (res: IRegisteredPartnerGroupResponse) => {
        destroyCookieApp(null, COMPANY_ID);
        if (!res.registrable) {
          router.replace(routers.notFound);
        }
        return res;
      },
      handleError: () => {
        destroyCookieApp(null, COMPANY_ID);
        router.replace(routers.notFound);
      },
    });

  const { fetch: joinPartnerGroup } = useFetch({
    fetcher: partnerGroupApi.joinPartnerGroup,
    dependencies: false,
  });

  const onJoinPartnerGroup = async () => {
    if (!company_id && !companyId) {
      router.push(routers.notFound);
      return;
    }

    const data: IRegisteredPartnerGroupPayload = {
      company_id: (company_id || companyId) as string,
    };

    try {
      await joinPartnerGroup(data);
      setIsSubmitted(true);
    } catch (error) {
      toast.error('have error when join partner group');
    }
  };

  return (
    <>
      {isFetching || !registerableGroup.registrable ? (
        <Loading />
      ) : (
        <div className="w-full mx-auto bg-partner-group bg-no-repeat bg-[length:150%] bg-top">
          <BoxContent>
            <BoxContent.Body>
              <div className="pt-[120px] w-full pl-10 pr-10 mx-auto pb-10">
                <Logo box />
                {isSubmitted ? (
                  <GroupActivated
                    partnerGroup={registerableGroup.registrable_groups || []}
                    companyName={registerableGroup.company_name}
                    className="mt-12 -mr-[16px]"
                  />
                ) : (
                  <GroupConfirm
                    partnerGroup={registerableGroup.registrable_groups || []}
                    companyName={registerableGroup.company_name}
                    className="mt-12 -mr-[16px]"
                  />
                )}
              </div>
            </BoxContent.Body>
            <BoxContent.Footer className="!px-4 !py-4 !pt-6 bg-white">
              {isSubmitted ? (
                <Button
                  fullSized
                  type="submit"
                  size="xl"
                  color="primary"
                  outline
                  onClick={() => redirectToLineChatRoomOA(liff)}
                >
                  トーク画面に戻る
                </Button>
              ) : (
                <div className="space-y-5">
                  <Button
                    fullSized
                    type="submit"
                    size="xl"
                    color="primary"
                    onClick={onJoinPartnerGroup}
                  >
                    登録する
                  </Button>
                  <Button
                    fullSized
                    type="submit"
                    size="xl"
                    color="primary"
                    outline
                    onClick={() => redirectToLineChatRoomOA(liff)}
                  >
                    登録せずに、トーク画面に戻る
                  </Button>
                </div>
              )}
            </BoxContent.Footer>
          </BoxContent>
        </div>
      )}
    </>
  );
};

export default JoinGroupScreen;
