// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import type { Meta, Story } from '@storybook/react';
import type { IButtonProps } from './Button';
import { Button } from './Button';
import { styles } from './Button.styles';

export default {
  title: 'Components/Button',
  component: Button,
  argTypes: {
    color: {
      options: Object.keys(styles.color),
      control: { type: 'radio' },
    },
    size: {
      options: Object.keys(styles.size),
      control: { type: 'radio' },
    },
  },
} as Meta;

const Template: Story<IButtonProps> = args => <Button {...args} />;

export const DefaultButton = Template.bind({});
DefaultButton.storyName = 'Button';
DefaultButton.args = {
  children: 'Button',
};
