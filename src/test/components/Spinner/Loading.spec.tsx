// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen } from '@testing-library/react';
import Loading from '@/components/Spinner/Loading';

describe('components/Spinner/Loading.spec.tsx', () => {
  it('should have `role="status"` by default', () => {
    render(<Loading aria-label="My loading" />);
    const loading = screen.getByRole('status');

    expect(loading).toHaveAccessibleName('My loading');
  });

  it('should be able to set no `role`', () => {
    render(<Loading aria-label="My loading" role={undefined} />);
    const loading = screen.getByLabelText('My loading');

    expect(loading).not.toHaveAttribute('role');
  });

  it('should use `color` classes', () => {
    const { container } = render(<Loading color="primary" />);
    // eslint-disable-next-line testing-library/no-container, testing-library/no-node-access
    const svg = container.querySelector('svg');

    expect(svg?.classList.contains('text-gray-200')).toBe(true);
  });
});
