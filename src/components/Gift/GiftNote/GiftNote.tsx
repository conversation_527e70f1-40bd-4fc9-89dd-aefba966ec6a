// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { twMerge } from 'tailwind-merge';
import React, { ComponentProps, FC } from 'react';
import { styles } from './GiftNote.styles';

export interface GiftNoteTheme {
  base: string;
}

export type GiftNoteProps = ComponentProps<'div'>;

export const GiftNote: FC<GiftNoteProps> = ({
  className,
  children,
  ...props
}) => {
  return (
    <div className={twMerge(styles.base, className)} {...props}>
      {children}
    </div>
  );
};
