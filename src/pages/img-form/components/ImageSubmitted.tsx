// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { BoxContent } from '@/components/BoxContent';
import { Button } from '@/components/Button';
import { useLiff } from '@/contexts/liff';
import { IFileUpload, IUploadImageFormResponse } from '@/types/img-form';
import { redirectToLineChatRoomOAWithCheckUtou } from '@/utils/redirect';
import { transformText2 } from '@/utils/text';
import Image from 'next/image';
import { styles } from '../components/styles';

interface ImageUploadSubmittedProps {
  submmittedRes: IUploadImageFormResponse;
  imagesPreview: IFileUpload[];
}
const ImageUploadSubmitted: React.FC<ImageUploadSubmittedProps> = ({
  submmittedRes,
  imagesPreview,
}) => {
  const { liff } = useLiff();

  return (
    <>
      <div className="container mx-auto">
        <BoxContent>
          <BoxContent.Body>
            <div className="mt-4">
              <pre
                dangerouslySetInnerHTML={{
                  __html: transformText2(submmittedRes.text_submitted, styles),
                }}
                className={styles.base}
                style={{ display: 'flex', flexDirection: 'column' }}
              />
              {imagesPreview.map((item, index) => (
                <div
                  key={index}
                  className="mt-6 flex flex-col items-center justify-center"
                >
                  <Image
                    src={item.image as string}
                    alt={`uploaded-image-${index}`}
                    width={0}
                    height={135}
                    className="w-full h-auto max-h-[135px] object-contain"
                  />
                </div>
              ))}
            </div>
          </BoxContent.Body>
          <BoxContent.Footer>
            <Button
              fullSized
              type="submit"
              size="xl"
              color="primary"
              onClick={() => redirectToLineChatRoomOAWithCheckUtou(liff)}
            >
              トーク画面に戻る
            </Button>
          </BoxContent.Footer>
        </BoxContent>
      </div>
    </>
  );
};

export default ImageUploadSubmitted;
