// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { useLiff } from '@/contexts/liff';
import { Logo } from '@/components/Logo';
import { Button } from '@/components/Button';
import { BoxContent } from '@/components/BoxContent';
import { redirectToLineChatRoomOA } from '@/utils/redirect';

const NotFoundScreen: FC = () => {
  const { liff } = useLiff();

  return (
    <>
      <div className="container mx-auto">
        <BoxContent>
          <BoxContent.Body className="pt-[160px]">
            <Logo box className="mb-10" />
            <div className="text-center">
              <h3 className="text-base font-bold">
                ページがないか、ネットワークが
                <br />
                つながっていないため、ページが
                <br />
                開けません。
              </h3>
            </div>
          </BoxContent.Body>
          <BoxContent.Footer>
            <Button
              fullSized
              type="submit"
              size="xl"
              color="primary"
              onClick={() => redirectToLineChatRoomOA(liff)}
            >
              トーク画面へ戻る
            </Button>
          </BoxContent.Footer>
        </BoxContent>
      </div>
    </>
  );
};

export default NotFoundScreen;
