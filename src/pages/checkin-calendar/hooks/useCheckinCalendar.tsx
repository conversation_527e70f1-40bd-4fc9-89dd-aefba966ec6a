// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { MbscDatepickerPageChangeEvent } from '@/lib/mobiscroll/react/esm5/mobiscroll.react.min';
import checkinCalendarApi from '@/services/internal/modules/checkin-calendar';
import {
  IActivity,
  ICalendarData,
  TypeSwipeDirections,
} from '@/types/checkin-calendar';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';

const defaultData: ICalendarData = {
  registeredDate: '',
  activities: [],
  stamp_count: [],
};

export const useCheckinCalendar = (companyId: string) => {
  const current = dayjs().format('YYYY/MM');
  const [queryDate, setQueryDate] = useState(current);
  const [queryDateInitMonth, setQueryDateInitMonth] = useState(current);
  const [activeStartDate, setActiveStartDate] = useState(new Date());
  const [swipeDirection, setSwipeDirection] =
    useState<TypeSwipeDirections>(null);
  const [currentMonthData, setCurrentMonthData] =
    useState<ICalendarData>(defaultData);
  const [recall, setRecall] = useState<boolean>();
  const [specifyMonthReCall, setSpecifyMonthReCall] = useState('');

  const createInitMonth = (date: string) => {
    return [
      dayjs(date).subtract(2, 'month').format('YYYY/MM'),
      dayjs(date).subtract(1, 'month').format('YYYY/MM'),
      date,
      dayjs(date).add(1, 'month').format('YYYY/MM'),
      dayjs(date).add(2, 'month').format('YYYY/MM'),
    ];
  };

  const [isResetMonth, setIsResetMonth] = useState<boolean>(false);
  const [intiMonthCall, setIntiMonthCall] = useState<string[]>(
    createInitMonth(current)
  );
  const [monthCall, setMonthCall] = useState<string[]>(intiMonthCall);

  useEffect(() => {
    const fetchDataForMonths = async () => {
      const newMonth = monthCall.find(
        (item, index) => item !== intiMonthCall[index]
      );
      let isCallApi = true;

      if (
        (currentMonthData.registeredDate &&
          dayjs(newMonth).format('YYYY/MM') <
            dayjs(currentMonthData.registeredDate).format('YYYY/MM')) ||
        dayjs(newMonth).format('YYYY/MM') >
          dayjs().add(1, 'year').format('YYYY/MM')
      ) {
        if (!specifyMonthReCall) {
          isCallApi = false;
        }
      }

      if (isCallApi)
        try {
          if (newMonth && newMonth > intiMonthCall[intiMonthCall.length - 1]) {
            setIntiMonthCall([...intiMonthCall, newMonth]);
          } else if (newMonth && newMonth < intiMonthCall[0]) {
            setIntiMonthCall([newMonth, ...intiMonthCall]);
          }

          let responses;

          if (specifyMonthReCall) {
            responses = await Promise.all([
              checkinCalendarApi.getCheckinCalendar({
                yearMonth: specifyMonthReCall as string,
                companyId: companyId as string,
              }),
            ]);
          } else if (newMonth) {
            responses = await Promise.all([
              checkinCalendarApi.getCheckinCalendar({
                yearMonth: newMonth as string,
                companyId: companyId as string,
              }),
            ]);
          } else {
            responses = await Promise.all(
              monthCall.map(month =>
                checkinCalendarApi.getCheckinCalendar({
                  yearMonth: month as string,
                  companyId: companyId as string,
                })
              )
            );
          }

          const updatedData = responses.reduce(
            (acc, currentItems) => {
              const result = Object.entries(currentItems.data).map(
                ([date, { stamps, agencyName, memo }]) => {
                  return {
                    date,
                    stamps: stamps || 0,
                    agencyName: agencyName,
                    memo: memo,
                  };
                }
              ) as IActivity[];

              const validDate =
                result?.[0]?.date && result?.[0]?.date !== 'Invalid date'
                  ? result?.[0]?.date
                  : result?.[1]?.date;

              const formattedDate = validDate
                ? dayjs(validDate).format('YYYY/MM')
                : null;

              return {
                registeredDate: currentItems.registeredDate,
                activities: [...acc.activities, ...result],
                stamp_count: formattedDate
                  ? [
                      ...acc.stamp_count,
                      { date: formattedDate, count: currentItems.stamp_count },
                    ]
                  : acc.stamp_count,
              };
            },
            {
              registeredDate: '',
              activities: [],
              stamp_count: [],
            } as ICalendarData
          );

          setCurrentMonthData(prevData => {
            const mergedActivities = [
              ...prevData.activities.filter(
                activity =>
                  dayjs(activity.date).format('YYYY/MM') !== specifyMonthReCall
              ),
              ...updatedData.activities,
            ];

            const mergedStampCount = [
              ...prevData.stamp_count.filter(
                stamp =>
                  dayjs(stamp.date).format('YYYY/MM') !== specifyMonthReCall
              ),
              ...updatedData.stamp_count,
            ];

            return {
              registeredDate:
                prevData.registeredDate || updatedData.registeredDate,
              activities: mergedActivities,
              stamp_count: mergedStampCount,
            };
          });
        } catch (error) {
          console.error('Error fetching data:', error);
        } finally {
          setIsResetMonth(false);
          setSpecifyMonthReCall('');
        }
    };

    fetchDataForMonths();
  }, [monthCall, recall]);

  const handleMonth = (year: number, month: number) => {
    const query = `${year}/${month.toString().padStart(2, '0')}`;
    const newDate = new Date(activeStartDate);
    newDate.setFullYear(year, month - 1);
    setQueryDate(query);
    setActiveStartDate(newDate);
  };

  const handleSelectPicker = (year: number, month: number) => {
    handleMonth(year, month);
    setSwipeDirection(null);
  };

  const onMonthChange = (event: MbscDatepickerPageChangeEvent) => {
    const { month } = event;
    const YYYY = dayjs(month).format('YYYY');
    const MM = dayjs(month).format('MM');

    if (
      dayjs(month).format('YYYY/MM') < dayjs(activeStartDate).format('YYYY/MM')
    ) {
      setSwipeDirection('right');
    } else {
      setSwipeDirection('left');
    }

    if (
      dayjs(month).format('YYYY/MM') <= dayjs(monthCall[1]).format('YYYY/MM') &&
      dayjs(monthCall[0]).format('YYYY/MM') >
        dayjs(currentMonthData.registeredDate).format('YYYY/MM')
    ) {
      setMonthCall([
        dayjs(monthCall[1] as string)
          .subtract(2, 'month')
          .format('YYYY/MM'),
        ...monthCall,
      ]);
    } else if (
      dayjs(month).format('YYYY/MM') >=
      dayjs(monthCall[monthCall.length - 2]).format('YYYY/MM')
    ) {
      setMonthCall([
        ...monthCall,
        dayjs(monthCall[monthCall.length - 2] as string)
          .add(2, 'month')
          .format('YYYY/MM'),
      ]);
    }

    handleMonth(Number(YYYY), Number(MM));
  };

  return {
    queryDate,
    swipeDirection,
    currentMonthData,
    isResetMonth,
    setMonthCall,
    setQueryDateInitMonth,
    setIsResetMonth,
    handleSelectPicker,
    onMonthChange,
    createInitMonth,
    handleMonth,
    setCurrentMonthData,
    defaultData,
    queryDateInitMonth,
    setIntiMonthCall,
    monthCall,
    setRecall,
    setSpecifyMonthReCall,
  };
};
