// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React, { FC } from 'react';
import { UserCurrentPointProps } from './UserCurrentPoint';
import { CurrentPoint, CurrentPointLinkProps } from '@/components/CurrentPoint';

export interface UserCurrentPointMineProps extends UserCurrentPointProps {
  points: number;
}

export const UserCurrentPointMine: FC<UserCurrentPointMineProps> = ({
  layout,
  points,
  label = {},
  action = {},
  link = {},
  note = {},
  ...props
}) => {
  const { children: childrenLabel = '保有ポイント', ...labelProps } = label;
  const {
    size: sizeAction = 'xl',
    className: classNameAction = 'w-[216px] rounded-3xl',
    children: childrenAction = 'ポイント履歴はこちら',
    ...actionProps
  } = action;
  const { children: childrenLink = 'ポイント獲得一覧', ...linkProps } =
    link as CurrentPointLinkProps;

  const {
    children: childrenNote = (
      <>
        <span className="font-bold">300pt以上</span>
        でギフトと交換ができます
      </>
    ),
    ...noteProps
  } = note;

  return (
    <CurrentPoint {...props}>
      {layout === 'short' ? (
        <CurrentPoint.Label point={points} {...labelProps}>
          {childrenLabel}
        </CurrentPoint.Label>
      ) : layout === 'medium' ? (
        <>
          <CurrentPoint.Label point={points} {...labelProps}>
            {childrenLabel}
          </CurrentPoint.Label>

          <CurrentPoint.Action
            size={sizeAction}
            className={classNameAction}
            {...actionProps}
          >
            {childrenAction}
          </CurrentPoint.Action>

          <CurrentPoint.Note {...noteProps}>{childrenNote}</CurrentPoint.Note>
        </>
      ) : (
        <>
          <CurrentPoint.Label point={points} {...labelProps}>
            {childrenLabel}
          </CurrentPoint.Label>

          <CurrentPoint.Note {...noteProps}>{childrenNote}</CurrentPoint.Note>

          <CurrentPoint.Link {...linkProps}>{childrenLink}</CurrentPoint.Link>
        </>
      )}
    </CurrentPoint>
  );
};
