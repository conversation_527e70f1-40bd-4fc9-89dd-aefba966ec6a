# gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
name: 'DEV: Deploy web app to Azure Container Web Apps'

# When this action will be executed
on:
  # Automatically trigger it when detected changes in repo
  push:
    branches: [develop]
    paths:
      - 'src/**'
      - 'Dockerfile'
      - 'package.json'
      - '.github/workflows/*'

  # Allow manually trigger
  workflow_dispatch:

permissions:
  issues: write
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    name: Build and Deploy
    environment: develop

    steps:
      - name: Checkout to the branch
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Log in to container registry
        uses: docker/login-action@v2
        with:
          registry: wellnessfedev.azurecr.io
          username: ${{ secrets.TASKSMANAGER_REGISTRY_USERNAME }}
          password: ${{ secrets.TASKSMANAGER_REGISTRY_PASSWORD }}

      - name: Build and push container image to registry
        uses: docker/build-push-action@v4
        with:
          push: true
          tags: wellnessfedev.azurecr.io/wellness-fe-dev:${{ github.sha }} , wellnessfedev.azurecr.io/wellness-fe-dev:latest
          file: Dockerfile
          context: ./.
          build-args: |
            APP_ENV=${{secrets.APP_ENV}}
            CLIENT_BASE_URL=${{secrets.CLIENT_BASE_URL}}
            HASH_SECRET_KEY=${{secrets.HASH_SECRET_KEY}}
            LIFF_ID=${{secrets.LIFF_ID}}
            LIFF_BASE_URL=${{secrets.LIFF_BASE_URL}}
            LINE_OA_URL=${{secrets.LINE_OA_URL}}
            LINE_LOGIN_URL=${{secrets.LINE_LOGIN_URL}}
            LINE_BASE_URL=${{secrets.LINE_BASE_URL}}
            INTERNAL_BASE_URL=${{secrets.INTERNAL_BASE_URL}}
            INTERNAL_BASE_API=${{secrets.INTERNAL_BASE_API}}
            INTERNAL_WEB_BASE_URL=${{secrets.INTERNAL_WEB_BASE_URL}}
            GTM_KEY=${{secrets.GTM_KEY}}
            APPINSIGHTS_CONNECTION_STRING=${{secrets.APPINSIGHTS_CONNECTION_STRING}}

  deploy:
    runs-on: ubuntu-latest
    needs: build
    name: Close Pull Request Job

    steps:
    - name: Azure Login
      uses: azure/login@v1
      with:
        creds: ${{ secrets.TASKSMANAGER_AZURE_CREDENTIALS }}

    - name: Deploy to containerapp
      uses: azure/CLI@v1
      with:
        inlineScript: |
          az config set extension.use_dynamic_install=yes_without_prompt
          az containerapp registry set -n wellness-container-fe-dev -g development --server wellnessfedev.azurecr.io --username ${{ secrets.TASKSMANAGER_REGISTRY_USERNAME }} --password ${{ secrets.TASKSMANAGER_REGISTRY_PASSWORD }}
          az containerapp update -n wellness-container-fe-dev -g development --image wellnessfedev.azurecr.io/wellness-fe-dev:${{ github.sha }}
