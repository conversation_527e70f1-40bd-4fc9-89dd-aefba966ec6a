/* eslint-disable @next/next/next-script-for-ga */
// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { Html, Head, Main, NextScript } from 'next/document';

const Document = () => {
  return (
    <Html lang="ja">
      <Head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="true"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@300;400;500;700&display=swap"
          rel="stylesheet"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Hind:wght@300;400;500;600;700&display=swap"
          rel="stylesheet"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Noto+Serif+TC:wght@200..900&display=swap"
          rel="stylesheet"
        />

        {/* Google Tag Manager for development*/}
        {/* {process.env.APP_ENV === 'development' && (
          <>
            <script
              async
              dangerouslySetInnerHTML={{
                __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-PVSMVR2C');`,
              }}
            />
          </>
        )} */}
        {/* Google Tag Manager for development*/}

        {/* Google Tag Manager for staging */}
        {process.env.APP_ENV === 'staging' && (
          <>
            <script
              async
              src={`https://www.googletagmanager.com/gtag/js?id=${process.env.GTM_KEY}`}
            />
            <script
              dangerouslySetInnerHTML={{
                __html: `window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '${process.env.GTM_KEY}');`,
              }}
            />
          </>
        )}
        {/* Google Tag Manager for staging */}

        {/* Google Tag Manager for production */}
        {/* {process.env.APP_ENV === 'production' && (
          <Script
            id="gtm"
            strategy="beforeInteractive"
            dangerouslySetInnerHTML={{
              __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                })(window,document,'script','dataLayer','GTM-N7MKCSP');`,
            }}
          />
        )} */}
        {/* End Google Tag Manager for production */}
      </Head>
      <body>
        {/* Google Tag Manager (noscript) for development */}
        {/* {process.env.APP_ENV === 'production' && (
          <noscript
            dangerouslySetInnerHTML={{
              __html: `<iframe src="https://www.googletagmanager.com/ns.html?id=GTM-PVSMVR2C"
    height="0" width="0" style="display:none;visibility:hidden"></iframe>`,
            }}
          />
        )} */}
        {/* End Google Tag Manager (noscript) for development */}

        {/* Google Tag Manager (noscript) for production */}
        {process.env.APP_ENV === 'production' && (
          <noscript
            dangerouslySetInnerHTML={{
              __html: `<iframe src="https://www.googletagmanager.com/ns.html?id=GTM-N7MKCSP"
                height="0" width="0" style="display:none;visibility:hidden"></iframe>`,
            }}
          />
        )}
        {/* End Google Tag Manager (noscript) for production */}
        <Main />
        <NextScript />
      </body>
    </Html>
  );
};

export default Document;
