// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { routers } from '@/constants/routes';
import { useItemIdScroll } from '@/hooks/use-item-id-scroll';
import { INewsLiffItem } from '@/types/news-list';
import { convertToCharacterSeparatedFormat } from '@/utils/time';
import { useRouter } from 'next/router';
import { ForwardedRef, MutableRefObject, forwardRef } from 'react';

const NewsItem = forwardRef(
  (
    { news_id, title, published_at }: INewsLiffItem,
    ref: ForwardedRef<HTMLDivElement>
  ) => {
    const router = useRouter();
    const { item_id } = useItemIdScroll({
      asPath: router.asPath,
      id: Number(news_id),
      ref,
    });

    if (item_id && item_id === `new${news_id}` && ref) {
      (ref as MutableRefObject<HTMLDivElement>).current?.scrollIntoView();
    }

    return (
      <li className="py-6 border-b border-[#eadbd5] mr-6 ml-2">
        <div
          ref={item_id === `${`new${news_id}`}` ? ref : undefined}
          id={`new${news_id}`}
        >
          <a
            data-testid="news-item-link"
            onClick={async () => {
              await router.replace(routers.newsList, {
                query: {
                  item_id: `new${news_id}`,
                },
              });
              router.push(`${routers.newsList}/${news_id}`);
            }}
          >
            <span className="font-normal text-[10px] leading-[14px] mb-2 block">
              {convertToCharacterSeparatedFormat(published_at, 'dot')}
            </span>
            <h6 className="font-normal text-sm leading-5">{title}</h6>
          </a>
        </div>
      </li>
    );
  }
);

NewsItem.displayName = 'NewsItem';

export default NewsItem;
