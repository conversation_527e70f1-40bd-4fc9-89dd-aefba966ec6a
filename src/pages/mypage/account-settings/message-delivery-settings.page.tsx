// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import Loading from '@/components/Spinner/Loading';
import { FC } from 'react';
import MypageLayout from '../components/MypageLayout';
import MessageDeliverySettingsForm from './components/MessageDeliverySettingsForm';
import { useMessageDeliverySettingsGet } from './hooks/useMessageDeliverySettingsGet';

const MessageDeliverySettingsScreen: FC = () => {
  const { companyOn, myPartnerGroup, companies, setCount, count } =
    useMessageDeliverySettingsGet();

  return (
    <>
      <MypageLayout>
        {myPartnerGroup.length > 0 && Object.keys(companies).length > 0 ? (
          <MessageDeliverySettingsForm
            companies={companies}
            myPartnerGroup={myPartnerGroup}
            companyOn={companyOn}
            reCallApi={() => setCount(count + 1)}
          />
        ) : (
          <Loading />
        )}
      </MypageLayout>
    </>
  );
};

export default MessageDeliverySettingsScreen;
