// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { RefObject, useState, useEffect, useRef } from 'react';

interface UseScrollProps {
  onScroll?: () => void;
  dependencies?: unknown[];
}

export function useScroll({
  onScroll = undefined,
  dependencies = [],
}: UseScrollProps = {}): [RefObject<HTMLDivElement>, boolean] {
  const containerRef = useRef<HTMLDivElement>(null);
  const [scrolledToBottom, setScrolledToBottom] = useState(false);

  useEffect(() => {
    const listener = () => {
      if (!containerRef.current) return;

      if (typeof onScroll === 'function') {
        onScroll();
      }

      const { offsetHeight, scrollTop, scrollHeight } = containerRef.current;

      if (offsetHeight + scrollTop >= scrollHeight) {
        setScrolledToBottom(true);
      }
    };

    containerRef.current?.addEventListener('scroll', listener);

    return () => {
      containerRef.current?.removeEventListener('scroll', listener);
    };
  }, [containerRef.current, ...dependencies]);

  return [containerRef, scrolledToBottom];
}
