// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { useLiff } from '@/contexts/liff';
import { IRedirectUtils, IVerifyRedirectExternalError } from '@/types/redirect';
import { useRedirect } from '@/hooks/use-redirect';
import Loading from '@/components/Spinner/Loading';
import redirectApi from '@/services/internal/modules/redirect';
import { LIFF_INSTALL_OS_ANDROID, LINE_INSTALL_OS_IOS } from '@/constants/line';
import {
  LAUNCH_APP_MESSAGE_TEMPLATE,
  LINK_TAPPED_MESSAGE_TEMPLATE,
  LINK_TAPPED_REDIRECT_TYPE,
  REDIRECT_EXPIRED_MESSAGE,
} from '@/constants/redirect';
import { EBrowserType } from '@/constants/enum';
import { useRouter } from 'next/router';
import { routers } from '@/constants/routes';
import { GONE } from '@/constants/status-response';
import { Message } from '@/constants/message';
import { redirectToLineChatRoomOA } from '@/utils/redirect';

const RedirectScreen: FC = () => {
  const { liff, setError } = useLiff();
  const lineInstallOS = liff.getOS();
  const router = useRouter();

  useRedirect(async ({ query, sendMessage, redirect }: IRedirectUtils) => {
    const { redirect_id: redirectId, index } = query;

    try {
      const {
        redirect: redirectUrl,
        withPoint,
        url_type: urlType,
        browser_type,
      } = await redirectApi.verifyRedirectExternalUrl({
        redirectId: redirectId as string,
        index: Number(index),
      });

      if (withPoint) {
        const message =
          urlType === LINK_TAPPED_REDIRECT_TYPE
            ? LINK_TAPPED_MESSAGE_TEMPLATE
            : LAUNCH_APP_MESSAGE_TEMPLATE;
        await sendMessage(message);
      }

      switch (lineInstallOS) {
        case LINE_INSTALL_OS_IOS:
        case LIFF_INSTALL_OS_ANDROID:
          browser_type === EBrowserType.external
            ? redirect(redirectUrl, { withExternalBrowser: true })
            : redirect(redirectUrl);
          break;
        default:
          redirect(redirectUrl);
          break;
      }
    } catch (error) {
      const {
        errors: { statusCode },
      } = error as unknown as IVerifyRedirectExternalError;
      if (statusCode === GONE) {
        setError(Message.error.expired);
        await sendMessage(REDIRECT_EXPIRED_MESSAGE);
        redirectToLineChatRoomOA(liff);
      } else router.replace(routers.notFound);
    }
  });

  return <Loading />;
};

export default RedirectScreen;
