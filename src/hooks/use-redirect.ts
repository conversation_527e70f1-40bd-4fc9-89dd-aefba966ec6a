// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useLiff } from '@/contexts/liff';
import { routers } from '@/constants/routes';
import { IRedirectUtils, RedirectOptions } from '@/types/redirect';
import { redirectToLineChatRoomOAWithCheckUtou } from '@/utils/redirect';

export function useRedirect(
  redirectHandler: (redirectUtils: IRedirectUtils) => Promise<void>
) {
  const { isLoggedIn, liff, userInfo } = useLiff();
  const { query, replace } = useRouter();
  const { is_active: isActive } = query;
  const lineContext = liff.getContext();
  const router = useRouter();

  const sendMessage = async (message = 'リンクタップ') => {
    if (lineContext?.type !== 'utou') return;

    await liff.sendMessages([{ type: 'text', text: message }]);
  };

  const redirect = (url: string, options?: RedirectOptions) => {
    const { withExternalBrowser = false } = options || {};

    if (withExternalBrowser) {
      liff.openWindow({ url, external: true });
      liff.closeWindow();
    } else {
      router.push(url);
    }
  };

  const replaceWithNotFoundScreen = () => {
    replace(routers.notFound);
  };

  const beforeRedirectHandler = () => {
    router.replace({ query: { ...query, is_active: true } });
  };

  useEffect(() => {
    if (!isLoggedIn || Object.keys(userInfo).length === 0) return;

    if (isActive) {
      redirectToLineChatRoomOAWithCheckUtou(liff);
      return;
    }

    beforeRedirectHandler();
    redirectHandler({ query, redirect, sendMessage }).catch(
      replaceWithNotFoundScreen
    );
  }, [isLoggedIn, userInfo]);

  return [query, redirect, sendMessage];
}
