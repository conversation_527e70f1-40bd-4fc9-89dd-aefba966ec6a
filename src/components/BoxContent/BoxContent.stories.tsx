// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import type { Meta, Story } from '@storybook/react';
import { Button } from '../Button';
import { BoxContent } from './BoxContent';
import type { IBoxContentProps } from './BoxContent';

export default {
  title: 'Components/BoxContent',
  component: BoxContent,
} as Meta;

const Template: Story<IBoxContentProps> = ({
  children,
  ...rest
}): JSX.Element => {
  return (
    <>
      <BoxContent {...rest}>{children}</BoxContent>
    </>
  );
};

export const Default = Template.bind({});
Default.args = {
  children: (
    <>
      <BoxContent.Header>
        <h3 className="text-theme-main text-lg text-center font-bold">
          Terms of Service
        </h3>
      </BoxContent.Header>
      <BoxContent.Body className="pt-6 space-y-6 mb-auto">
        <div className="space-y-6 !mb-auto">
          <p className="text-base leading-relaxed text-gray-500 dark:text-gray-400">
            With less than a month to go before the European Union enacts new
            consumer privacy laws for its citizens, companies around the world
            are updating their terms of service agreements to comply.
          </p>
          <p className="text-base leading-relaxed text-gray-500 dark:text-gray-400">
            The European Union’s General Data Protection Regulation (G.D.P.R.)
            goes into effect on May 25 and is meant to ensure a common set of
            data rights in the European Union. It requires organizations to
            notify users as soon as possible of high-risk data breaches that
            could personally affect them.
          </p>
        </div>
      </BoxContent.Body>
      <BoxContent.Footer className="space-y-2">
        <Button fullSized size="lg">
          I accept
        </Button>
      </BoxContent.Footer>
    </>
  ),
};
