// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { twMerge } from 'tailwind-merge';
import { ComponentProps, FC } from 'react';
import { IThemeSizes } from '@/types/theme';
import { styles } from './Gift.styles';
import { GiftBody } from './GiftBody/GiftBody';
import { GiftNote } from './GiftNote/GiftNote';
import { GiftThumb } from './GiftThumb/GiftThumb';
import { GiftAction } from './GiftAction/GiftAction';
import { GiftContent } from './GiftContent/GiftContent';

export type GiftGap = Pick<IThemeSizes, 'xs' | 'sm' | 'md' | 'lg'>;

export type GiftOrientation = 'horizontal' | 'vertical';

export interface GiftTheme {
  base: string;
  gap: GiftGap;
  orientation: Record<GiftOrientation, string>;
}

export interface GiftProps extends ComponentProps<'div'> {
  gap?: keyof GiftGap;
  orientation?: GiftOrientation;
}

const GiftComponent: FC<GiftProps> = ({
  gap = 'md',
  orientation = 'vertical',
  className,
  children,
  ...props
}) => {
  return (
    <div
      className={twMerge(
        styles.base,
        styles.gap[gap],
        styles.orientation[orientation],
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

GiftComponent.displayName = 'Gift';
export const Gift = Object.assign(GiftComponent, {
  Body: GiftBody,
  Note: GiftNote,
  Thumb: GiftThumb,
  Action: GiftAction,
  Content: GiftContent,
});
