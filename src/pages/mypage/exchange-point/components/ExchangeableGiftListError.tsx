// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { MessageBox } from '@/components/MessageBox';

interface ExchangeableGiftListErrorProps {
  retry?: () => void;
}

export const ExchangeableGiftListError: FC<ExchangeableGiftListErrorProps> = ({
  retry,
}) => {
  return (
    <MessageBox className="pt-[100px]">
      <MessageBox.Body className="max-w-[75vw]">
        <MessageBox.Icon name="AccessDenied" />
        <MessageBox.Content>
          現在ギフト一覧を読み込めないか、ギフトの準備中です。
          <br />
          時間を空けて再度お試しください。
        </MessageBox.Content>
      </MessageBox.Body>
      <MessageBox.Action onClick={retry} customSpan="!font-bold">
        再度読み込む
      </MessageBox.Action>
    </MessageBox>
  );
};
