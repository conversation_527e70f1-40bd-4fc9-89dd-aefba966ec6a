// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import classNames from 'classnames';
import React, { ComponentProps, FC } from 'react';
import { styles } from './MessageBoxBody.styles';

export interface MessageBoxBodyTheme {
  base: string;
}

export const MessageBoxBody: FC<ComponentProps<'div'>> = ({
  className,
  children,
  ...props
}) => {
  return (
    <div className={classNames(styles.base, className)} {...props}>
      {children}
    </div>
  );
};
