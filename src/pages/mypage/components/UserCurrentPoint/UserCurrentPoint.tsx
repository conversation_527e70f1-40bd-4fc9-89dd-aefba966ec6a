// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React, { FC } from 'react';
import { useFetch } from '@/hooks/use-fetch';
import pointApi from '@/services/internal/modules/point';
import {
  CurrentPointActionProps,
  CurrentPointLabelProps,
  CurrentPointLinkProps,
  CurrentPointNoteProps,
  CurrentPointProps,
} from '@/components/CurrentPoint';
import { UserCurrentPointError } from './UserCurrentPointError';
import { UserCurrentPointLoading } from './UserCurrentPointLoading';
import { UserCurrentPointMine } from './UserCurrentPointMine';

export type UserCurrentPointLayouts = 'full' | 'medium' | 'short';

export interface UserCurrentPointProps extends CurrentPointProps {
  layout?: UserCurrentPointLayouts;
  label?: Omit<CurrentPointLabelProps, 'point'>;
  action?: CurrentPointActionProps;
  link?: CurrentPointLinkProps;
  note?: CurrentPointNoteProps;
}

export const UserCurrentPoint: FC<UserCurrentPointProps> = ({
  layout = 'short',
  ...props
}) => {
  const {
    fetch: getCurrentPoint,
    data: currentPoint,
    isFetching,
    isErrorFetching,
  } = useFetch<number>({
    fetcher: pointApi.getCurrentPoint,
    initialValue: 0,
    dependencies: [],
  });

  return (
    <>
      {isFetching ? (
        <UserCurrentPointLoading layout={layout} />
      ) : isErrorFetching ? (
        <UserCurrentPointError retry={getCurrentPoint} />
      ) : (
        <UserCurrentPointMine
          layout={layout}
          points={currentPoint}
          {...props}
        />
      )}
    </>
  );
};
