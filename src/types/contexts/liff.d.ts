// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import type { Liff } from '@line/liff';
import type { Dispatch, SetStateAction } from 'react';
import { ImemberStatus, IUserInfo } from '../user';
import { IUserLineInfo } from '../user/line';

declare global {
  interface Window {
    liff?: Liff;
  }
}

export interface ILiffContext {
  error?: unknown;
  redirect?: string;
  isLoggedIn: boolean;
  isReady: boolean;
  liff: Liff;
  userInfo: IUserInfo;
  userLineInfo: IUserLineInfo;
  memberStatus: ImemberStatus;
  setUserInfo: Dispatch<SetStateAction<IUserInfo>>;
  setMemberStatus: Dispatch<SetStateAction<ImemberStatus>>;
  setUserLineInfo: Dispatch<SetStateAction<userLineInfo>>;
  setError: Dispatch<SetStateAction<unknown>>;
}
