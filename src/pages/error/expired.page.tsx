// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { BoxContent } from '@/components/BoxContent';
import { Button } from '@/components/Button';
import { Logo } from '@/components/Logo';
import { routers } from '@/constants/routes';
import { useLiff } from '@/contexts/liff';
import { redirectToLineChatRoomOA } from '@/utils/redirect';
import { useRouter } from 'next/router';
import { FC, useEffect } from 'react';

const ExpiredEx: FC = () => {
  const router = useRouter();
  const { error, liff } = useLiff();
  useEffect(() => {
    if (!error) {
      router.push(routers.home);
    }
  }, [router, error]);
  return (
    <>
      <div className="container mx-auto">
        <BoxContent>
          <BoxContent.Body className="pt-[160px]">
            <Logo box className="mb-10" />
            <div className="text-center">
              <h3 className="text-base font-bold">
                ポイント付与期間は終了しています。
              </h3>
            </div>
          </BoxContent.Body>
          <BoxContent.Footer>
            <Button
              fullSized
              type="submit"
              size="xl"
              color="primary"
              onClick={() => redirectToLineChatRoomOA(liff)}
            >
              トーク画面へ戻る
            </Button>
          </BoxContent.Footer>
        </BoxContent>
      </div>
    </>
  );
};

export default ExpiredEx;
