// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { IAppConfig } from '@/types/app';
import RegisterConfirmScreen from '@/pages/register-confirm/components/RegisterConfirmScreen';

export interface IRegisterConfirmScreenProps {
  appConfig: IAppConfig;
}

const RegisterConfirm: FC<IRegisterConfirmScreenProps> = ({ appConfig }) => {
  return (
    <>
      <div className="container mx-auto">
        <div className="w-full md:max-w-md mx-auto space-y-6 min-h-screen flex flex-col justify-between pt-6 pb-10">
          <RegisterConfirmScreen companyId={appConfig.companyId} />
        </div>
      </div>
    </>
  );
};

export default RegisterConfirm;
