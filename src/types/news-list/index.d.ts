// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
export interface INewsLiffDetailParams {
  id: string;
}

type IIsPublished = '1' | '0';

export interface INewsLiff {
  continuationToken: unknown;
  data: INewsLiffDetailResponse[];
  hasMoreResults: boolean;
}

export interface INewsLiffDetailResponse {
  type: string;
  pk: string;
  news_id: string;
  title: string;
  content: string;
  created_at: string;
  published_at: string;
  unique_key: string;
  is_published: IIsPublished;
}

export interface INewsLiffItem {
  news_id: string;
  title: string;
  published_at: string;
}
