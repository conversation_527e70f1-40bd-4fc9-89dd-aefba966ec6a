// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { useScroll } from '@/hooks/use-scroll';

interface UseScrollProps {
  onScroll?: () => void;
  dependencies?: unknown[];
}

const Target: FC<UseScrollProps> = ({ onScroll, dependencies }) => {
  const [containerRef] = useScroll({
    onScroll,
    dependencies,
  });

  return <div data-testid="scrollbox" ref={containerRef}></div>;
};

describe('hooks/use-scroll/use-scroll.spec.tsx', () => {
  afterAll(() => {
    jest.clearAllMocks();
  });

  describe('User interactions', () => {
    it('should handle scroll event when scrolling', async () => {
      render(<Target />);

      const target = screen.getByTestId('scrollbox');

      fireEvent.scroll(target, { target: { scrollY: 300 } });

      expect(target).toBeInTheDocument();
    });

    it('should calls `onScroll` only on given `onScroll`', async () => {
      const onScroll = jest.fn();

      render(<Target onScroll={onScroll} />);

      const target = screen.getByTestId('scrollbox');

      fireEvent.scroll(target, { target: { scrollY: 300 } });

      expect(onScroll).toHaveBeenCalledTimes(1);
    });

    it('should return when have not containerRef.current', async () => {
      render(<Target />);

      const target = screen.getByTestId('scrollbox');

      fireEvent.scroll(target, { target: { scrollY: 300 } });
    });
  });
});
