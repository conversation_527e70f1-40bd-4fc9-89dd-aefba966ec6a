// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { Button } from '@/components/Button';
import { Modal } from '@/components/Modal';
import React, { Dispatch, SetStateAction } from 'react';
interface ModalErrorProps {
  setIsOpenError: Dispatch<SetStateAction<boolean>>;
  isOpenError: boolean;
  text: string | null;
}

const ModalError: React.FC<ModalErrorProps> = ({
  isOpenError,
  setIsOpenError,
  text,
}) => {
  return (
    <Modal size="sm" show={isOpenError} innerClass="w-[324px] overflow-y-auto">
      <Modal.Body className="!px-6 pt-[40px]">
        <div
          className="text-sm"
          dangerouslySetInnerHTML={{
            __html: text ? text.replace(/\n/g, '<br />') : '',
          }}
        />
      </Modal.Body>
      <Modal.Footer className="pt-8 px-6">
        <Button
          fullSized
          className="!font-bold"
          size="xl"
          color="primary"
          onClick={() => setIsOpenError(false)}
        >
          戻る
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ModalError;
