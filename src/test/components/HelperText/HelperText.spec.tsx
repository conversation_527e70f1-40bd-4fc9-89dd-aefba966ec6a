// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen } from '@testing-library/react';
import { HelperText } from '@/components/HelperText';

describe('components/HelperText/HelperText.spec.tsx', () => {
  describe('A11y', () => {
    it('should render when `children={0}`', () => {
      render(<HelperText aria-label="helper">My helper text</HelperText>);
      const logo = screen.getByLabelText('helper');
      expect(logo).toHaveTextContent('My helper text');
    });
  });

  describe('Prop', () => {
    it('should use `color` classes', () => {
      render(<HelperText color="success" aria-label="My helper text" />);
      const logo = screen.getByLabelText('My helper text');

      expect(logo.classList.contains('text-theme-success-primary')).toBe(true);
    });
  });
});
