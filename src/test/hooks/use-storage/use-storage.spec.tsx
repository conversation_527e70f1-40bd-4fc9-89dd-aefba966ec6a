// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC, useEffect } from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { useLocalStorage, useSessionStorage } from '@/hooks/use-storage';

interface WrapperProps {
  storageKey: string;
  defaultValue?: string;
  value?: string | ((prevState: string) => string);
  handler?: (val: string) => void;
}

const WrapperComponent: FC<WrapperProps> = ({
  storageKey,
  defaultValue,
  value: finalValue,
  handler,
}) => {
  const [value, setValue, removeValue] = useLocalStorage({
    key: storageKey,
    defaultValue,
    callback: (val: string) => {
      handler?.(val);
    },
  });

  useSessionStorage({
    key: storageKey,
    defaultValue,
  });

  useEffect(() => {
    if (finalValue) setValue(finalValue);
  }, [finalValue]);

  return (
    <div>
      <div data-testid="ls">{value}</div>
      <button data-testid="action" onClick={removeValue}>
        Remove
      </button>
    </div>
  );
};

describe('hooks/use-storage/use-storage.spec.tsx', () => {
  // Mock the local storage object for each test
  let store: { [key: string]: string } = {};
  beforeEach(() => {
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(key => store[key]),
        setItem: jest.fn((key, value) => {
          store[key] = value;
        }),
        removeItem: jest.fn(key => delete store[key]),
      },
      writable: true,
    });
  });

  afterEach(() => {
    store = {};
  });

  describe('A11y', () => {
    it('should returns default value if no value is set', () => {
      render(<WrapperComponent storageKey="ls-key" defaultValue="ls-value" />);
      expect(screen.getByTestId('ls')).toHaveTextContent('ls-value');
    });

    it('should ignores default value if already set', () => {
      // Set the target key to an existing value
      store['ls-key'] = 'ls-existing';
      render(<WrapperComponent storageKey="ls-key" defaultValue="ls-value" />);
      expect(screen.getByTestId('ls')).toHaveTextContent('ls-existing');
    });

    it('should sets & returns expected local storage value', () => {
      // Render the hook with a default value for key "ls-key"
      const { rerender } = render(
        <WrapperComponent storageKey="ls-key" defaultValue="ls-value" />
      );
      expect(screen.getByTestId('ls')).toHaveTextContent('ls-value');
      // Rerender the hook & set a new value for "ls-key"
      rerender(<WrapperComponent storageKey="ls-key" value="ls-value-2" />);
      expect(screen.getByTestId('ls')).toHaveTextContent('ls-value-2');
    });
  });

  describe('User interactions', () => {
    it('should remove local storage value', () => {
      const { rerender } = render(
        <WrapperComponent storageKey="ls-key" defaultValue="ls-value" />
      );
      expect(screen.getByTestId('ls')).toHaveTextContent('ls-value');

      fireEvent.click(screen.getByTestId('action'));
      rerender(<WrapperComponent storageKey="ls-key" />);

      expect(window.localStorage.removeItem).toHaveBeenCalled();
    });

    it('should sets & returns expected local storage value with handler function', () => {
      const { rerender } = render(
        <WrapperComponent storageKey="ls-key" value="ls-value" />
      );

      const handler = (val: string) => {
        return `${val}-handler`;
      };
      rerender(<WrapperComponent storageKey="ls-key" value={handler} />);

      expect(screen.getByTestId('ls')).toHaveTextContent('ls-value-handler');
    });
  });
});
