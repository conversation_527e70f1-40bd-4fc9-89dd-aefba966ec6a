// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import {
  INewsLiff,
  INewsLiffDetailParams,
  INewsLiffDetailResponse,
} from '@/types/news-list';
import service from '..';

const basePath = '/news-liff';

const newsLiffApi = {
  getNewsLiff: () => service.get<INewsLiff>(`${basePath}`),
  getNewsLiffDetail: ({ id }: INewsLiffDetailParams) =>
    service.get<INewsLiffDetailResponse>(`${basePath}/${id}`),
};

export default newsLiffApi;
