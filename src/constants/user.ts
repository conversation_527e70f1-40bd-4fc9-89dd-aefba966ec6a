// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { UNKNOWN } from './define';

export const USER_ACCESS_TOKEN = 'accessToken';
export const USER_ACCESS_TOKEN_EXPIRES = 1 * 60 * 60; // 60 minutes
export const USER_COMPANY = 'userCompany';
export const USER_COMPANY_EXPIRES = 10 * 365 * 24 * 60 * 60; // 10 years
export const USER_INFO_REGISTER = 'userInfoRegister';
export const USER_PREFECTURE_SELECTED = 'userPrefectureSelected';
export const USER_REGISTER_ERRORS = 'userRegisterErrors';
export const USER_CHECKIN_RESULT = 'userCheckinResult';
export const GENDER_FEMALE_VALUE = 'female';
export const GENDER_FEMALE_LABEL = '女性';
export const GENDER_MALE_VALUE = 'male';
export const GENDER_MALE_LABEL = '男性';
export const GENDER_OTHER_VALUE = 'other';
export const GENDER_OTHER_LABEL = 'その他';
export const GENDER_NOT_ANSWER_VALUE = 'not_answer';
export const GENDER_NOT_ANSWER_LABEL = '回答しない';
export const PEER_CONNE_ID = 'peer_conne_id';
export const COMPANY_ID = 'company_id';
export const SERVICE_ID = 'service_id';
export const ONE_TIME_TOKEN = 'one_time_token';
export const LINE_ACCESS_TOKEN = 'line_access_token';
export const USER_GENDERS_LIST = [
  {
    id: GENDER_FEMALE_VALUE,
    label: GENDER_FEMALE_LABEL,
    value: GENDER_FEMALE_VALUE,
  },
  {
    id: GENDER_MALE_VALUE,
    label: GENDER_MALE_LABEL,
    value: GENDER_MALE_VALUE,
  },
  {
    id: GENDER_OTHER_VALUE,
    label: GENDER_OTHER_LABEL,
    value: GENDER_OTHER_VALUE,
  },
  {
    id: GENDER_NOT_ANSWER_VALUE,
    label: GENDER_NOT_ANSWER_LABEL,
    value: GENDER_NOT_ANSWER_VALUE,
  },
];

export const PREFECTURE_SELECTED_DEFAULT = UNKNOWN;

export const BIRTH_YEARS = Array.from(
  { length: new Date().getFullYear() - 1899 },
  (_, i) => new Date().getFullYear() - i - 11 //Because the min age of the users is set as 12 years old.
);

export const BIRTH_MONTHS = [...Array(12)].map((_, index) => index + 1);

export const EMAIL = 'メールアドレス';
export const PASSWORD = 'パスワード';
export const PASSWORD_CONFIRM = 'パスワードの確認';
export const BIRTH_MONTH_YEAR = '生まれた年・月';
export const PREFECTURE = 'お住いの都道府県';
export const GENDER = '性別';
export const POLICY = 'プライバシーポリシーに同意する';
export const TERM = '会員規約に同意する';

export const PARTNER_GROUPS_ID = 'PartnerGroupsId';
