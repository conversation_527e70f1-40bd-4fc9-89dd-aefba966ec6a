// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import MenuItem, { IMenuItemProps } from '@/pages/mypage/components/MenuItem';

jest.mock('next/router', () => require('next-router-mock'));

const TestMenuItem: FC<IMenuItemProps> = props => {
  return <MenuItem {...props} />;
};

describe('pages/mypage/components/MenuItem.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<TestMenuItem label="lorem" />);
      const menu = screen.getByTestId('menu-item');

      expect(menu).toBeInTheDocument();
    });
  });

  describe('User interactions', () => {
    it('should trigger `onClick` menu to redirect', async () => {
      render(<TestMenuItem label="lorem" to="/registered" />);
      const menu = screen.getByTestId('menu-item');
      fireEvent.click(menu);

      expect(menu).toBeInTheDocument();
    });
  });
});
