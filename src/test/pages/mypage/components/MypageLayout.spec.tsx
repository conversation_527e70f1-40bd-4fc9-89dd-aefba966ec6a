// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen } from '@testing-library/react';
import MypageLayout from '@/pages/mypage/components/MypageLayout';

describe('pages/mypage/components/MypageLayout.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document with wrap container', async () => {
      render(<MypageLayout />);
      const href = screen.getByRole('link');

      expect(href).toBeInTheDocument();
    });

    it('should render in the document without wrap container', async () => {
      render(<MypageLayout withContainer={false} />);
      const href = screen.getByRole('link');

      expect(href).toBeInTheDocument();
    });
  });
});
