// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen, waitFor } from '@testing-library/react';
import redirectApi from '@/services/internal/modules/redirect';
import RedirectGifteeScreen from '@/pages/re/prize/[lotteryId].page';
import { LiffContextMockProviders } from '@/test/mocks';

jest.mock('next/router', () => require('next-router-mock'));

const TestRedirectPrizeScreen = (): JSX.Element => {
  return (
    <LiffContextMockProviders>
      <RedirectGifteeScreen />
    </LiffContextMockProviders>
  );
};

describe('pages/re/prize/[lotteryId].page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<TestRedirectPrizeScreen />);

      await waitFor(() => {
        expect(screen.getByRole('status')).toBeInTheDocument();
      });
    });
  });

  describe('Fetching', () => {
    it('should call api return response with isWinner=true', async () => {
      const spy = jest.spyOn(redirectApi, 'verifyRedirectGifteeUrl');
      spy.mockResolvedValue({
        isWinner: true,
        redirect: 'https://example.com',
      });

      render(<TestRedirectPrizeScreen />);

      await waitFor(() => {
        expect(screen.getByRole('status')).toBeInTheDocument();
      });
    });

    it('should call api return response with isWinner=false', async () => {
      const spy = jest.spyOn(redirectApi, 'verifyRedirectGifteeUrl');
      spy.mockResolvedValue({
        isWinner: false,
        redirect: 'https://example.com',
      });

      render(<TestRedirectPrizeScreen />);

      await waitFor(() => {
        expect(screen.getByRole('status')).toBeInTheDocument();
      });
    });
  });
});
