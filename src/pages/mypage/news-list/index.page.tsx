// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { Button } from '@/components/Button';
import Loading from '@/components/Spinner/Loading';
import { routers } from '@/constants/routes';
import { useFetch } from '@/hooks/use-fetch';
import newsLiffApi from '@/services/internal/modules/news-list';
import { INewsLiff } from '@/types/news-list';
import { FC, useRef } from 'react';
import NewsItem from './components/news-item';
import { NewsError } from '@/pages/mypage/news-list/components/NewsError';

const AnnouncementScreen: FC = () => {
  const {
    isFetching,
    data: { data: listNewsLiff },
    isErrorFetching,
    fetch,
  } = useFetch<INewsLiff>({
    dependencies: [],
    fetcher: newsLiffApi.getNewsLiff,
    initialValue: {
      continuationToken: null,
      hasMoreResults: false,
      data: [],
    } as INewsLiff,
  });
  const itemRef = useRef<HTMLDivElement>(null);

  return (
    <>
      {isFetching ? (
        <Loading />
      ) : (
        <div className="container mx-auto">
          <div className="w-full md:max-w-md mx-auto min-h-screen flex flex-col justify-between">
            {isErrorFetching ? (
              <>
                <div />
                <NewsError retry={fetch} title="お知らせ一覧" />
              </>
            ) : (
              <ul>
                {listNewsLiff.map(item => (
                  <NewsItem
                    key={item.unique_key}
                    news_id={item.unique_key}
                    title={item.title}
                    published_at={item.published_at}
                    ref={itemRef}
                  />
                ))}
              </ul>
            )}
            <div className="pt-8">
              <Button
                fullSized
                size="xl"
                color="link-primary"
                customSpan="leading-6"
                href={routers.myPageTop}
              >
                ＼マイページTOPに戻る／
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default AnnouncementScreen;
