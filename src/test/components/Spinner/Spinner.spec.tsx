// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen } from '@testing-library/react';
import { Spinner } from '@/components/Spinner/Spinner';

describe('components/Spinner/Spinner.spec.tsx', () => {
  it('should have `role="status"` by default', () => {
    render(<Spinner aria-label="My spinner" />);
    const spinner = screen.getByRole('status');

    expect(spinner).toHaveAccessibleName('My spinner');
  });

  it('should be able to set no `role`', () => {
    render(<Spinner aria-label="My spinner" role={undefined} />);
    const spinner = screen.getByLabelText('My spinner');

    expect(spinner).not.toHaveAttribute('role');
  });

  it('should use `color` classes', () => {
    const { container } = render(<Spinner color="primary" />);
    // eslint-disable-next-line testing-library/no-container, testing-library/no-node-access
    const svg = container.querySelector('svg');

    expect(svg?.classList.contains('text-gray-200')).toBe(true);
  });
});
