// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC, Dispatch, SetStateAction, ReactNode } from 'react';
import { useScroll } from '@/hooks/use-scroll';
import { Button } from '../Button';
import { IModalProps, Modal } from './Modal';

export interface IPoliciesModalProps extends IModalProps {
  isShowModal: boolean;
  setIsShowModal: Dispatch<SetStateAction<boolean>>;
  onAcceptHandler?: (type: 'policies' | 'terms') => void;
  customFooter?: ReactNode;
  isShowNotice?: boolean;
}

const PoliciesModal: FC<IPoliciesModalProps> = ({
  isShowModal,
  setIsShowModal,
  onAcceptHandler,
  customFooter,
  isShowNotice = false,
  ...props
}) => {
  const [container, scrolledToBottom] = useScroll({
    dependencies: [isShowModal],
  });

  const closeHandler = () => {
    setIsShowModal(false);
  };

  return (
    <Modal
      size="md"
      show={isShowModal}
      setShow={setIsShowModal}
      onClose={closeHandler}
      {...props}
    >
      <Modal.Header>
        <h3 className="text-theme-primary text-lg text-center font-bold">
          プライバシーポリシー
        </h3>
      </Modal.Header>
      <Modal.Body>
        <div className="relative">
          <div
            className="bg-theme-primary/10 border border-theme-primary/30 rounded max-h-[400px] overflow-x-auto"
            ref={container}
          >
            <div className="space-y-3 p-4 text-sm text-justify">
              <h3 className="text-center font-bold">
                ピアコネ　プライバシーポリシー
              </h3>
              <p>
                「ピアコネ」は、株式会社Ａｌｌ　Ｒｉｇｈｔ（以下「当社」といいます。）が運営する会員向けサービス（以下「本サービス」といいます。）です。本サービスを利用するみなさま（以下、「会員」といいます。）は、本サービスに関してのプライバシーポリシー（以下「本ポリシー」といいます。）に同意いただく必要があります。
              </p>
              <div className="space-y-2">
                <p className="font-bold">
                  第　１条　個人情報および個人データの定義
                </p>
                <p>
                  （１）「個人情報」とは、個人情報の保護に関する法律第２条第１項に規定する個人情報をいいます。
                </p>
                <p>
                  （２）「個人データ」とは、個人情報の保護に関する法律第１６条第３項に規定する個人データをいいます。
                </p>
              </div>

              <div className="space-y-2">
                <p className="font-bold">第　２条　取得する個人情報</p>
                <p>
                  会員が本サービスをご利用いただくにあたり、当社は必要な範囲で個人情報を適法かつ公正な手段で取得させていただきます。
                </p>
              </div>

              <div className="space-y-2">
                <p className="font-bold">第　３条　利用目的</p>
                <ul className="space-y-1">
                  <li>
                    <span className="block mb-1">
                      （１）当社は、取得した個人情報を以下の目的で利用します。
                    </span>
                    <ul className="ml-6 space-y-1">
                      <li>
                        ①顧客属性や行動履歴等の各種情報分析、またそれらを活用した本サービスの開発、改修、運用、ならびにマーケティング施策等への展開
                      </li>
                      <li>
                        ②提携先（以下「パートナー企業」といいます）および会員からの問い合わせ対応
                      </li>
                      <li>
                        ③本サービスを通じた当社およびパートナー企業の商品・サービスの案内
                      </li>
                      <li>
                        ④①での運用・分析結果等を活用した当社およびパートナー企業における新たな商品・サービスの開発
                      </li>
                      <li>
                        ⑤個人を識別できない状態に加工し、又は統計処理したデータの企業又は団体に対する提供
                      </li>
                    </ul>
                  </li>

                  <li>
                    （２）前項に定める利用目的を追加し、又は変更した場合は、本サービスを通じて会員へ通知し、又はその内容を公表します。
                  </li>
                </ul>
              </div>

              <div className="space-y-2">
                <p className="font-bold">第　４条　個人データの第三者提供</p>
                <p>
                  当社は、法令の定めによる場合を除き、ご本人の同意がない限り、個人データを第三者に提供することはありません。
                </p>
              </div>

              <div className="space-y-2">
                <p className="font-bold">第　５条　適切な管理</p>
                <p>
                  会員の個人データにおいて、漏えい、紛失、破壊、改ざん又は会員の個人データへの不正なアクセスを防止するため、個人データの取扱いについて、適時、適切に見直しを行い、個人データの安全で正確な管理に努めます。
                </p>
              </div>

              <div className="space-y-2">
                <p className="font-bold">第　６条　委託先の管理</p>
                <ul>
                  <li>
                    （１）本サービスの提供に当たり、個人データの取扱業務の全部または一部を利用目的の範囲内で第三者に委託する場合があります。
                  </li>
                  <li>
                    （２）この場合、個人データの適正な管理が期待できる委託先を選定したうえで、適正な取扱いを確保するための措置を契約上義務付けます。
                  </li>
                  <li>
                    （３）委託先において，当社が果たすべき安全管理措置と同等の措置が講じられるよう、必要かつ適切な監督を行います。
                  </li>
                </ul>
              </div>

              <div className="space-y-2">
                <p className="font-bold">
                  第　７条　本ポリシーの通知・公表と改定、同意取得
                </p>
                <ul className="space-y-1">
                  <li>
                    <span className="block mb-1"> （１）通知・公表</span>
                    <ul className="ml-6">
                      <li>
                        当社は、本ポリシーに関する通知・公表を、本サービスの会員登録時および本サービスの会員専用ページに掲載する方法で行います。
                      </li>
                    </ul>
                  </li>
                  <li>
                    <span className="block mb-1"> （２）改定</span>
                    <ul className="ml-6">
                      <li>
                        当社は、利用目的の変更、関連法令および規範の改定等、必要に応じ、当社の裁量において本ポリシーを改定できるものとします。
                      </li>
                    </ul>
                  </li>
                  <li>
                    <span className="block mb-1"> （３）同意取得</span>
                    <ul className="ml-6">
                      <li>
                        同意の取得は、本サービスの会員登録時およびログイン時（本ポリシーの改定内容に応じて必要な場合）に取得する方法で行います。
                      </li>
                    </ul>
                  </li>
                </ul>
              </div>

              <div className="space-y-2">
                <p className="font-bold">
                  第　８条　開示等の請求および苦情の申出
                </p>
                <p>
                  本サービスに関連して当社が保有する個人データの開示、内容の訂正、追加または削除、利用の停止、消去および第三者への提供の停止の請求および本サービスに関連して当社が保有する個人データの取り扱いに係る苦情の申出については、以下の窓口への郵送により受け付けます。
                </p>
              </div>

              <div className="ml-3">
                <p>
                  開示等の請求等の申出先：
                  <br />
                  株式会社Ａｌｌ　Ｒｉｇｈｔ
                  <br />
                  住所：〒１０３－００２６　東京都中央区日本橋兜町７－１
                  ＫＡＢＵＴＯ ＯＮＥ ９Ｆ　ＷｅＷｏｒｋ
                </p>
              </div>

              <div className="space-y-2 flex justify-end">
                <p>２０２５年　７月　１日改正</p>
              </div>
            </div>
          </div>
          <div className="absolute left-[1px] bottom-[1px] right-[1px] h-10 bg-gradient-to-b from-theme-modal-gradient-top/0 via-theme-modal-gradient-middle/50 to-theme-modal-gradient-bottom rounded-bl rounded-br" />
        </div>
        {isShowNotice && (
          <div className="mt-[21px]">
            <ul className="text--indent">
              <li className="text-xs">
                ※
                一番下までスクロールしていただくと、チェックボタンが押せるようになります。
              </li>
            </ul>
          </div>
        )}
      </Modal.Body>
      <Modal.Footer className="space-y-2">
        {customFooter ?? (
          <>
            <Button
              fullSized
              size="lg"
              color={scrolledToBottom ? 'primary' : 'gray-10'}
              disabled={!scrolledToBottom}
              onClick={() => onAcceptHandler?.('policies')}
            >
              プライバシーポリシーに同意する
            </Button>
            <Button fullSized size="lg" color="link" onClick={closeHandler}>
              閉じる
            </Button>
          </>
        )}
      </Modal.Footer>
    </Modal>
  );
};

export default PoliciesModal;
