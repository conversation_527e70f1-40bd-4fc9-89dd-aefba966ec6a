// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
export interface TextContent {
  meta_title: string;
  text_out_of_period?: string;
  text_description?: string | undefined;
  button_text_image_upload?: string | undefined;
  button_text_image_upload_another?: string | undefined;
  button_text_image_upload_max?: string | undefined;
  button_text_submit?: string;
  text_submitted?: string | undefined;
  text_img_preview?: string | undefined;
}

export interface ImageContent {
  top_image?: string | undefined;
  sample_image?: string | undefined;
}

export interface IGetImageFormData {
  company_id?: string;
  id?: string;
  image?: ImageContent;
  image_count_max?: number;
  text: TextContent;
}

type IGetImageFormStatus = 'success' | 'unavailable' | 'failed';

export interface IGetImageFormResponse {
  status: IGetImageFormStatus;
  data: IGetImageFormData;
}

export interface IFileUpload {
  file: File | null;
  image: string | null | File;
}

export interface IUploadImageFormResponse {
  image_urls: string[];
  image_form_id: string;
  text_submitted: string;
  meta_title: string;
}

export type { IGetImageFormStatus };
