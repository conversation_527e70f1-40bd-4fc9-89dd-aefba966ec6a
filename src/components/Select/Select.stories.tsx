// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import type { Meta, Story } from '@storybook/react';
import type { ISelectProps } from './Select';
import { Select } from './Select';

export default {
  title: 'Components/Select',
  component: Select,
} as Meta;

const Template: Story<ISelectProps> = args => <Select {...args} />;

export const DefaultSelect = Template.bind({});
DefaultSelect.storyName = 'Select';
DefaultSelect.args = {
  id: 'province',
  children: (
    <>
      <option>Tokyo</option>
      <option>Kanto</option>
      <option>Chubu</option>
    </>
  ),
};
