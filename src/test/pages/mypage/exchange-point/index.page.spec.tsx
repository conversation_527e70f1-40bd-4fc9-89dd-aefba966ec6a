// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { act, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { LiffContextMockProviders } from '@/test/mocks';
import { EXCHANGEABLE_GIFT_LIST } from '@/mocks/exchange-points';
import ExchangePointScreen from '@/pages/mypage/exchange-point/index.page';
import exchangePointsApi from '@/services/internal/modules/exchange-points';

jest.mock('next/router', () => require('next-router-mock'));

const ExchangePointScreenTest = () => {
  return (
    <LiffContextMockProviders>
      <ExchangePointScreen />
    </LiffContextMockProviders>
  );
};

describe('pages/mypage/exchange-point/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<ExchangePointScreenTest />);
      const heading = screen.getAllByRole('heading');

      expect(heading.length).toEqual(2);
    });
  });

  describe('Fetching', () => {
    describe('Fetching success', () => {
      it('should call api success return response exchangeable gift list', async () => {
        jest
          .spyOn(exchangePointsApi, 'getExchangeableGiftList')
          .mockResolvedValue(EXCHANGEABLE_GIFT_LIST);

        render(<ExchangePointScreenTest />);

        await waitFor(() => {
          const heading = screen.getAllByRole('heading');
          expect(heading.length).toEqual(9);
        });
      });
    });

    describe('Fetching failure', () => {
      it('should call api failure return error', async () => {
        const user = userEvent.setup();
        jest
          .spyOn(exchangePointsApi, 'getExchangeableGiftList')
          .mockRejectedValue({ statusCode: 500 });

        render(<ExchangePointScreenTest />);

        await waitFor(async () => {
          const button = screen.getAllByRole('button');
          await act(() => user.click(button[0]));

          expect(button.length).toEqual(1);
        });
      });
    });
  });
});
