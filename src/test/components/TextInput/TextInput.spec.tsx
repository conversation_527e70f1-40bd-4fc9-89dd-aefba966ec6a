// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen } from '@testing-library/react';
import { UserCircleIcon } from '@heroicons/react/24/solid';
import { TextInput } from '@/components/TextInput';

describe('components/TextInput/TextInput.spec.tsx', () => {
  describe('A11y', () => {
    it('should have `role="textbox"` by default', () => {
      render(<TextInput />);
      const textInput = screen.getByRole('textbox');
      expect(textInput).toBeInTheDocument();
    });

    it('should have Icon if selected ', () => {
      render(<TextInput rightIcon={UserCircleIcon} />);
      const page = screen.getByTestId('right-icon');

      expect(page).toBeInTheDocument();
    });
  });

  describe('Prop', () => {
    it('should be disabled when `disabled={true}`', () => {
      render(<TextInput disabled />);

      expect(screen.getByRole('textbox')).toBeDisabled();
    });
  });
});
