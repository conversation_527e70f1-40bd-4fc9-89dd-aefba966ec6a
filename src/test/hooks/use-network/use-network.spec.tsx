// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { renderHook, act } from '@testing-library/react';
import { useWindowEvent } from '@/hooks/use-window-event';
import { useNetwork } from '@/hooks/use-network';

interface NetworkStatus {
  downlink?: number;
  downlinkMax?: number;
  effectiveType?: 'slow-2g' | '2g' | '3g' | '4g';
  rtt?: number;
  saveData?: boolean;
  type?:
    | 'bluetooth'
    | 'cellular'
    | 'ethernet'
    | 'wifi'
    | 'wimax'
    | 'none'
    | 'other'
    | 'unknown';
}

const NetworkInformation: NetworkStatus = {
  downlink: 1.5,
  downlinkMax: 2.5,
  effectiveType: '3g',
  rtt: 2,
  saveData: true,
  type: 'wifi',
};

describe('hooks/use-network/use-network.spec.tsx', () => {
  describe('A11y', () => {
    it('should addEventListener navigator.connection', () => {
      const addEventListener = jest.fn();
      const removeEventListener = jest.fn();

      Object.defineProperty(window, 'navigator', {
        value: {
          online: true,
          connection: {
            ...NetworkInformation,
            addEventListener: addEventListener,
            removeEventListener: removeEventListener,
          },
        },
        writable: true,
      });

      renderHook(() => useNetwork());

      expect(addEventListener).toHaveBeenCalledTimes(1);
    });

    it('should handle status when online', () => {
      Object.defineProperty(window, 'navigator', {
        value: {
          online: true,
        },
      });

      renderHook(() => useNetwork());
      const type = 'online';
      const listener = jest.fn();

      renderHook(() => useWindowEvent(type, listener));

      act(() => {
        window.dispatchEvent(new Event(type));
      });

      expect(listener).toHaveBeenCalledTimes(1);
    });

    it('should handle status when online', () => {
      Object.defineProperty(window, 'navigator', {
        value: {
          online: true,
        },
      });

      renderHook(() => useNetwork());
      const type = 'offline';
      const listener = jest.fn();

      renderHook(() => useWindowEvent(type, listener));

      act(() => {
        window.dispatchEvent(new Event(type));
      });

      expect(listener).toHaveBeenCalledTimes(1);
    });
  });
});
