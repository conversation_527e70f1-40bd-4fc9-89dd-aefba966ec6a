// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { useState } from 'react';
import { useRouter } from 'next/router';
import { isArray, isNil } from 'lodash';

export interface useDetectTabProps {
  /** query params name from url */
  name: string;
  /** define tab list */
  tabs: string[];
  /** define default tab value */
  defaultTab: string;
}

export const useDetectTab = ({ name, tabs, defaultTab }: useDetectTabProps) => {
  const router = useRouter();

  const handleDetectTab = (): string => {
    const tabQuery = router.query[name];
    if (isNil(tabQuery)) return defaultTab;
    const tabString = isArray(tabQuery) ? tabQuery[0] : tabQuery;

    return tabs.includes(tabString as string) ? tabString : defaultTab;
  };

  const [tabValue, setTabValue] = useState(() => handleDetectTab());

  return { tabValue, setTabValue };
};
