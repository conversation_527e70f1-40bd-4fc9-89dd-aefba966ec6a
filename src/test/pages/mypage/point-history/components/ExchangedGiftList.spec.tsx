// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { act, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { EXCHANGED_GIFT_LIST } from '@/mocks/exchange-points';
import exchangePointsApi from '@/services/internal/modules/exchange-points';
import { ExchangedGiftList } from '@/pages/mypage/point-history/components/ExchangedGiftList';

describe('pages/mypage/point-history/components/ExchangedGiftList.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document with wrap container', async () => {
      render(<ExchangedGiftList />);
      const href = screen.getByTestId('loading');

      expect(href).toBeInTheDocument();
    });
  });

  describe('Fetching api', () => {
    describe('Fetching success', () => {
      it('should call api success return response exchanged gift list', async () => {
        jest
          .spyOn(exchangePointsApi, 'getExchangedGiftList')
          .mockResolvedValue(EXCHANGED_GIFT_LIST);

        render(<ExchangedGiftList />);

        await waitFor(() => {
          const heading = screen.getAllByRole('heading');
          expect(heading.length).toEqual(3);
        });
      });
    });

    describe('Fetching failure', () => {
      it('should call api failure return error', async () => {
        const user = userEvent.setup();
        jest
          .spyOn(exchangePointsApi, 'getExchangedGiftList')
          .mockRejectedValue({ statusCode: 500 });

        render(<ExchangedGiftList />);

        await waitFor(async () => {
          const button = screen.getAllByRole('button');
          await act(() => user.click(button[0]));
          expect(button.length).toEqual(1);
        });
      });
    });
  });
});
