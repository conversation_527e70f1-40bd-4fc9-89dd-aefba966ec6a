// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { Logo } from '@/components/Logo';
import { Button } from '@/components/Button';
import { BoxContent } from '@/components/BoxContent';
import { redirectToLineChatRoomOA } from '@/utils/redirect';
import { useLiff } from '@/contexts/liff';

const BlockedErrorScreen: FC = () => {
  const { liff } = useLiff();

  return (
    <>
      <div className="container mx-auto">
        <BoxContent>
          <BoxContent.Body>
            <div className="pt-[96px]">
              <Logo box className="mb-4" />
              <div className="text-center">
                <h3 className="text-sm font-bold mb-2">
                  お客さまはすでに退会済です。
                </h3>
              </div>
            </div>
          </BoxContent.Body>
          <BoxContent.Footer>
            <Button
              fullSized
              type="submit"
              size="xl"
              color="primary"
              onClick={() => redirectToLineChatRoomOA(liff)}
            >
              トーク画面へ戻る
            </Button>
          </BoxContent.Footer>
        </BoxContent>
      </div>
    </>
  );
};

export default BlockedErrorScreen;
