// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
// eslint-disable-next-line import/no-extraneous-dependencies
import { action } from '@storybook/addon-actions';
import type { <PERSON><PERSON>, <PERSON> } from '@storybook/react';
import { Button } from '../Button';
import type { IModalProps } from './Modal';
import { Modal } from './Modal';

export default {
  title: 'Components/Modal',
  component: Modal,
  args: {
    show: false,
  },
} as Meta;

const Template: Story<IModalProps> = ({ children, ...rest }): JSX.Element => {
  return (
    <>
      <Button onClick={action('open')}>Toggle modal</Button>
      <Modal onClose={action('close')} {...rest}>
        {children}
      </Modal>
    </>
  );
};

export const Default = Template.bind({});
Default.args = {
  children: (
    <>
      <Modal.Header>
        <h3 className="text-theme-main text-lg text-center font-bold">
          Terms of Service
        </h3>
      </Modal.Header>
      <Modal.Body>
        <div className="space-y-6">
          <p className="text-base leading-relaxed text-gray-500 dark:text-gray-400">
            With less than a month to go before the European Union enacts new
            consumer privacy laws for its citizens, companies around the world
            are updating their terms of service agreements to comply.
          </p>
          <p className="text-base leading-relaxed text-gray-500 dark:text-gray-400">
            The European Union’s General Data Protection Regulation (G.D.P.R.)
            goes into effect on May 25 and is meant to ensure a common set of
            data rights in the European Union. It requires organizations to
            notify users as soon as possible of high-risk data breaches that
            could personally affect them.
          </p>
        </div>
      </Modal.Body>
      <Modal.Footer className="space-y-2">
        <Button fullSized size="lg" onClick={action('close')}>
          I accept
        </Button>
        <Button fullSized size="lg" color="gray" onClick={action('close')}>
          Decline
        </Button>
      </Modal.Footer>
    </>
  ),
};

export const PopUp = Template.bind({});
PopUp.storyName = 'Pop-up modal';
PopUp.args = {
  children: (
    <Modal.Body>
      <div className="text-center p-6">
        <h3 className="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
          Are you sure you want to delete this product?
        </h3>
        <div className="flex justify-center gap-4">
          <Button color="failure" onClick={action('close')}>
            {"Yes, I'm sure"}
          </Button>
          <Button color="info" onClick={action('close')}>
            No, cancel
          </Button>
        </div>
      </div>
    </Modal.Body>
  ),
};
