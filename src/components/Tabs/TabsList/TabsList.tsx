// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import classNames from 'classnames';
import React, { forwardRef } from 'react';
import { styles } from './TabsList.styles';
import { useTabsContext } from '../Tabs.context';
import { TabsPosition, TabsVariant } from '../Tabs';

export interface IThemeTabsList {
  base: string;
  styles: Record<TabsVariant, string>;
  position: Record<TabsPosition, string>;
}

export interface TabsListProps extends React.ComponentPropsWithoutRef<'div'> {
  /** <Tabs.Tab /> components */
  children: React.ReactNode;

  /** Determines whether tabs should take the whole space */
  grow?: boolean;

  /** Tabs alignment */
  position?: TabsPosition;

  /** Tabs class */
  className?: string;
}

export const TabsList = forwardRef<HTMLDivElement, TabsListProps>(
  ({ children, className, position = 'left', ...others }, ref) => {
    const { variant } = useTabsContext();

    return (
      <div
        {...others}
        className={classNames(
          styles.base,
          styles.styles[variant],
          styles.position[position],
          className
        )}
        ref={ref}
        role="tablist"
      >
        {children}
      </div>
    );
  }
);

TabsList.displayName = 'TabsList';
