// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import classNames from 'classnames';
import { forwardRef } from 'react';
import type { ComponentProps, FC, ReactNode } from 'react';
import { IThemeBoolean, IThemeColors, IThemeSizes } from '@/types/theme';
import { styles } from './TextInput.styles';
import { HelperText } from '../HelperText';

export interface ITextInputTheme {
  base: string;
  field: {
    base: string;
    icon: {
      base: string;
      svg: string;
    };
    rightIcon: {
      base: string;
      svg: string;
    };
    input: {
      base: string;
      sizes: ITextInputSizes;
      colors: ITextInputColors;
      withIcon: IThemeBoolean;
      withRightIcon: IThemeBoolean;
      withShadow: IThemeBoolean;
    };
    interactableIcon: string;
  };
}

export interface ITextInputColors extends IThemeColors {
  [key: string]: string;
  default: string;
}

export interface ITextInputSizes
  extends Pick<IThemeSizes, 'xs' | 'sm' | 'md' | 'lg'> {
  [key: string]: string;
}

export interface ITextInputProps
  extends Omit<ComponentProps<'input'>, 'ref' | 'color'> {
  sizing?: keyof ITextInputSizes;
  shadow?: boolean;
  helperText?: ReactNode;
  addon?: ReactNode;
  icon?: FC<ComponentProps<'svg'>>;
  rightIcon?: FC<ComponentProps<'svg'>>;
  color?: keyof ITextInputColors;
  interactableIcon?: ReactNode;
}

export const TextInput = forwardRef<HTMLInputElement, ITextInputProps>(
  (
    {
      sizing = 'md',
      shadow,
      helperText,
      addon,
      icon: Icon,
      rightIcon: RightIcon,
      color = 'default',
      interactableIcon,
      className,
      ...props
    },
    ref
  ) => {
    const theme = styles;

    return (
      <>
        <div className={classNames(theme.base, className)}>
          <div className={theme.field.base}>
            {Icon && (
              <div className={theme.field.icon.base}>
                <Icon className={theme.field.icon.svg} />
              </div>
            )}
            {RightIcon && (
              <div
                data-testid="right-icon"
                className={theme.field.rightIcon.base}
              >
                <RightIcon className={theme.field.rightIcon.svg} />
              </div>
            )}
            <input
              className={classNames(
                theme.field.input.base,
                theme.field.input.colors[color],
                theme.field.input.withIcon[Icon ? 'on' : 'off'],
                theme.field.input.withShadow[shadow ? 'on' : 'off'],
                theme.field.input.sizes[sizing]
              )}
              {...props}
              ref={ref}
            />
            {interactableIcon && (
              <div className={theme.field.interactableIcon}>
                {interactableIcon}
              </div>
            )}
          </div>
        </div>
        {helperText && <HelperText color={color}>{helperText}</HelperText>}
      </>
    );
  }
);

TextInput.displayName = 'TextInput';
