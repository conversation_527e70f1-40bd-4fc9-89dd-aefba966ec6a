// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import classNames from 'classnames';
import React, { forwardRef } from 'react';
import { useTabsContext } from '../Tabs.context';
import { styles } from './TabsPanel.styles';

export interface IThemeTabsPanel {
  base: string;
}

export interface TabsPanelProps extends React.ComponentPropsWithoutRef<'div'> {
  /** Value of associated control */
  value: string;

  /** TabsPanel class */
  className?: string;

  /** Panel content */
  children: React.ReactNode;
}

export const TabsPanel = forwardRef<HTMLDivElement, TabsPanelProps>(
  (props, ref) => {
    const { value, children, className, ...others } = props;
    const ctx = useTabsContext();
    const active = ctx.value === value;

    return (
      <div
        ref={ref}
        role="tabpanel"
        hidden={!active}
        id={ctx.getPanelId(value)}
        aria-labelledby={ctx.getTabId(value)}
        className={classNames(styles.base, className)}
        {...others}
      >
        {children}
      </div>
    );
  }
);

TabsPanel.displayName = 'TabsPanel';
