// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { act, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import FriendshipErrorScreen from '@/pages/error/friendship.page';
import { LiffContextMockProviders } from '@/test/mocks';
import { Message } from '@/constants/message';

jest.mock('next/router', () => require('next-router-mock'));

const TestFriendshipErrorScreen = (): JSX.Element => {
  const liffContextValue = {
    error: { message: Message.error.friendNotAdd },
  };

  return (
    <LiffContextMockProviders contextValue={liffContextValue}>
      <FriendshipErrorScreen />
    </LiffContextMockProviders>
  );
};

describe('pages/error/friendship.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<TestFriendshipErrorScreen />);

      expect(screen.getByText('ピアコネ会員登録の流れ')).toBeInTheDocument();
    });
  });

  describe('User interactions', () => {
    it('should trigger `onClick` button', async () => {
      const user = userEvent.setup();

      render(<TestFriendshipErrorScreen />);

      const openButton = screen.getByRole('button');

      const href = 'https://example.com';
      jest
        .spyOn(window, 'location', 'get')
        .mockImplementation(() => ({ href } as Location));

      await act(() => user.click(openButton));

      expect(window.location.href).toEqual(href);
    });
  });
});
