// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { useLiff } from '@/contexts/liff';
import Loading from '@/components/Spinner/Loading';
import MypageLayout from './components/MypageLayout';

const InquiryScreen: FC = () => {
  const { isLoggedIn, userInfo } = useLiff();

  return (
    <>
      {!isLoggedIn ? (
        <Loading />
      ) : (
        <MypageLayout withContainer={false} spacingTop="none">
          <iframe
            className="w-full h-full flex-grow"
            src={`https://form.run/embed/@td-holdings-1677202097?embed=direct&output=embed&_field_4=${userInfo.email}`}
          />
        </MypageLayout>
      )}
    </>
  );
};

export default InquiryScreen;
