// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import React, { FC } from 'react';
import { act, fireEvent, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { IUserInfo } from '@/types/user';
import RegisterScreen from '@/pages/register/index.page';
import { LiffContextMockProviders, MockLiffProvidersProps } from '@/test/mocks';
import {
  USER_INFO_REGISTER,
  USER_PREFECTURE_SELECTED,
  USER_REGISTER_ERRORS,
} from '@/constants/user';
import {
  USER_REGISTER_FORM,
  USER_REGISTER_ERROR,
  USER_PREFECTURE_SELECTED_FORM,
  USER_INFO,
} from '@/mocks/users';
import { useRouter } from 'next/router';

jest.mock('next/router', () => ({
  useRouter: jest.fn(),
  __esModule: true,
  default: {
    push: jest.fn(),
    replace: jest.fn(),
    asPath: '/checkin',
  },
}));

interface TestRegisterScreenProps {
  userInfo?: IUserInfo;
}

const TestRegisterScreen: FC<TestRegisterScreenProps> = ({
  userInfo = {} as IUserInfo,
}) => {
  const liffContextValue: MockLiffProvidersProps = {
    userInfo,
  };

  return (
    <LiffContextMockProviders contextValue={liffContextValue}>
      <RegisterScreen />
    </LiffContextMockProviders>
  );
};

describe('pages/register/index.page.spec.tsx', () => {
  const store: Record<string, string> = {
    [USER_INFO_REGISTER]: JSON.stringify(USER_REGISTER_FORM),
    [USER_REGISTER_ERRORS]: JSON.stringify(USER_REGISTER_ERROR),
    [USER_PREFECTURE_SELECTED]: JSON.stringify(USER_PREFECTURE_SELECTED_FORM),
  };
  beforeEach(() => {
    // mock a return value on sessionStorage
    Object.defineProperty(window, 'sessionStorage', {
      value: {
        getItem: jest.fn(key => store[key]),
        setItem: jest.fn((key, value) => {
          store[key] = value;
        }),
        removeItem: jest.fn(key => delete store[key]),
      },
      writable: true,
    });
  });

  describe('A11y', () => {
    it('should render in the document', async () => {
      useRouter.mockReturnValue({
        query: {},
      });

      render(<TestRegisterScreen />);

      expect(screen.getByRole('img')).toBeInTheDocument();
    });
  });

  describe('Redirect', () => {
    it('should open privacy modal when has query show=privacy', async () => {
      useRouter.mockReturnValue({
        push: jest.fn(),
        query: {
          show: 'privacy',
        },
      });

      render(<TestRegisterScreen />);

      expect(useRouter).toHaveBeenCalled();
    });

    it('should open privacy modal when has query show=terms', async () => {
      useRouter.mockReturnValue({
        push: jest.fn(),
        query: {
          show: 'terms',
        },
      });

      render(<TestRegisterScreen />);

      expect(useRouter).toHaveBeenCalled();
    });

    it('should do not open modal when has query show=lorem', async () => {
      useRouter.mockReturnValue({
        push: jest.fn(),
        query: {
          show: 'lorem',
        },
      });

      render(<TestRegisterScreen />);

      expect(useRouter).toHaveBeenCalled();
    });

    it('should open privacy and terms modal when has query show=[privacy, terms]', async () => {
      useRouter.mockReturnValue({
        push: jest.fn(),
        query: {
          show: ['privacy', 'terms'],
        },
      });

      render(<TestRegisterScreen />);

      useRouter.mockReturnValue({
        push: jest.fn(),
        query: {
          show: ['lorem', 'terms'],
        },
      });

      expect(useRouter).toHaveBeenCalled();
    });

    it('should open privacy modal when has query show=[privacy, lorem]', async () => {
      useRouter.mockReturnValue({
        push: jest.fn(),
        query: {
          show: ['lorem', 'terms'],
        },
      });

      render(<TestRegisterScreen />);

      expect(useRouter).toHaveBeenCalled();
    });

    it('should redirect to registered when user signed in', async () => {
      useRouter.mockReturnValue({
        push: jest.fn(),
        query: {},
      });

      render(<TestRegisterScreen userInfo={USER_INFO} />);

      expect(useRouter).toHaveBeenCalled();
    });
  });

  describe('User interactions', () => {
    it('should trigger register button', async () => {
      const user = userEvent.setup();
      useRouter.mockReturnValue({
        push: jest.fn(),
        query: {},
      });

      render(<TestRegisterScreen />);

      // input email
      fireEvent.input(screen.getByRole('textbox'), {
        target: {
          value: '<EMAIL>',
        },
      });
      // input password
      fireEvent.input(
        screen.getByLabelText('パスワード（半角英数字8文字以上20文字以内）'),
        {
          target: {
            value: 'Aa@123456789',
          },
        }
      );
      // input confirm password
      fireEvent.input(screen.getByLabelText('パスワード再入力'), {
        target: {
          value: 'Aa@123456789',
        },
      });
      // select prefecture
      fireEvent.input(screen.getByLabelText('お住いの都道府県'), {
        target: {
          value: '青森県',
        },
      });
      // select birth month
      fireEvent.input(screen.getByRole('option', { name: '6月' }), {
        target: {
          value: '6',
        },
      });
      // select birth year
      fireEvent.input(screen.getByRole('option', { name: '滋賀県' }), {
        target: {
          value: '2000',
        },
      });
      // select gender
      const buttonGender = screen.getByRole('button', { name: '男性' });
      await act(() => user.click(buttonGender));
      // click button policy
      const buttonPolicy = screen.getByRole('button', {
        name: 'プライバシーポリシーに同意する',
      });
      await act(() => user.click(buttonPolicy));
      // click button terms
      const buttonTerms = screen.getByRole('button', {
        name: '会員規約に同意する',
      });
      await act(() => user.click(buttonTerms));
      // click button register
      const button = screen.getByRole('button', { name: '確認画面に進む' });
      await act(() => user.click(button));

      expect(button).toHaveFocus();
    });
  });
});
