// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
export const ExchangeableGiftDetailLoading = () => {
  return (
    <div className="animate-pulse" data-testid="loading">
      <div className="w-full pt-[100%] bg-slate-200 rounded" />
      <div className="space-y-1.5 mt-6">
        <div className="w-4/5 h-5 bg-slate-200 rounded" />
        <div className="space-y-1">
          <div className="w-full h-6 bg-slate-200 rounded" />
          <div className="w-3/4 h-6 bg-slate-200 rounded" />
        </div>
      </div>
      <div className="flex items-center gap-4 mt-4">
        <div className="w-1/2 h-6 bg-slate-200 rounded" />
      </div>
      <div className="mt-4">
        <div className="w-1/3 h-6 bg-slate-200 rounded mb-1.5" />
        <div className="space-y-1">
          <div className="w-1/2 h-3 bg-slate-200 rounded" />
          <div className="w-2/3 h-3 bg-slate-200 rounded" />
          <div className="w-3/4 h-3 bg-slate-200 rounded" />
        </div>
      </div>
      <div className="mt-4 space-y-4">
        <div className="w-1/3 h-5 bg-slate-200 rounded" />
        <div className="w-1/3 h-5 bg-slate-200 rounded" />
      </div>
    </div>
  );
};
