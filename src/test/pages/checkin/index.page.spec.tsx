// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import type { Liff } from '@line/liff';
import authApi from '@/services/internal/modules/auth';
import { LiffContextMockProviders } from '@/test/mocks';
import CheckinReadQrScreen from '@/pages/checkin/index.page';
import {
  CHECKIN_RESPONSE_FAILURE,
  CHECKIN_RESPONSE_SUCESS,
} from '@/mocks/checkin';

jest.mock('next/router', () => require('next-router-mock'));
// eslint-disable-next-line @typescript-eslint/no-var-requires
const useRouter = jest.spyOn(require('next/router'), 'useRouter');

const useMockRouter = (query: Record<string, string | string[]> = {}) => {
  return useRouter.mockReturnValue({
    query,
    push: jest.fn(),
  });
};

const queryParams = {
  company_id: 'lorem',
  agency_id: 'ipsum',
  agency_token: 'dolor',
};

interface TestCheckinReadQrScreenProps {
  scanCodeV2Result?: string;
}

const TestCheckinReadQrScreen: FC<TestCheckinReadQrScreenProps> = ({
  scanCodeV2Result,
}) => {
  const contextValue = {
    liff: {
      getContext: () => ({}),
      openWindow: () => ({}),
      scanCodeV2: () => ({
        value: scanCodeV2Result,
      }),
    } as unknown as Liff,
  };

  return (
    <LiffContextMockProviders contextValue={contextValue}>
      <CheckinReadQrScreen />
    </LiffContextMockProviders>
  );
};

describe('pages/checkin/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      useMockRouter({ is_active: 'true' });

      render(<TestCheckinReadQrScreen />);
      const heading = screen.getByRole('status');

      await waitFor(() => {
        expect(heading).toBeInTheDocument();
      });
    });
  });

  describe('Fetching success', () => {
    beforeEach(() => {
      // mock response from api
      jest.spyOn(authApi, 'checkin').mockResolvedValue(CHECKIN_RESPONSE_SUCESS);
    });

    it('should call api return response with liff scanCodeV2 correct', async () => {
      useMockRouter();

      const scanCodeV2Result =
        'https://example.com/checkin/?company_id=lorem&agency_id=ipsum&agency_token=dolor';
      render(<TestCheckinReadQrScreen scanCodeV2Result={scanCodeV2Result} />);
      const heading = screen.getByRole('heading');

      await waitFor(() => {
        expect(heading).toBeInTheDocument();
      });
    });

    it('should call api return response with liff scanCodeV2 incorrect', async () => {
      useMockRouter();

      const scanCodeV2Result =
        'https://example.com/checkin/?company_id=lorem&agency_id=ipsum&agency_token1=dolor';
      render(<TestCheckinReadQrScreen scanCodeV2Result={scanCodeV2Result} />);
      const button = screen.getByRole('button');
      fireEvent.click(button);

      await waitFor(() => {
        expect(button).toBeInTheDocument();
      });
    });

    it('should call api return response with checkin url', async () => {
      useMockRouter(queryParams);

      render(<TestCheckinReadQrScreen />);
      const status = screen.getByRole('status');

      await waitFor(() => {
        expect(status).toBeInTheDocument();
      });
    });
  });

  describe('Fetching failure', () => {
    beforeEach(() => {
      // mock response from api
      jest
        .spyOn(authApi, 'checkin')
        .mockRejectedValue(CHECKIN_RESPONSE_FAILURE);
    });

    it('should call api return statusCode=500', async () => {
      useMockRouter(queryParams);

      render(<TestCheckinReadQrScreen />);
      const status = screen.getByRole('status');

      await waitFor(() => {
        expect(status).toBeInTheDocument();
      });
    });
  });
});
