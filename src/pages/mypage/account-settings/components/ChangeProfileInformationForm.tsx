// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import * as yup from 'yup';
import punycode from 'punycode';
import isEmail from 'validator/lib/isEmail';
import { yupResolver } from '@hookform/resolvers/yup';
import { Button } from '@/components/Button';
import { Label } from '@/components/Label';
import { Select } from '@/components/Select';
import { TextInput } from '@/components/TextInput';
import { HelperText } from '@/components/HelperText';
import { Spinner } from '@/components/Spinner';
import {
  IChangeUserInformationForm,
  IUserGender,
  IUserUpdatePayload,
  IUserUpdateResponse,
} from '@/types/user';
import {
  BIRTH_MONTHS,
  BIRTH_YEARS,
  USER_GENDERS_LIST,
  EMAIL,
  BIRTH_MONTH_YEAR,
  PREFECTURE,
  GENDER,
} from '@/constants/user';
import { PREFECTURES } from '@/constants/prefectures';
import { useLiff } from '@/contexts/liff';
import authApi from '@/services/internal/modules/auth';
import { templateString } from '@/utils/text';
import { convertToISOFormat, extractYearAndMonth } from '@/utils/time';
import { IErrorsResponse, IIErrorDetailResponse } from '@/types/errors';
import { toast } from 'react-toastify';
import { useFetch } from '@/hooks/use-fetch';
import { Message } from '@/constants/message';
import { EMAIL_EXCLUDE_NON_ASCII_REGEX } from '@/constants/regex';

const schema = yup.object({
  email: yup
    .string()
    .required(templateString(Message.validation.required, { field: EMAIL }))
    .test('email', Message.validation.emailFormatInvalid, (value?: string) => {
      return isEmail(value || '');
    })
    .test('email', Message.validation.emailFormatInvalid, (value?: string) => {
      return EMAIL_EXCLUDE_NON_ASCII_REGEX.test(
        // resolve bug https://stackoverflow.com/questions/32117497/input-type-email-value-in-chrome-with-accented-characters-wrong
        punycode.toUnicode(value || '')
      );
    }),
  birthYear: yup
    .string()
    .required(
      templateString(Message.validation.required, { field: BIRTH_MONTH_YEAR })
    ),
  birthMonth: yup
    .string()
    .required(
      templateString(Message.validation.required, { field: BIRTH_MONTH_YEAR })
    ),
  prefecture: yup
    .string()
    .required(
      templateString(Message.validation.required, { field: PREFECTURE })
    ),
  gender: yup
    .string()
    .required(templateString(Message.validation.required, { field: GENDER })),
});

const ChangeProfileInformationForm: FC = () => {
  const { userInfo, setUserInfo } = useLiff();

  const [birthYear, birthMonth] = extractYearAndMonth(userInfo.birthday);

  const { email, prefecture, gender } = userInfo;

  const {
    register,
    handleSubmit,
    getValues,
    setError,
    reset,
    control,
    formState: { errors, isValid, isDirty: isFormEdited },
  } = useForm<IChangeUserInformationForm>({
    mode: 'onChange',
    resolver: yupResolver(schema),
    defaultValues: {
      email,
      prefecture,
      gender,
      birthYear,
      birthMonth,
    },
  });

  const handleResponse = (res: IUserUpdateResponse) => {
    reset(getValues());
    setUserInfo({ ...userInfo, ...res });
    toast.success(Message.success.updateProfile);
    return res;
  };

  const handleError = (error: unknown) => {
    const { errors: errorArr } = error as IErrorsResponse;

    if (!errorArr || !errorArr.length) return;

    errorArr.forEach((err: IIErrorDetailResponse) => {
      if (err.property !== 'email') return;

      const { UpdateEmailUnique } = err.constraints;

      if (!UpdateEmailUnique) return;

      setError('email', {
        type: 'server',
        message: Message.validation.emailExisted,
      });
    });
  };

  const { fetch: updateUser, isFetching } = useFetch<
    IUserUpdateResponse,
    IUserUpdatePayload,
    IErrorsResponse | IUserUpdateResponse
  >({
    fetcher: authApi.updateUser,
    handleResponse,
    handleError,
    dependencies: false,
  });

  useWatch({
    name: 'gender',
    control,
  });

  const changeProfileInformation = (data: IChangeUserInformationForm) => {
    // eslint-disable-next-line @typescript-eslint/no-shadow
    const { email, birthMonth, birthYear, gender, prefecture } = data;
    const { line_user_id } = userInfo;
    updateUser({
      memberLineId: line_user_id,
      email,
      gender,
      prefecture,
      birthday: convertToISOFormat(birthMonth, birthYear),
    });
  };

  return (
    <>
      <form
        noValidate
        className="flex flex-grow flex-col justify-between h-full"
        onSubmit={handleSubmit(changeProfileInformation)}
      >
        <div className="space-y-6">
          {/* Email */}
          <div>
            <Label htmlFor="email" className="block mb-2">
              メールアドレス
            </Label>
            <TextInput
              type="email"
              id="email"
              placeholder="（例）<EMAIL>"
              {...register('email')}
              color={errors.email ? 'failure' : 'default'}
              helperText={errors.email?.message}
            />
          </div>
          {/* Birthday */}
          <div>
            <Label className="block mb-2">生まれた年・月</Label>
            <div className="grid grid-cols-12 gap-2">
              <div className="col-span-6">
                <Select
                  id="birthYear"
                  required
                  defaultValue=""
                  {...register('birthYear')}
                  color={errors.birthYear ? 'failure' : 'default'}
                >
                  <option value="" disabled>
                    年
                  </option>

                  {BIRTH_YEARS.map(year => (
                    <option key={year} value={year}>
                      {year}年
                    </option>
                  ))}
                </Select>
              </div>
              <div className="col-span-6">
                <Select
                  id="birthMonth"
                  required
                  defaultValue=""
                  {...register('birthMonth')}
                  color={errors.birthMonth ? 'failure' : 'default'}
                >
                  <option value="" disabled>
                    月
                  </option>
                  {BIRTH_MONTHS.map(month => (
                    <option key={month} value={month}>
                      {month}月
                    </option>
                  ))}
                </Select>
              </div>
            </div>
            {(errors.birthYear?.message || errors.birthMonth?.message) && (
              <HelperText
                value={templateString(Message.validation.required, {
                  field: BIRTH_MONTH_YEAR,
                })}
                color="failure"
              />
            )}
          </div>
          {/* Prefecture */}
          <div>
            <Label htmlFor="prefecture" className="block mb-2">
              お住いの都道府県
            </Label>
            <Select
              required
              id="prefecture"
              defaultValue=""
              {...register('prefecture')}
              color={errors.prefecture ? 'failure' : 'default'}
              helperText={errors.prefecture?.message}
            >
              <option value="" disabled>
                都道府県を選択してください
              </option>
              {PREFECTURES.map((pref: string) => (
                <option key={pref} value={pref}>
                  {pref}
                </option>
              ))}
            </Select>
          </div>
          {/* Gender */}
          <div>
            <Label className="block mb-2">性別</Label>
            <div className="grid grid-cols-12 gap-1">
              {USER_GENDERS_LIST.map((gen: IUserGender) => (
                <div className="col-span-3" key={gen.id}>
                  <Button
                    outline={getValues('gender') !== gen.value}
                    fullSized
                    size="xl"
                    color="primary-gray"
                    type="button"
                    className="relative"
                    customSpan="!px-0"
                  >
                    <Label htmlFor={gen.id} className="absolute w-full h-full">
                      <input
                        {...register('gender')}
                        type="radio"
                        value={gen.value}
                        className="hidden"
                        id={gen.id}
                      ></input>
                    </Label>
                    <span
                      className={
                        getValues('gender') === gen.value
                          ? 'font-bold text-white'
                          : 'font-normal'
                      }
                    >
                      {gen.label}
                    </span>
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </div>
        {/* Submit */}
        <div className="pt-8">
          <Button
            fullSized
            type="submit"
            size="xl"
            color={!isValid || !isFormEdited ? 'gray-10' : 'primary'}
            disabled={!isValid || !isFormEdited || isFetching}
          >
            {isFetching && (
              <div className="mr-3">
                <Spinner size="lg" color="primary" />
              </div>
            )}
            変更する
          </Button>
        </div>
      </form>
    </>
  );
};

export default ChangeProfileInformationForm;
