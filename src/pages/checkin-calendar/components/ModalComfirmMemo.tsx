// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { Button } from '@/components/Button';
import { Modal } from '@/components/Modal';
import React, { Dispatch, SetStateAction } from 'react';
interface ModalComfirmMemoProps {
  setIsOpenModalConfirm: Dispatch<SetStateAction<boolean>>;
  isOpenModalConfirm: boolean;
  onCancelEdit: (allowEdit: boolean) => void;
}

const ModalComfirmMemo: React.FC<ModalComfirmMemoProps> = ({
  isOpenModalConfirm,
  setIsOpenModalConfirm,
  onCancelEdit,
}) => {
  return (
    <Modal
      size="sm"
      show={isOpenModalConfirm}
      innerClass="max-h-[75%] overflow-y-auto"
    >
      <Modal.Header>
        <h3 className="text-lg text-center font-bold">
          編集中のテキストがあります
        </h3>
      </Modal.Header>
      <Modal.Body>
        <div className="p-5">
          <p className="text-sm font-medium">
            保存せずに編集を終了しますか？
            <br />
            その場合、編集内容は保存されません。
          </p>
        </div>
      </Modal.Body>
      <Modal.Footer className=" pt-4 px-5 pb-5">
        <div className="flex items-center justify-between">
          <Button
            outline
            size="xl"
            color="dark"
            onClick={() => onCancelEdit(true)}
          >
            保存して終了する
          </Button>
          <Button
            color="calendar-orange"
            size="xl"
            onClick={() => onCancelEdit(false)}
          >
            保存せず終了する
          </Button>
        </div>
        <Button
          className="mt-4 block mx-auto"
          customSpan="!py-0 !px-0 hover:text-theme-main"
          size="xl"
          color="link"
          onClick={() => setIsOpenModalConfirm(false)}
        >
          編集をつづける
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ModalComfirmMemo;
