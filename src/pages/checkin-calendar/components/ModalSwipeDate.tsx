// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { Button } from '@/components/Button';
import { Modal } from '@/components/Modal';
import { SvgIcon } from '@/components/SvgIcon';
import { IDateMemo } from '@/types/checkin-calendar';
import dayjs from 'dayjs';
import React, {
  Dispatch,
  MutableRefObject,
  SetStateAction,
  useEffect,
  useState,
} from 'react';
import SwiperCore from 'swiper';
import { Swiper, SwiperSlide } from 'swiper/react';
interface ModalSwipeDateProps {
  isOpenModal: boolean;
  setIsOpenModal: Dispatch<SetStateAction<boolean>>;
  onCloseModal: () => void;
  isEditable: boolean;
  onEditable: (allowEdit: boolean) => void;
  activeDateMemo: string;
  handleSlideChange: (swiper: SwiperCore) => void;
  swiperRef: MutableRefObject<SwiperCore | null>;
  dates: IDateMemo[];
  setIsOpenModalConfirm: Dispatch<SetStateAction<boolean>>;
  memoValue: string;
  onChangeMemoValue: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
}

const ModalSwipeDate: React.FC<ModalSwipeDateProps> = ({
  isOpenModal,
  setIsOpenModal,
  onCloseModal,
  isEditable,
  onEditable,
  activeDateMemo,
  handleSlideChange,
  swiperRef,
  dates,
  setIsOpenModalConfirm,
  memoValue,
  onChangeMemoValue,
}) => {
  const [initialHeight, setInitialHeight] = useState<number | null>(null);

  useEffect(() => {
    if (isOpenModal) {
      if (window.visualViewport) {
        const height = window.visualViewport.height;
        setInitialHeight(height - 66);
      }
    }
  }, [isOpenModal]);

  return (
    <>
      {isOpenModal && (
        <Modal
          size="sm"
          show={isOpenModal}
          setShow={setIsOpenModal}
          onClose={onCloseModal}
          innerClass={`overflow-hidden !rounded-[12px]  h-[calc(100vh-66px)] items-start`}
          innerStyle2={{
            height: initialHeight ? `${initialHeight}px` : 'calc(100vh-66px)',
          }}
          className="items-start"
          style={{ alignItems: 'start', WebkitBoxAlign: 'start' }}
          innerStyle={{
            alignItems: 'start',
            WebkitBoxAlign: 'start',
            height: '100%',
            paddingTop: 33,
          }}
        >
          <div className="flex justify-between items-center px-4 py-2.5 bg-calendar-yellow">
            <Button
              color="calendar-orange"
              className={isEditable ? 'invisible' : ''}
              customSpan="!font-bold !px-3 !py-1 leading-[20px]"
              size="xs"
              onClick={() => onEditable(false)}
            >
              編集
            </Button>
            <h3 className="font-medium">
              {dayjs(activeDateMemo).format('YYYY年M月D日')}
            </h3>
            <span className="font-bold cursor-pointer" onClick={onCloseModal}>
              <SvgIcon name="XMark" />
            </span>
          </div>
          <Modal.Body className="px-0 h-[calc(100%-48px)]">
            <div className="relative h-full">
              <Swiper
                spaceBetween={50}
                slidesPerView={1}
                onSlideChange={handleSlideChange}
                onSwiper={(swiper: SwiperCore) => {
                  swiperRef.current = swiper;
                }}
                initialSlide={dates.findIndex(
                  date =>
                    dayjs(date.date).format('YYYY/MM/DD') ===
                    dayjs(activeDateMemo).format('YYYY/MM/DD')
                )}
                className="h-full"
                speed={700}
              >
                {dates.map(({ agencyName, memo }, index) => {
                  return (
                    <SwiperSlide key={index}>
                      <div className="h-full pb-4 overflow-hidden flex flex-col">
                        <div
                          className={`pt-5 px-5 bg-white shrink-0 ${
                            isEditable ? 'pt-2.5' : ''
                          }`}
                        >
                          <div
                            className={`space-y-4 pb-6 border-b-theme-main border-b border-solid ${
                              isEditable ? 'pb-2.5' : ''
                            }`}
                          >
                            <div className="flex items-center">
                              <div
                                className={`bg-calendar-yellow rounded flex items-center justify-center leading-5 font-medium mr-4 ${
                                  isEditable
                                    ? 'w-4 h-4 min-w-[16px] basis-4 text-[10px]'
                                    : 'w-8 h-8 min-w-[32px] basis-8'
                                }`}
                              >
                                1
                              </div>
                              <span
                                className={`break-word ${
                                  isEditable
                                    ? 'text-xs leading-[18px]'
                                    : 'text-base'
                                }`}
                              >
                                {agencyName?.[0] || 'ー'}
                              </span>
                            </div>
                            <div className="flex items-center">
                              <div
                                className={`bg-calendar-yellow rounded flex items-center justify-center leading-5 font-medium mr-4 ${
                                  isEditable
                                    ? 'w-4 h-4 min-w-[16px] basis-4 text-[10px]'
                                    : 'w-8 h-8 min-w-[32px] basis-8'
                                }`}
                              >
                                2
                              </div>
                              <span
                                className={`break-word ${
                                  isEditable
                                    ? 'text-xs leading-[18px]'
                                    : 'text-base'
                                }`}
                              >
                                {agencyName?.[1] || 'ー'}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div
                          className={`break-words px-4 grow min-h-0 overflow-auto flex flex-col ${
                            isEditable ? 'mt-2.5' : 'mt-5'
                          }`}
                        >
                          {memo && !isEditable && (
                            <p className="text-sm leading-[22px] whitespace-pre-wrap">
                              {memo}
                            </p>
                          )}
                          {isEditable && (
                            <>
                              <div className="flex items-center justify-end mb-2 shrink-0">
                                <Button
                                  color="dark"
                                  customSpan="!font-bold !px-3 !py-1 leading-[20px] hover:text-[#333333]"
                                  size="xs"
                                  onClick={() => {
                                    setIsOpenModalConfirm(true);
                                  }}
                                >
                                  キャンセル
                                </Button>
                                <Button
                                  color="calendar-orange"
                                  customSpan="!font-bold !px-3 !py-1 leading-[20px]"
                                  size="xs"
                                  onClick={() => onEditable(true)}
                                >
                                  保存
                                </Button>
                              </div>
                              <textarea
                                style={{
                                  resize: 'none',
                                }}
                                value={memoValue}
                                onChange={onChangeMemoValue}
                                className="bg-[#F8F8F8] focus:outline-none focus:border-[#EE7910] focus:[box-shadow:unset] w-full rounded border-[#EE7910] border-solid border-2 py-4 px-3 text-sm leading-[22px] font-normal grow"
                              />
                            </>
                          )}
                        </div>
                      </div>
                    </SwiperSlide>
                  );
                })}
              </Swiper>
            </div>
          </Modal.Body>
        </Modal>
      )}
    </>
  );
};

export default ModalSwipeDate;
