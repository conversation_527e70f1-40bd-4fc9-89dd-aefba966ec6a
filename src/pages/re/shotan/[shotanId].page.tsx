// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import Loading from '@/components/Spinner/Loading';
import { routers } from '@/constants/routes';
import { useRedirect } from '@/hooks/use-redirect';
import redirectApi from '@/services/internal/modules/redirect';
import router from 'next/router';

const RedirectShotanScreen = () => {
  useRedirect(async ({ query: { shotanId }, redirect }) => {
    try {
      const { url } = await redirectApi.verifyRedirectShotan({
        shotanId,
      });

      if (url) {
        redirect(url);
      }
    } catch (error) {
      router.push(routers.notFound);
    }
  });

  return <Loading />;
};

export default RedirectShotanScreen;
