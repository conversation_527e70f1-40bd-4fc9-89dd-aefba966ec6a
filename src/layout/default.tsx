// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import Head from 'next/head';
import { ComponentProps, FC, Fragment } from 'react';
import { useNetwork } from '@/hooks/use-network';
import Loading from '@/components/Spinner/Loading';
import NoWifiModal from '@/components/Modal/NoWifiModal';
import { MetaSeoDetail } from '@/types/app';
import { NextSeo } from 'next-seo';

interface LayoutDefaultProps extends ComponentProps<'div'> {
  isReady: boolean;
  isLoading: boolean;
  meta: MetaSeoDetail;
}

const LayoutDefault: FC<LayoutDefaultProps> = ({
  isReady,
  isLoading,
  children,
  meta,
}) => {
  const { online } = useNetwork();

  return (
    <Fragment>
      <NextSeo {...meta} />
      <Head>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
        />
      </Head>
      {!isReady || isLoading ? (
        <Loading />
      ) : (
        <Fragment>
          {children}
          <NoWifiModal isShowModal={!online} />
        </Fragment>
      )}
    </Fragment>
  );
};

export default LayoutDefault;
