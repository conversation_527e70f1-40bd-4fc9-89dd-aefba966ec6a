// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { act, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Message } from '@/constants/message';
import { LiffContextMockProviders } from '@/test/mocks';
import BlockedErrorScreen from '@/pages/error/blocked.page';

jest.mock('next/router', () => require('next-router-mock'));

const TestBlockedErrorScreen = (): JSX.Element => {
  const liffContextValue = {
    error: { message: Message.error.friendNotAdd },
  };

  return (
    <LiffContextMockProviders contextValue={liffContextValue}>
      <BlockedErrorScreen />
    </LiffContextMockProviders>
  );
};

describe('pages/error/blocked.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<TestBlockedErrorScreen />);

      expect(screen.getByRole('img')).toBeInTheDocument();
    });
  });

  describe('User interactions', () => {
    it('should trigger `onClick` button', async () => {
      const user = userEvent.setup();

      render(<TestBlockedErrorScreen />);

      const openButton = screen.getByRole('button');

      const href = 'https://example.com';
      jest
        .spyOn(window, 'location', 'get')
        .mockImplementation(() => ({ href } as Location));

      await act(() => user.click(openButton));

      expect(window.location.href).toEqual(href);
    });
  });
});
