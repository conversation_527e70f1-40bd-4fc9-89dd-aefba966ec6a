stages:
  - build
  - deploy-dev

jobs:
  - name: build
    stage: build
    image: node:16-alpine
    script:
      - yarn install --frozen-lockfile
      - yarn check:format && yarn check:style && yarn check:lint
      - yarn build
    after_script:
      - echo "Build successfully"
    only:
      branches:
        - develop
        - staging
      events:
        - push
        - pull_request
    cache:
      - key:
          files:
            - yarn.lock
        paths:
          - node_modules
