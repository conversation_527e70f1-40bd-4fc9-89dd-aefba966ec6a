// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import classNames from 'classnames';
import React, { ComponentProps, FC } from 'react';
import { IThemeSizes } from '@/types/theme';
import { styles } from './CurrentPoint.styles';
import { CurrentPointLink } from './CurrentPointLink';
import { CurrentPointLabel } from './CurrentPointLabel';
import { CurrentPointAction } from './CurrentPointAction';
import { CurrentPointNote } from './CurrentPointNote';

type CurrentPointGap = Pick<IThemeSizes, 'sm' | 'md' | 'lg'>;
type CurrentPointPadding = Pick<IThemeSizes, 'sm' | 'md' | 'lg'>;

export interface CurrentPointProps extends ComponentProps<'div'> {
  /** Spacing items vertical */
  gap?: keyof CurrentPointGap;

  /** Padding component */
  padding?: keyof CurrentPointPadding;
}

const CurrentPointComponent: FC<CurrentPointProps> = ({
  gap = 'sm',
  padding = 'sm',
  className,
  children,
  ...props
}) => {
  return (
    <div
      className={classNames(
        styles.root.base,
        styles.root.gap[gap],
        styles.root.padding[padding],
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

CurrentPointComponent.displayName = 'CurrentPoint';
export const CurrentPoint = Object.assign(CurrentPointComponent, {
  Label: CurrentPointLabel,
  Action: CurrentPointAction,
  Link: CurrentPointLink,
  Note: CurrentPointNote,
});
