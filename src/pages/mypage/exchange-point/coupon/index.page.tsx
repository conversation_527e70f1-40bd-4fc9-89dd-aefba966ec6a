// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { useRouter } from 'next/router';
import React, { useEffect } from 'react';
import Head from 'next/head';
import { routers } from '@/constants/routes';
import MypageLayout from '../../components/MypageLayout';
import { ExchangeableGiftDetailError } from '../[giftId]/components/ExchangeableGiftDetailError';
import { ExchangeableGiftDetailLoading } from '../[giftId]/components/ExchangeableGiftDetailLoading';
import { useFetch } from '@/hooks/use-fetch';
import exchangePointsApi from '@/services/internal/modules/exchange-points';
import { CouponDetail } from './components/CouponDetail';

const CouponDetailScreen = () => {
  const router = useRouter();
  const { couponId, code } = router.query;

  const {
    fetch: getCouponDetail,
    data: couponDetail,
    isFetching,
    isErrorFetching,
  } = useFetch({
    fetcher: () =>
      exchangePointsApi.getCouponDetail({
        id: couponId as string,
        code: code as string,
      }),
    dependencies: [],
  });

  useEffect(() => {
    if (!couponId || !code) {
      router.push(routers.exchangePoint);
      return;
    }
  }, [couponId, code]);

  return (
    <>
      <Head>
        <title>
          {couponDetail.gift_company_name
            ? `${couponDetail.gift_company_name}クーポン`
            : 'クーポン'}
        </title>
      </Head>
      <MypageLayout spacingTop="lg">
        {isFetching ? (
          <ExchangeableGiftDetailLoading />
        ) : isErrorFetching ? (
          <ExchangeableGiftDetailError retry={getCouponDetail} />
        ) : couponDetail ? (
          <CouponDetail coupon={couponDetail} />
        ) : (
          <ExchangeableGiftDetailError retry={getCouponDetail} />
        )}
      </MypageLayout>
    </>
  );
};

export default CouponDetailScreen;
