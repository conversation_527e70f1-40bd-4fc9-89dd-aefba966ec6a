// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { styles } from '../TextInput/TextInput.styles';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/solid';

interface IShowHiddenPassword {
  isShow: boolean;
  setIsShow: (_: boolean) => void;
}

export const ShowHiddenPassword: FC<IShowHiddenPassword> = ({
  isShow,
  setIsShow,
}) => {
  const theme = styles;

  return (
    <>
      <EyeIcon
        className={`${theme.field.rightIcon.svg} ${isShow && 'hidden'}`}
        onClick={() => setIsShow(!isShow)}
      />
      <EyeSlashIcon
        className={`
          ${theme.field.rightIcon.svg}
          ${!isShow && 'hidden'}
        `}
        onClick={() => setIsShow(!isShow)}
      />
    </>
  );
};
