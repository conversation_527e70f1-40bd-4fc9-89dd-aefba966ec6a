// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { renderHook, act } from '@testing-library/react';
import { useRouter } from 'next/router';
import { useError } from '@/hooks/use-error';

jest.mock('next/router', () => ({
  useRouter: jest.fn(),
  __esModule: true,
  default: {
    push: jest.fn(),
    asPath: '/checkin',
  },
}));

describe('hooks/use-error/use-error.spec.tsx', () => {
  describe('User interactions', () => {
    it('should handle redirect when have not err', async () => {
      const mockHref = 'http://example.com';

      useRouter.mockReturnValue({
        push: jest.fn(),
        query: {
          redirectTo: '/',
          unblocked: 'string',
        },
      });

      const { result } = renderHook(() => useError());

      jest
        .spyOn(window, 'location', 'get')
        .mockImplementation(() => ({ href: mockHref } as unknown as Location));

      act(() => {
        result.current.handler();
      });

      expect(useRouter().push).toHaveBeenCalledWith('/');
    });
  });
});
