// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { LiffContextMockProviders } from '@/test/mocks';
import authApi from '@/services/internal/modules/auth';
import ChangeProfileInformationForm from '@/pages/mypage/account-settings/components/ChangeProfileInformationForm';

const TestChangeProfileInformationForm = (): JSX.Element => {
  return (
    <LiffContextMockProviders>
      <ChangeProfileInformationForm />
    </LiffContextMockProviders>
  );
};

const response = jest.spyOn(authApi, 'updateUser');

describe('pages/mypage/account-settings/components/ChangeProfileInformationForm.spec.tsx', () => {
  const button = () => screen.getByRole('button', { name: '変更する' });
  const emailField = () =>
    screen.getByRole('textbox', {
      name: 'メールアドレス',
    }) as HTMLInputElement;

  describe('A11y', () => {
    it('should render in document', async () => {
      render(<TestChangeProfileInformationForm />);

      const listButton = screen.getAllByRole('button');

      await waitFor(() => {
        expect(listButton.length).toEqual(5);
      });
    });
  });

  describe('Fetching success', () => {
    it('should render in document', async () => {
      response.mockResolvedValue({
        birthday: 'string',
        email: 'string',
        gender: 'string',
        prefecture: 'string',
      });

      render(<TestChangeProfileInformationForm />);

      fireEvent.input(emailField(), {
        target: {
          value: '<EMAIL>',
        },
      });

      fireEvent.submit(button());

      await waitFor(() => {
        expect(emailField().value).toEqual('<EMAIL>');
      });
    });
  });

  describe('Fetching failure', () => {
    it('should handle error when fetch fail', async () => {
      response.mockRejectedValue({
        errors: [
          {
            property: 'email',
            constraints: {
              UpdateEmailUnique: 'example',
            },
          },
        ],
      });

      render(<TestChangeProfileInformationForm />);

      fireEvent.input(emailField(), {
        target: {
          value: '<EMAIL>',
        },
      });

      fireEvent.submit(button());

      await waitFor(() => {
        expect(emailField().value).toEqual('<EMAIL>');
      });
    });

    it('should return when have not error detail response', async () => {
      response.mockRejectedValue({
        errors: [],
      });

      render(<TestChangeProfileInformationForm />);

      fireEvent.input(emailField(), {
        target: {
          value: '<EMAIL>',
        },
      });

      fireEvent.submit(button());

      await waitFor(() => {
        expect(emailField().value).toEqual('<EMAIL>');
      });
    });

    it('should return when err property !== email', async () => {
      response.mockRejectedValue({
        errors: [
          {
            property: 'email1123',
            constraints: {
              UpdateEmailUnique: 'example',
            },
          },
        ],
      });
      render(<TestChangeProfileInformationForm />);

      fireEvent.input(emailField(), {
        target: {
          value: '<EMAIL>',
        },
      });

      fireEvent.submit(button());

      await waitFor(() => {
        expect(emailField().value).toEqual('<EMAIL>');
      });
    });

    it('should return when have not err UpdateEmailUnique', async () => {
      response.mockRejectedValue({
        errors: [
          {
            property: 'email',
            constraints: {
              UpdateEmailUnique: '',
            },
          },
        ],
      });
      render(<TestChangeProfileInformationForm />);

      fireEvent.input(emailField(), {
        target: {
          value: '<EMAIL>',
        },
      });

      fireEvent.submit(button());

      await waitFor(() => {
        expect(emailField().value).toEqual('<EMAIL>');
      });
    });
  });
});
