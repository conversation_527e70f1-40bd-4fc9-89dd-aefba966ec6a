// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen, fireEvent } from '@testing-library/react';
import ToastMessage from '@/components/ToastMessage/ToastMessage';
import { Button } from '@/components/Button';
import { toast } from 'react-toastify';

const TestToast = (): JSX.Element => {
  const onClickHandler = () => {
    toast.info('Toast message');
  };

  return (
    <div>
      <div>
        <ToastMessage />
      </div>
      <div>
        <Button onClick={onClickHandler} type="submit">
          Submit
        </Button>
      </div>
    </div>
  );
};

describe('components/ToastMessage/ToastMessage.spec.tsx', () => {
  describe('A11y', () => {
    it('should render toast message', async () => {
      render(<TestToast />);

      fireEvent.click(screen.getByText('Submit'));

      await screen.findByRole('alert');

      expect(screen.getByRole('alert')).toHaveTextContent('Toast message');
    });
  });
});
