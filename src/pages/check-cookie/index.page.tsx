// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC, useState } from 'react';
import { TrashIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/Button';
import { destroyCookieApp, parseCookiesApp } from '@/utils/cookie';
import { IAppConfig } from '@/types/app';
import { useRouter } from 'next/router';

interface CheckCookieScreenProps {
  appConfig: IAppConfig;
}

const CheckCookieScreen: FC<CheckCookieScreenProps> = ({
  appConfig: { cookies: cookiesFromServer = {} },
}) => {
  const router = useRouter();
  const [cookiesFromBrowser, setCookiesFromBrowser] = useState<{
    [key: string]: string;
  }>(() => parseCookiesApp());

  const handleDeleteCookie = (key: string) => {
    destroyCookieApp(null, key);
    setCookiesFromBrowser(parseCookiesApp());
  };

  const handleDeleteAllCookies = () => {
    Object.keys(cookiesFromBrowser).forEach((key: string) => {
      destroyCookieApp(null, key);
    });

    setCookiesFromBrowser(parseCookiesApp());
  };

  return (
    <div className="container mx-auto">
      <div className="w-full md:max-w-md mx-auto min-h-screen">
        <div className="py-8 space-y-4">
          <div className="space-y-3">
            <h3 className="font-bold">Cookies from browser</h3>
            <div className="space-y-2">
              {Object.keys(cookiesFromBrowser).map(
                (key: string, index: number) => (
                  <div
                    key={index}
                    className="rounded border border-gray-10 p-2 space-y-2"
                  >
                    <div className="flex items-center justify-between">
                      <h3 className="text-base font-bold truncate">{key}</h3>
                      <TrashIcon
                        data-testid="delete-cookie"
                        className="w-5 h-5 text-red-600"
                        onClick={() => handleDeleteCookie(key)}
                      />
                    </div>

                    <p className="text-sm break-words">
                      {cookiesFromBrowser[key]}
                    </p>
                  </div>
                )
              )}
            </div>
            {Object.keys(cookiesFromBrowser).length > 0 ? (
              <Button
                fullSized
                type="submit"
                size="xl"
                color="primary"
                onClick={handleDeleteAllCookies}
              >
                Delete All Cookies Client
              </Button>
            ) : (
              <Button
                fullSized
                type="submit"
                size="xl"
                color="primary"
                onClick={() => router.reload()}
              >
                Refresh page
              </Button>
            )}
          </div>
          <div className="space-y-3">
            <h3 className="font-bold">Cookies from server</h3>
            <div className="space-y-2">
              {Object.keys(cookiesFromServer).map(
                (key: string, index: number) => (
                  <div
                    key={index}
                    className="rounded border border-gray-10 p-2 space-y-2"
                  >
                    <div className="flex items-center justify-between">
                      <h3 className="text-base font-bold truncate">{key}</h3>
                    </div>

                    <p className="text-sm break-words">
                      {cookiesFromBrowser[key]}
                    </p>
                  </div>
                )
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckCookieScreen;
