// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { POINT_HISTORIES } from '@/mocks/point-histories';
import pointApi from '@/services/internal/modules/point';
import { PointHistoryList } from '@/pages/mypage/point-history/components/PointHistoryList';

describe('pages/mypage/point-history/components/PointHistoryList.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document with wrap container', async () => {
      render(<PointHistoryList />);
      const href = screen.getByTestId('loading');

      expect(href).toBeInTheDocument();
    });
  });

  describe('Fetching api', () => {
    describe('Fetching success', () => {
      it('should call api success return response exchanged gift list', async () => {
        jest
          .spyOn(pointApi, 'getPointHistories')
          .mockResolvedValue(POINT_HISTORIES);

        render(<PointHistoryList />);

        await waitFor(() => {
          const heading = screen.getAllByRole('img');
          expect(heading.length).toEqual(4);
        });
      });
    });

    describe('Fetching failure', () => {
      it('should call api failure return error', async () => {
        jest
          .spyOn(pointApi, 'getPointHistories')
          .mockRejectedValue({ statusCode: 500 });
        render(<PointHistoryList />);
        await waitFor(() => {
          const button = screen.getByRole('button');
          expect(button).toBeInTheDocument();
        });
      });
    });
  });

  describe('User interactions', () => {
    it('should trigger `onClick` show more button', async () => {
      const user = userEvent.setup();
      jest
        .spyOn(pointApi, 'getPointHistories')
        .mockResolvedValue(POINT_HISTORIES);

      render(<PointHistoryList />);

      await waitFor(() => {
        const button = screen.getByRole('button');
        user.click(button);
        expect(button).toHaveFocus();
      });
    });
  });
});
