// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React, { ComponentProps, FC } from 'react';
import { formatTime, isSameBefore } from '@/utils/time';
import { Gift } from '@/components/Gift';
import { Button } from '@/components/Button';
import { Spinner } from '@/components/Spinner';
import { HandleToggleGiftProps } from './ExchangedGiftList';
import { TAB_EXCHANGED_GIFT_USED } from '@/constants/mypage';
import { IExchangedGiftDetail } from '@/types/exchange-points';
import { EXCHANGED_EXPIRE_TIME_PLACEHOLDER } from '@/constants/exchange-points';
import { useRouter } from 'next/router';

interface ExchangedGiftListUsedProps extends ComponentProps<'div'> {
  gifts: IExchangedGiftDetail[];
  toggle: (options: HandleToggleGiftProps) => void;
}

export const ExchangedGiftListUsed: FC<ExchangedGiftListUsedProps> = ({
  gifts,
  toggle,
  ...props
}) => {
  const router = useRouter();

  return (
    <div className="space-y-4" {...props}>
      {gifts.map((exchangedGift: IExchangedGiftDetail) => {
        const { gift, gift_type, exchanged_at, uid, loading } = exchangedGift;

        const exchangedAtTime = formatTime({
          time: exchanged_at,
          format: 'YYYY/M/DD',
        });

        const exchangedExpireTimeOrigin =
          gift_type === 'choosable' ? gift?.choosable_end_at : gift?.expired_at;

        /**
         * In case of gift_type==="fixed", when you exchange gift success.
         * Right away, go back to exchanged history screen you will recive expires_at===null.
         * So in the case will display text default
         */
        const exchangedExpireTime = exchangedExpireTimeOrigin
          ? formatTime({
              format: 'YYYY年M月DD日まで有効',
              time: exchangedExpireTimeOrigin,
            })
          : EXCHANGED_EXPIRE_TIME_PLACEHOLDER;

        const isExchangedExpireSameBeforeNow = isSameBefore(
          exchangedExpireTimeOrigin ? exchangedExpireTimeOrigin : new Date()
        );

        return (
          <Gift key={uid} className="gap-2.5 bg-gray-5 p-2 border-none">
            <Gift.Body
              showChevron
              type="medium"
              contentClassName="items-center"
              onClick={() => {
                if (gift_type === 'coupon') {
                  const couponUrl = `/mypage/exchange-point/coupon?couponId=${encodeURIComponent(
                    gift.gift_id_in_pc
                  )}&code=${encodeURIComponent(gift.code || '')}`;

                  router.push(couponUrl);
                } else {
                  window.location.href = gift.gift_url;
                }
              }}
            >
              <Gift.Thumb
                width={55}
                height={55}
                src={gift?.gift_image}
                alt={gift?.gift_name}
              />
              <Gift.Content
                title={gift?.gift_name}
                company={gift?.gift_company_name}
                exchangedAt={exchangedAtTime}
                exchangedExpire={exchangedExpireTime}
              />
            </Gift.Body>

            {isExchangedExpireSameBeforeNow && (
              <Gift.Action className="gap-4">
                <Button
                  disabled
                  color="gray-10"
                  className="w-1/2"
                  customSpan="!text-xs !px-1 max-[320px]:!text-[10px]"
                >
                  利用済みにする
                </Button>
                <Button
                  color="secondary"
                  className="w-1/2"
                  customSpan="!text-xs !px-1 max-[320px]:!text-[10px]"
                  disabled={loading}
                  onClick={() => toggle({ uid, type: TAB_EXCHANGED_GIFT_USED })}
                >
                  {loading && (
                    <div className="mr-2">
                      <Spinner size="md" color="secondary" />
                    </div>
                  )}
                  使えるギフトに戻す
                </Button>
              </Gift.Action>
            )}

            <Gift.Note>
              {isExchangedExpireSameBeforeNow
                ? '誤って利用済みにしたギフトは使えるギフトに戻すことができます'
                : 'こちらのギフトは有効期限を過ぎています'}
            </Gift.Note>
          </Gift>
        );
      })}
    </div>
  );
};
