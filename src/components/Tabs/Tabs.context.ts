// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { TabsValue, TabsVariant } from './Tabs';
import { TABS_ERRORS } from './Tabs.errors';
import { createSafeContext } from '@/utils/context';

interface TabsContext {
  id: string;
  value: TabsValue;
  variant: TabsVariant;
  onTabChange(value: TabsValue): void;
  getTabId(value: string): string;
  getPanelId(value: string): string;
}

export const [TabsContextProvider, useTabsContext] =
  createSafeContext<TabsContext>(TABS_ERRORS.context);
