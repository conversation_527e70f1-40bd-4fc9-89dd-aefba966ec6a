// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { fireEvent, render, screen } from '@testing-library/react';
import type { Liff } from '@line/liff';
import { LiffContextMockProviders } from '@/test/mocks';
import LotteryOutOfPeriod from '@/pages/lottery/out-of-period.page';

const TestLotteryOutOfPeriod = (): JSX.Element => {
  const liffContextValue = {
    liff: {
      closeWindow: () => ({}),
    } as unknown as Liff,
  };
  return (
    <LiffContextMockProviders contextValue={liffContextValue}>
      <LotteryOutOfPeriod />
    </LiffContextMockProviders>
  );
};

describe('pages/lottery/out-of-period.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<TestLotteryOutOfPeriod />);
      const headings = screen.getByRole('heading');

      expect(headings).toBeInTheDocument();
    });
  });

  describe('User interactions', () => {
    it('should trigger `onClick` button', async () => {
      render(<TestLotteryOutOfPeriod />);
      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(button).toBeInTheDocument();
    });
  });
});
