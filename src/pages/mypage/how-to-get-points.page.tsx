// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import MypageLayout from './components/MypageLayout';

const HowToGetPointsScreen: FC = () => {
  const menusCaromill = [
    { title: '対象メニュー', description: 'ポイント数' },
    { title: 'カロミルアプリの起動', description: '3 pt' },
  ];

  const menusGoldGym = [
    { title: '対象メニュー', description: 'ポイント数' },
    {
      title: '店舗設置の「チェックインdeポイント」専用QRコードの読み取り',
      description: '8 pt',
    },
  ];

  const menusSoelu = [
    { title: '対象メニュー', description: 'ポイント数' },
    {
      title: 'オンラインレッスンの受講',
      description: '4 pt',
    },
    {
      title: '店舗設置の「チェックインdeポイント」専用QRコードの読み取り',
      description: '4 pt',
    },
  ];

  const menusMinchare = [
    { title: '対象メニュー', description: 'ポイント数' },
    { title: 'みんチャレアプリの起動', description: '3 pt' },
  ];

  return (
    <>
      <MypageLayout spacingBottom="4xl">
        <div className="text-sm">
          <h3 className="mb-6 text-theme-primary font-bold text-lg">
            ピアコネポイントの付与対象となる機能、ポイント数
          </h3>
          <ol>
            <li>
              <p className="mb-6">
                １．次のリッチメニュー操作でポイントを付与します。
                <br />
                ただし、対象となる各機能ともポイント付与は１日につき１回とします。
              </p>

              <div className="mb-4">
                <p className="font-bold mb-2">カロミルのリッチメニュー</p>
                <table className="w-full mb-2">
                  <colgroup>
                    <col style={{ width: '75%' }}></col>
                    <col style={{ width: '25%' }}></col>
                  </colgroup>
                  <tbody>
                    {menusCaromill.map((menu, index) => (
                      <tr key={index}>
                        <td className="py-[6px] pl-[13px] border border-theme-pale rounded">
                          {menu.title}
                        </td>
                        <td className="text-center border border-theme-pale rounded">
                          {menu.description}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                <ul className="text--indent">
                  <li>
                    ※「食事記録」「体重バイタル」「運動」「体重予測」のいずれか一つをその日初めて起動したときに付与します。
                    <br />
                    その後、異なるメニューで起動しても、2回目以降の起動と判定し、重複してのポイントは付与しません。
                  </li>
                </ul>
              </div>

              <div className="mb-4">
                <p className="font-bold mb-2">ゴールドジムのリッチメニュー</p>
                <table className="w-full mb-2">
                  <colgroup>
                    <col style={{ width: '75%' }}></col>
                    <col style={{ width: '25%' }}></col>
                  </colgroup>
                  <tbody>
                    {menusGoldGym.map((menu, index) => (
                      <tr key={index}>
                        <td className="py-[6px] pl-[13px] border border-theme-pale rounded">
                          {menu.title}
                        </td>
                        <td className="text-center border border-theme-pale rounded">
                          {menu.description}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                <ul className="text--indent">
                  <li>
                    ※スマートフォン内のQRコードリーダーで対象のQRコードを直接読み込んだ場合もポイントは付与されます。
                  </li>
                </ul>
              </div>

              <div className="mb-4">
                <p className="font-bold mb-2">SOELUのリッチメニュー</p>
                <table className="w-full mb-2">
                  <colgroup>
                    <col style={{ width: '75%' }}></col>
                    <col style={{ width: '25%' }}></col>
                  </colgroup>
                  <tbody>
                    {menusSoelu.map((menu, index) => (
                      <tr key={index}>
                        <td className="py-[6px] pl-[13px] border border-theme-pale rounded">
                          {menu.title}
                        </td>
                        <td className="text-center border border-theme-pale rounded">
                          {menu.description}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                <ul className="text--indent">
                  <li>
                    ※オンラインレッスンの受講によるポイントは申請により、1ヵ月分がまとめて付与されます。（上限120ポイント［30回分］）
                  </li>
                  <li>
                    ※スマートフォン内のQRコードリーダーで対象のQRコードを直接読み込んだ場合もポイントは付与されます。
                  </li>
                </ul>
              </div>

              <div className="mb-4">
                <p className="font-bold mb-2">みんチャレのリッチメニュー</p>
                <table className="w-full">
                  <colgroup>
                    <col style={{ width: '75%' }}></col>
                    <col style={{ width: '25%' }}></col>
                  </colgroup>
                  <tbody>
                    {menusMinchare.map((menu, index) => (
                      <tr key={index}>
                        <td className="py-[6px] pl-[13px] border border-theme-pale rounded">
                          {menu.title}
                        </td>
                        <td className="text-center border border-theme-pale rounded">
                          {menu.description}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </li>
            <li>
              <p className="mb-6">
                ２．ピアコネからの配信
                <br />
                ピアコネからお勧めの記事やアンケートへのリンクを配信することがあります。
                <br />
                その際、記事への遷移やアンケートへのご回答でピアコネポイントを付与することがあります。
                <br />
                ポイントの付与対象の場合、その旨およびポイント数は配信時にお知らせします。
              </p>
            </li>
          </ol>
        </div>
      </MypageLayout>
    </>
  );
};

export default HowToGetPointsScreen;
