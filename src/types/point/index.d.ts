import { PointType } from '@/constants/enum';

// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
export interface IHistoryItem {
  amount: number;
  event_datetime: string;
  company_id: string;
  company_name: string;
  company_image: string;
  event_name: string;
  event_type: PointType;
}

export interface IPointHistoriesResponse {
  hasMoreResults: boolean;
  current_point_amount: number;
  point_histories: Array<IHistoryItem>;
}

export interface IPointHistoriesParams {
  page: number;
  limit: number;
}
