// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { Gift } from '@/components/Gift';
import { Button } from '@/components/Button';
import {
  IExchangeableGiftInListResponse,
  IExchangeableGiftListResponse,
} from '@/types/exchange-points';
import { routers } from '@/constants/routes';

interface ExchangeableGiftListProps {
  gifts: IExchangeableGiftListResponse[];
}

export const ExchangeableGiftList: FC<ExchangeableGiftListProps> = ({
  gifts,
}) => {
  return (
    <div className="space-y-4">
      {gifts.map((giftGroup: IExchangeableGiftListResponse, index: number) => (
        <div key={index} className="space-y-2">
          <div className="relative py-[5px] pr-[25px] pl-[38px] w-fit bg-theme-secondary rounded-sm overflow-hidden">
            <div className="absolute w-2 h-2 rounded-full -translate-y-1/2 top-1/2 bg-white -left-[4px]"></div>
            <div className="absolute w-[2px] h-[5px] rounded-full bg-white left-[24px] -top-[1px]"></div>
            <div className="absolute w-[2px] h-[5px] rounded-full bg-white left-[24px] top-1/4"></div>
            <div className="absolute w-[2px] h-[5px] rounded-full bg-white left-[24px] bottom-1/4"></div>
            <div className="absolute w-[2px] h-[5px] rounded-full bg-white left-[24px] -bottom-[1px]"></div>
            <h3 className="text-sm font-bold text-white relative">
              {giftGroup.gift_required_amount} pt で交換可能なギフト
            </h3>
          </div>
          <div className="space-y-2">
            {giftGroup.gift_list.map(
              (gift: IExchangeableGiftInListResponse) => (
                <Gift
                  key={gift.gift_id_in_pc}
                  orientation="horizontal"
                  className="px-3 py-[9px]"
                >
                  <Gift.Body className="flex-1 min-w-0 w-full">
                    <Gift.Thumb src={gift?.gift_image} alt={gift.gift_name} />
                    <Gift.Content
                      title={gift?.gift_name}
                      company={gift?.gift_company_name}
                      customTitleClassName="min-h-[40px]"
                    >
                      <Gift.Action position="right" className="mt-2">
                        <Button
                          size="md"
                          color="primary"
                          href={`${routers.exchangePoint}/${gift.gift_id_in_pc}`}
                          customSpan="!text-xs"
                        >
                          このギフトと交換する
                        </Button>
                      </Gift.Action>
                    </Gift.Content>
                  </Gift.Body>
                </Gift>
              )
            )}
          </div>
        </div>
      ))}
    </div>
  );
};
