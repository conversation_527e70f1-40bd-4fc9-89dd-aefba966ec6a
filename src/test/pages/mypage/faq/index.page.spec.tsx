// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen } from '@testing-library/react';
import FAQScreen from '@/pages/mypage/faq.page';

jest.mock('next/router', () => require('next-router-mock'));

describe('pages/mypage/faq/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<FAQScreen />);
      const link = screen.getAllByRole('link');

      expect(link.length).toEqual(6);
    });
  });
});
