/* eslint-disable @typescript-eslint/no-explicit-any */
// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
/**
 * Parse Query String
 * @param {string} search
 * @returns {Object.<string, string>}
 */
export const parseQueryString = (search: string): Record<string, string> =>
  (search || '')
    .replace(/^\?/g, '')
    .split('&')
    .reduce((acc, query) => {
      const [key, value] = query.split('=');

      if (key) {
        acc[key] = decodeURIComponent(value);
      }

      return acc;
    }, {} as Record<string, string>);

export const windowExists = (): boolean => typeof window !== 'undefined';

interface DataLayerEvent {
  event: string;
  [key: string]: any; // to support others parameter
}

export const sendDataLayerEvent = (
  eventName: string,
  params: { [key: string]: any }
) => {
  // Check dataLayer was defined
  if (window && (window as any).dataLayer) {
    const eventData: DataLayerEvent = {
      event: eventName,
      ...params, // pass some parameters
    };

    // push event dataLayer
    (window as any).dataLayer.push(eventData);

    console.log('success');
  } else {
    console.error('dataLayer is not initialized.');
  }
};

export const normalizeArray = (arr: string[]) => [...new Set(arr)].sort();

export const dirtyCheck = (init: string[], current: string[]) => {
  const isDirty =
    JSON.stringify(normalizeArray(init)) !==
    JSON.stringify(normalizeArray(current));
  return isDirty;
};
