html,
body {
  height: 100%;
}

body {
  font-size: $font-size;
  line-height: 1.5rem;
  font-family: $font-family-primary;
  color: $text-main;
  background-color: $bg-white;
  font-style: normal;
  font-variant-caps: normal;
  font-variant-ligatures: normal;
  font-variant-numeric: normal;
  font-variant-east-asian: normal;
  font-weight: normal;
  font-stretch: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  -ms-overflow-x: hidden;
  overflow-y: auto;
  -ms-overflow-y: auto;
}

input,
textarea,
button,
select,
a {
  -webkit-tap-highlight-color: transparent;
}

pre {
  font-family: $font-family-primary;
  white-space: pre-line;
}
