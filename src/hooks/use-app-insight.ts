// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { ApplicationInsights } from '@microsoft/applicationinsights-web';

type UseAppInsightType = {
  appInsights: ApplicationInsights;
  loadAppInsights: () => void;
};

export const useAppInsight = (): UseAppInsightType => {
  const appInsights = new ApplicationInsights({
    config: {
      connectionString: process.env.APPINSIGHTS_CONNECTION_STRING,
    },
  });

  const loadAppInsights = () => {
    try {
      appInsights.loadAppInsights();
    } catch (error) {
      console.log('Initial appInsight failed');
    }
  };

  return { appInsights, loadAppInsights };
};
