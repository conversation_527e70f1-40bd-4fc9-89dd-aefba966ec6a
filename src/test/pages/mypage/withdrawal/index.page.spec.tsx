// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen, act, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import WithDrawalScreen from '@/pages/mypage/withdrawal.page';

jest.mock('next/router', () => require('next-router-mock'));
jest.mock('@/hooks/use-fetch', () => ({
  ...jest.requireActual('@/hooks/use-fetch'),
  useFetch: () => {
    return {
      fetch: () => ({ success: true }),
    };
  },
}));

describe('pages/mypage/withdrawal/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<WithDrawalScreen />);
      const h3 = screen.getByRole('heading');

      expect(h3).toBeInTheDocument();
    });
  });

  describe('User interactions', () => {
    it('should trigger withdrawal button', async () => {
      const user = userEvent.setup();

      const { container } = render(<WithDrawalScreen />);
      // eslint-disable-next-line testing-library/no-container, testing-library/no-node-access
      container.querySelectorAll('input[type=checkbox]').forEach(input => {
        const value = input.getAttribute('value') as string;
        fireEvent.click(input, { target: { value } });
      });
      const button = screen.getByRole('button');
      await act(() => user.click(button));

      expect(button).toHaveFocus();
    });
  });
});
