// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React, { FC, forwardRef, useEffect, useState } from 'react';
import Select, {
  StylesConfig,
  Props as ReactSelectProps,
  components,
  DropdownIndicatorProps,
} from 'react-select';
import { SvgIcon } from '../SvgIcon';
export interface OptionType {
  value: string;
  label: string;
}
interface Select2Props extends ReactSelectProps<OptionType> {
  options: OptionType[];
  placeholder?: string;
  customStyles?: StylesConfig<OptionType>;
  onChange?: (value: OptionType | null) => void;
  defaultValue?: OptionType;
  className?: string;
}
export const Select2 = forwardRef<HTMLSelectElement, Select2Props>(
  (
    {
      options,
      placeholder = 'Select an option',
      customStyles,
      onChange,
      defaultValue,
      className,
      ...props
    },
    ref
  ) => {
    const [value, setValue] = useState<OptionType | null>(null);
    const DropdownIndicator: FC<DropdownIndicatorProps> = propsDropdown => {
      return (
        <components.DropdownIndicator
          {...propsDropdown}
          className="absolute w-full h-full left-0 justify-end items-center !px-4"
        >
          <SvgIcon name={'BoldDownArrow'} />
        </components.DropdownIndicator>
      );
    };
    const handleChange = (selectedOption: OptionType | null) => {
      setValue(selectedOption);
      if (onChange) {
        onChange(selectedOption);
      }
    };
    useEffect(() => {
      if (defaultValue) setValue(defaultValue);
    }, [defaultValue]);
    const defaultStyles: StylesConfig = {
      control: provided => ({
        ...provided,
        backgroundColor: '#B88770',
        borderRadius: 4,
        padding: 4,
        border: 'none',
        boxShadow: 'none',
        cursor: 'pointer',
        height: 48,
      }),
      indicatorSeparator: () => ({ display: 'none' }),
      menu: provided => ({
        ...provided,
        borderRadius: 4,
        boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
        overflow: 'hidden',
        marginTop: 0,
      }),
      option: (provided, state) => ({
        ...provided,
        padding: '12px 16px',
        cursor: 'pointer',
        backgroundColor: state.isSelected ? '#eee' : '#ffffff',
        color: '#000',
        fontWeight: 500,
        fontSize: 14,
        lineHeight: '20px',
        minHeight: 44,
        wordBreak: 'break-word',
        textAlign: 'center',
      }),
      singleValue: provided => ({
        ...provided,
        color: '#fff',
        fontWeight: 700,
        fontSize: 16,
        lineHeight: '20px',
        textAlign: 'center',
      }),
    };
    return (
      <Select
        value={value}
        options={options}
        styles={{ ...defaultStyles, ...customStyles }}
        placeholder={placeholder}
        components={{ DropdownIndicator }}
        isFocused={false}
        isSearchable={false}
        isMulti={false}
        onChange={handleChange}
        defaultValue={defaultValue}
        className={className}
        ref={ref}
        {...props}
      />
    );
  }
);
Select2.displayName = 'Select2';
