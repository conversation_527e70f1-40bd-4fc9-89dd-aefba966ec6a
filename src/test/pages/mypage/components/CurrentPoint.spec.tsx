// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import CurrentPoint from '@/pages/mypage/components/CurrentPoint';

const TestCurrentPoint: FC<{ handleRedeemPointsButtonClick?: () => void }> = ({
  handleRedeemPointsButtonClick,
}) => {
  return (
    <CurrentPoint
      currentPoint={12}
      handleRedeemPointsButtonClick={handleRedeemPointsButtonClick}
    />
  );
};

describe('pages/mypage/components/CurrentPoint.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<TestCurrentPoint />);
      const heading = screen.getByRole('heading');

      expect(heading).toBeInTheDocument();
    });
  });

  describe('User interactions', () => {
    it('should trigger `onClick` button', async () => {
      const handleRedeemPointsButtonClick = jest.fn();
      render(
        <TestCurrentPoint
          handleRedeemPointsButtonClick={handleRedeemPointsButtonClick}
        />
      );
      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(handleRedeemPointsButtonClick).toHaveBeenCalledTimes(1);
    });
  });
});
