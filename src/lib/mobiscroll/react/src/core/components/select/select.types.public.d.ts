// GKC: 01JPKNE8A7Z8AFWC717C0GBAA5
import { IPickerCancelEvent, IPickerChangeEvent, IPickerCloseEvent, IPickerOpenEvent, IPickerProps, IPickerTempChangeEvent } from '../../../core/shared/picker/picker.types';
import { IBaseEvent } from '../../base';
import { IScrollerProps } from '../scroller/scroller.types';
import { SelectBase } from './select';
export interface MbscSelectFilterEvent extends IBaseEvent<SelectBase> {
    filterText: string;
}
export declare type MbscSelectOnFilterEvent = MbscSelectFilterEvent;
export declare type MbscSelectChangeEvent = IPickerChangeEvent<SelectBase>;
export declare type MbscSelectTempChangeEvent = IPickerTempChangeEvent<SelectBase>;
export declare type MbscSelectCancelEvent = IPickerCancelEvent<SelectBase>;
export declare type MbscSelectCloseEvent = IPickerCloseEvent<SelectBase>;
export declare type MbscSelectOpenEvent = IPickerOpenEvent<SelectBase>;
export interface MbscSelectData {
    group?: string;
    text: string;
    value: any;
    disabled?: boolean;
    [x: string]: any;
}
export interface MbscSelectItemData {
    data: MbscSelectData;
    display: string;
    isGroup: boolean;
    value: any;
}
export interface MbscSelectOptions extends IPickerProps<SelectBase>, IScrollerProps {
    /** @hidden */
    clearIcon?: string;
    /** @hidden */
    inputElement?: HTMLElement;
    /** @hidden */
    inputTyping?: boolean;
    /** @hidden */
    fullScreen?: boolean;
    /** @hidden */
    selectElement?: HTMLSelectElement;
    /** @hidden */
    valid?: any[];
    /**
     * Specifies the selectable options for the component.
     *
     * When it's an array of strings, the selectable options will be the items of the array.
     * The strings will appear on the picker, and the selected values will be the strings themselves.
     *
     * When it's an array of objects, the objects can have the following properties:
     * - `text`: *string* - The text of the option.
     * - `value`: *any* - The value of the option.
     * - `group`: *string* - The group name in case of grouped options.
     * - `disabled`: *boolean* - The disabled state of the option. Disabled options cannot be selected.
     *
     * @defaultValue undefined
     */
    data?: Array<MbscSelectData | string | number>;
    /**
     * If `false`, the down arrow icon is hidden.
     * @defaultValue true
     */
    dropdown?: boolean;
    /**
     * If `true`, it turns filtering on. A filter input will be rendered above the selectable options.
     * Typing in the input will filter the selectable options and will also trigger the [onFilter](#event-onFilter) event.
     *
     * The default behavior is based on the presence of the search term in the option text.
     * The [onFilter](#event-onFilter) event can be used to prevent the default filtering and customize the experience.
     *
     * @defaultValue false
     */
    filter?: boolean;
    /**
     * An array of values that are invalid. Invalid options cannot be selected, and show up as disabled on the Select.
     * @defaultValue undefined
     */
    invalid?: any[];
    /**
     * If `true` and the Select has groups, two columns will be rendered on the picker,
     * the first containing the group labels, and the second one the options.
     * Groups can be specified either by `optgroup` elements, when the data comes from the html markup,
     * or by using the `group` property of the option objects, when the [data](#opt-data) option is used.
     * @defaultValue false
     */
    showGroupWheel?: boolean;
    /**
     * Text for the empty state of the Select. The Select will show this message,
     * when [filtering](#opt-filter) is turned on and there are no results for the searched text.
     * @defaultValue 'No results'
     * @group Localizations
     */
    filterEmptyText?: string;
    /**
     * Placeholder text for the filter input, when [filtering](#opt-filter) is turned on.
     * @defaultValue 'Search'
     * @group Localizations
     */
    filterPlaceholderText?: string;
    /**
     * Specifies the accessibility label for the group wheel, when present.
     * @defaultValue 'Group'
     * @group Localizations
     */
    groupText?: string;
    /**
     * @event
     * Triggered when the value of the filter input changes.
     * Filtering can be turned on with the [filter](#opt-filter) option.
     *
     * To fully customize the filtering, the default behavior can be prevented by returning `false` from the handler function.
     *
     * @param args The event argument with the following properties:
     *    - `filterText`: *string* - The value of the filter input.
     * @param inst The component instance.
     */
    onFilter?(args: MbscSelectFilterEvent, inst: SelectBase): boolean;
    /**
     * @event
     * Triggered when the picker is canceled.
     * @param args The event argument with the following properties:
     *    - `value`: *any* - The selected value.
     *    - `valueText`: *string* - The selected value as text.
     * @param inst The component instance.
     */
    onCancel?(args: MbscSelectCancelEvent, inst: SelectBase): void;
    /**
     * @event
     * Triggered when the value is changed.
     * @param args The event argument with the following properties:
     *    - `value`: *any* - The selected value.
     *    - `valueText`: *string* - The selected value as text.
     * @param inst The component instance.
     */
    onChange?(args: MbscSelectChangeEvent, inst: SelectBase): void;
    /**
     * @event
     * Triggered when the picker is closed.
     * @param args The event argument with the following properties:
     *    - `value`: *any* - The selected value.
     *    - `valueText`: *string* - The selected value as text.
     * @param inst The component instance.
     */
    onClose?(args: MbscSelectCloseEvent, inst: SelectBase): void;
    /**
     * @event
     * Triggered when the picker is opened.
     * @param args The event argument with the following properties:
     *    - `target`: *HTMLElement* - The DOM element of the popup.
     *    - `value`: *any* - The selected value.
     *    - `valueText`: *string* - The selected value as text.
     * @param inst The component instance.
     */
    onOpen?(args: MbscSelectOpenEvent, inst: SelectBase): void;
    /**
     * @event
     * Triggered when the temporary value is changed.
     * @param args The event argument with the following properties:
     *    - `value`: *any* - The selected value.
     * @param inst The component instance.
     */
    onTempChange?(args: MbscSelectTempChangeEvent, inst: SelectBase): void;
    /**
     * Customize each selectable item on the wheel.
     *
     * When the group wheel is also shown using the [`showGroupWheel`](#opt-showGroupWheel) option,
     * the `data` property and the `isGroup` property of the item will be `undefined` in the case of the group wheel items.
     * So, you can distinguish from the group wheel and the option wheel by checking if both the `data` and the `isGroup` are undefined.
     *
     * Available parameters:
     * - `display`: _string_ - The text of the item.
     * - `value`: _any_ - The value of the item.
     * - `isGroup`: _boolean_ - For group headers this property will be true
     * - `data`: _MbscSelectData_ - The original option item that is passed in the [`data`](#opt-data) array
     *
     * @param args
     * @defaultValue undefined
     *
     * @group Renderers
     */
    renderItem?(args: MbscSelectItemData): any;
}
