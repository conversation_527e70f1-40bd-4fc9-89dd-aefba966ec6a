// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { FC } from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import HomeScreen from '@/pages/index.page';
import { LiffContextMockProviders } from '@/test/mocks';
import { useRouter } from 'next/router';

jest.mock('next/router', () => ({
  useRouter: jest.fn(),
  __esModule: true,
  default: {
    push: jest.fn(),
    replace: jest.fn(),
    asPath: '/checkin',
  },
}));

interface TestHomeScreenProps {
  error?: unknown;
}

const TestHomeScreen: FC<TestHomeScreenProps> = ({ error = undefined }) => {
  const liffContextValue = {
    error,
    liff: { getContext: () => ({}), openWindow: () => ({}) } as unknown as Liff,
  };

  return (
    <LiffContextMockProviders contextValue={liffContextValue}>
      <HomeScreen />
    </LiffContextMockProviders>
  );
};

describe('pages/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      useRouter.mockReturnValue({
        query: {
          is_active: 'true',
        },
      });

      render(<TestHomeScreen />);

      await waitFor(() => {
        expect(screen.getByRole('status')).toBeInTheDocument();
      });
    });
  });

  describe('Redirect', () => {
    it('should redirect to chat room', async () => {
      render(<TestHomeScreen />);

      expect(useRouter).toHaveBeenCalled();
    });

    it('should redirect to previous screen', async () => {
      useRouter.mockReturnValue({
        query: {
          redirectTo: '/registered',
        },
      });

      render(<TestHomeScreen />);

      expect(useRouter).toHaveBeenCalled();
    });

    it('should redirect when have not isActive and redirectTo', async () => {
      useRouter.mockReturnValue({
        query: {},
      });

      render(<TestHomeScreen />);

      expect(useRouter).toHaveBeenCalled();
    });

    it('should redirect to friendship', async () => {
      render(<TestHomeScreen error="Lorem" />);

      expect(useRouter).toHaveBeenCalled();
    });
  });
});
