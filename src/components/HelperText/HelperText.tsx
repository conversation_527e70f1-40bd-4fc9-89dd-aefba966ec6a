// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import classNames from 'classnames';
import type { ComponentProps, FC, PropsWithChildren } from 'react';
import { IThemeColors } from '@/types/theme';
import { styles } from './HelperText.styles';

export interface IHelperTextTheme {
  base: string;
  colors: IHelperColors;
}

export interface IHelperColors extends IThemeColors {
  [key: string]: string;
  default: string;
}

export interface IHelperTextProps
  extends PropsWithChildren<Omit<ComponentProps<'p'>, 'color'>> {
  color?: keyof IHelperColors;
  value?: string;
}

export const HelperText: FC<IHelperTextProps> = ({
  value,
  children,
  color = 'default',
  className,
  ...props
}) => {
  const theme = styles;
  return (
    <p
      className={classNames(theme.base, theme.colors[color], className)}
      {...props}
    >
      {value ?? children ?? ''}
    </p>
  );
};
