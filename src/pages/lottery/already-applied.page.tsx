// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { BoxContent } from '@/components/BoxContent';
import { Button } from '@/components/Button';
import { SvgIcon } from '@/components/SvgIcon';
import { useLiff } from '@/contexts/liff';
import { useRouter } from 'next/router';

const LotteryAlreadyApplied = () => {
  const { liff } = useLiff();
  const router = useRouter();
  const { is_once_a_day: isOnceADay } = router.query;

  return (
    <>
      <div className="container mx-auto">
        <BoxContent>
          <BoxContent.Body className="pt-[180px]">
            <div className="text-center">
              <div className="flex justify-center mb-8">
                <SvgIcon name={'AlreadyAppliedLotteryIcon'} />
              </div>
              <p className="text-base">
                {isOnceADay === 'true'
                  ? '本日はチャレンジ済です。'
                  : '期間中には1回のみチャレンジ可能です。'}
              </p>
            </div>
          </BoxContent.Body>
          <BoxContent.Footer>
            <Button
              fullSized
              type="submit"
              size="xl"
              color="primary"
              onClick={() => liff.closeWindow()}
            >
              トーク画面に戻る
            </Button>
          </BoxContent.Footer>
        </BoxContent>
      </div>
    </>
  );
};

export default LotteryAlreadyApplied;
