// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC, useState } from 'react';
import { Button } from '@/components/Button';
import { Label } from '@/components/Label';
import { SvgIcon } from '@/components/SvgIcon';
import { useForm, useWatch } from 'react-hook-form';
import { useFetch } from '@/hooks/use-fetch';
import authApi from '@/services/internal/modules/auth';
import { IWithDrawResponse } from '@/types/user';
import { COMPANY_ID, USER_ACCESS_TOKEN, USER_COMPANY } from '@/constants/user';
import { useLiff } from '@/contexts/liff';
import MypageLayout from './components/MypageLayout';
import { destroyCookieApp } from '@/utils/cookie';

const REASON_WITHDRAWAL = [
  'ポイントに関する権利を放棄することに同意します。',
  '各種サービスを利用できなくなることに同意します。',
  '会員情報はプライバシーポリシーに則り取り扱われることに同意します。',
];

const WithDrawalScreen: FC = () => {
  const { liff } = useLiff();
  const [isSubmitted, setIsSubmitted] = useState(false);
  const numReason = REASON_WITHDRAWAL.length;

  const { register, handleSubmit, getValues, control } = useForm({
    mode: 'onChange',
    defaultValues: {
      reasons: [] as string[],
    },
  });
  const reasonsForm = getValues('reasons');
  useWatch({ name: 'reasons', control });

  const { fetch: withdrawalHandler } = useFetch<IWithDrawResponse>({
    dependencies: false,
    fetcher: authApi.withdrawal,
    initialValue: {} as IWithDrawResponse,
  });

  const handleWithDrawal = async () => {
    try {
      setIsSubmitted(true);
      await withdrawalHandler();
      destroyCookieApp(null, USER_COMPANY);
      destroyCookieApp(null, USER_ACCESS_TOKEN);
      destroyCookieApp(null, COMPANY_ID);
      localStorage.clear();
      liff.closeWindow();
    } catch {
      setIsSubmitted(false);
    }
  };

  return (
    <>
      <MypageLayout>
        <form
          className="flex flex-grow flex-col justify-between h-full"
          onSubmit={handleSubmit(handleWithDrawal)}
        >
          <div className="main">
            <div className="space-y-4">
              <h2 className="font-bold text-base mb-4">
                ピアコネ会員登録の削除を行うと下記のものがご利用いただけなくなります
              </h2>
              <ul className="text--indent">
                <li className="text-sm font-normal mb-4">
                  ・これまでに獲得したポイント
                </li>
                <li className="text-sm font-normal">
                  ・ピアコネパートナー企業がピアコネ上で提供する各種サービス
                </li>
              </ul>
            </div>

            <div className="pt-10">
              <p className="text-sm font-bold mb-3.5">
                退会手続きを進める場合、下記に同意の上「退会する」ボタンをタップください
              </p>
              <div className="space-y-6 flex flex-col">
                {REASON_WITHDRAWAL.map(reason => (
                  <Label
                    key={reason}
                    htmlFor={reason}
                    className="flex items-center"
                  >
                    <div>
                      <SvgIcon
                        name={
                          reasonsForm.includes(reason)
                            ? 'CheckboxChecked'
                            : 'Checkbox'
                        }
                      />
                    </div>
                    <p className="pl-3">{reason}</p>
                    <input
                      id={reason}
                      type="checkbox"
                      {...register('reasons')}
                      name={'reasons'}
                      value={reason}
                      className="hidden"
                    />
                  </Label>
                ))}
              </div>
            </div>
          </div>
          {/* Submit */}
          <div className="pt-8">
            <Button
              fullSized
              type="submit"
              size="xl"
              color={reasonsForm.length !== numReason ? 'gray-10' : 'primary'}
              disabled={reasonsForm.length !== numReason || isSubmitted}
            >
              退会する
            </Button>
          </div>
        </form>
      </MypageLayout>
    </>
  );
};

export default WithDrawalScreen;
