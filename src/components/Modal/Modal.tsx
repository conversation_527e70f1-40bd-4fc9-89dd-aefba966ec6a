// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import classNames from 'classnames';
import { twMerge } from 'tailwind-merge';
import { createPortal } from 'react-dom';
import {
  ComponentProps,
  Dispatch,
  FC,
  PropsWithChildren,
  SetStateAction,
  useEffect,
  useState,
} from 'react';
import { IThemeBoolean, IThemePositions, IThemeSizes } from '@/types/theme';
import { styles } from './Modal.styles';
import { ModalBody } from './ModalBody';
import { ModalContext } from './ModalContext';
import { ModalFooter } from './ModalFooter';
import { ModalHeader } from './ModalHeader';
import { useClickOutside } from '../../hooks/use-click-outside';

export interface IModalTheme {
  base: string;
  show: IThemeBoolean;
  content: {
    base: string;
    inner: string;
  };
  body: {
    base: string;
    popup: string;
  };
  header: {
    base: string;
    popup: string;
  };
  footer: {
    base: string;
    popup: string;
  };
  sizes: IModalSizes;
  positions: IModalPositions;
}

export interface IModalPositions extends IThemePositions {
  [key: string]: string;
}

export interface IModalSizes extends Omit<IThemeSizes, 'xs'> {
  [key: string]: string;
}

export interface IModalProps extends PropsWithChildren<ComponentProps<'div'>> {
  onClose?: () => void;
  position?: keyof IModalPositions;
  popup?: boolean;
  root?: HTMLElement;
  show?: boolean;
  setShow?: Dispatch<SetStateAction<boolean>>;
  scrollable?: boolean;
  closeOnBackdrop?: boolean;
  size?: keyof IModalSizes;
  innerClass?: string;
  innerStyle?: React.CSSProperties;
  innerStyle2?: React.CSSProperties;
}

const ModalComponent: FC<IModalProps> = ({
  children,
  show,
  setShow,
  root,
  popup,
  scrollable = false,
  closeOnBackdrop = false,
  size = '2xl',
  position = 'center',
  onClose,
  innerClass,
  className,
  innerStyle,
  innerStyle2,
  ...props
}) => {
  const theme = styles;
  const appendClassWhenModalOpen: string[] = ['overflow-hidden'];
  const [parent, setParent] = useState<HTMLElement | undefined>(root);
  const [container, setContainer] = useState<HTMLDivElement | undefined>();

  const modalContentRef = useClickOutside(() => {
    if (closeOnBackdrop && typeof setShow === 'function') {
      setShow(false);
    }
  });

  useEffect(() => {
    if (!parent) setParent(document.body);
    if (!container) setContainer(document.createElement('div'));
  }, [container, parent]);

  useEffect(() => {
    if (!container || !parent || !show) {
      return;
    }

    parent.appendChild(container);

    if (!scrollable && container) {
      document.body.classList.add(...appendClassWhenModalOpen);
    }

    return () => {
      if (container) {
        parent.removeChild(container);
        const isExistClass = appendClassWhenModalOpen.every(
          (classNameAppend: string) =>
            document.body.classList.contains(classNameAppend)
        );
        isExistClass &&
          document.body.classList.remove(...appendClassWhenModalOpen);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [container, parent, show, scrollable]);

  return container
    ? createPortal(
        <ModalContext.Provider value={{ popup, onClose }}>
          <div
            aria-hidden={!show}
            className={classNames(
              theme.base,
              theme.positions[position],
              show ? theme.show.on : theme.show.off,
              className
            )}
            data-testid="modal"
            role="dialog"
            {...props}
          >
            <div
              ref={modalContentRef}
              className={classNames(theme.content.base, theme.sizes[size])}
              style={innerStyle}
            >
              <div
                className={twMerge(theme.content.inner, innerClass)}
                style={innerStyle2}
              >
                {children}
              </div>
            </div>
          </div>
        </ModalContext.Provider>,
        container
      )
    : null;
};

ModalComponent.displayName = 'Modal';
ModalHeader.displayName = 'Modal.Header';
ModalBody.displayName = 'Modal.Body';
ModalFooter.displayName = 'Modal.Footer';

export const Modal = Object.assign(ModalComponent, {
  Header: ModalHeader,
  Body: ModalBody,
  Footer: ModalFooter,
});
