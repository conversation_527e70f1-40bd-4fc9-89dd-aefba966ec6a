// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { Logo } from '@/components/Logo';
import { Button } from '@/components/Button';
import { BoxContent } from '@/components/BoxContent';
import { useError } from '@/hooks/use-error';

const AuthErrorScreen: FC = () => {
  const { handler } = useError();

  return (
    <>
      <div className="container mx-auto">
        <BoxContent>
          <BoxContent.Body className="pt-[160px]">
            <Logo box className="mb-10" />
            <div className="text-center">
              <h3 className="text-base font-bold">
                前画面で「トークへのメッセージ
                <br />
                送信」を許可してください。
              </h3>
            </div>
          </BoxContent.Body>
          <BoxContent.Footer>
            <Button
              fullSized
              type="submit"
              size="xl"
              color="primary"
              onClick={handler}
            >
              前画面へ戻る
            </Button>
          </BoxContent.Footer>
        </BoxContent>
      </div>
    </>
  );
};

export default AuthErrorScreen;
