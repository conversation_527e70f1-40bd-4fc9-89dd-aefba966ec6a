// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { IButtonTheme } from './Button';

export const styles: IButtonTheme = {
  base: 'group h-min font-medium focus:z-10',
  fullSized: 'w-full',
  color: {
    gray: 'text-white bg-gray-50 hover:bg-gray-50/90 focus:ring-gray-50/50 focus:ring-2',
    'gray-10':
      'text-white bg-gray-10 hover:bg-gray-10/90 focus:ring-gray-10/50 focus:ring-2',
    'gray-accent':
      'text-main bg-theme-secondary/10 hover:bg-theme-secondary/20 focus:ring-theme-secondary/15 focus:ring-2',
    primary:
      'text-white bg-theme-primary hover:bg-theme-primary/90 focus:ring-theme-primary/50 focus:ring-2',
    'primary-gray':
      'text-white bg-theme-primary hover:bg-theme-primary/90 focus:ring-theme-primary/50 focus:ring-2',
    secondary:
      'text-white bg-theme-secondary hover:bg-theme-secondary/90 focus:ring-theme-secondary/50 focus:ring-2',
    info: 'text-white bg-theme-info-primary hover:bg-theme-info-primary/90 focus:ring-theme-info-primary/50 focus:ring-2',
    success:
      'text-white bg-theme-success-primary hover:bg-theme-success-primary/90 focus:ring-theme-success-primary/50 focus:ring-2',
    warning:
      'text-white bg-theme-warning-primary hover:bg-theme-warning-primary/90 focus:ring-theme-warning-primary/50 focus:ring-2',
    failure:
      'text-white bg-theme-error-primary hover:bg-theme-error-primary/90 focus:ring-theme-error-primary/50 focus:ring-2',
    link: 'text-main bg-white hover:text-theme-primary',
    'link-primary': 'text-theme-primary hover:text-opacity-90 bg-white',
    'calendar-orange': 'text-white bg-calendar-orange',
    white: 'text-primary bg-white outline-1',
    'primary-no-focus': 'text-white bg-theme-primary',
  },
  disabled: 'cursor-not-allowed',
  inner: {
    base: 'flex items-center',
    outline: 'border border-transparent',
  },
  outline: {
    color: {
      gray: 'text-gray-50 hover:text-white outline -outline-offset-1 outline-1 outline-gray-10',
      'gray-10':
        'text-gray-10 hover:text-white outline -outline-offset-1 outline-1 outline-gray-10',
      'gray-accent':
        'text-main bg-white hover:bg-white outline -outline-offset-1 outline-1 outline-gray-10 hover:outline-[#E1F5FF]',
      primary:
        '!text-theme-primary !bg-white hover:bg-white outline -outline-offset-1 outline-1 outline-theme-primary',
      'primary-gray':
        '!text-theme-sub bg-white hover:bg-white outline -outline-offset-1 outline-1 outline-gray-10 hover:outline-theme-primary',
      secondary:
        'text-theme-secondary hover:text-white outline -outline-offset-1 outline-1 outline-theme-secondary',
      info: 'text-theme-info-primary hover:text-white outline -outline-offset-1 outline-1 outline-theme-info-primary',
      success:
        'text-theme-success-primary hover:text-white outline -outline-offset-1 outline-1 outline-theme-success-primary',
      warning:
        'text-theme-warning-primary hover:text-white outline -outline-offset-1 outline-1 outline-theme-warning-primary',
      failure:
        'text-theme-error-primary hover:text-white outline -outline-offset-1 outline-1 outline-theme-error-primary',
      link: 'text-main bg-white hover:text-theme-primary',
      'link-primary': 'text-theme-primary hover:text-opacity-90 bg-white',
      dark: 'text-theme-main outline -outline-offset-1 outline-1 outline-theme-main',
      white:
        'text-theme-main outline -outline-offset-1 outline-1 outline-theme-primary',
    },
    off: '',
    on: 'bg-white transition-all duration-75 ease-in group-hover:bg-opacity-0 group-hover:text-inherit',
    pill: {
      off: 'rounded',
      on: 'rounded-full',
    },
  },
  pill: {
    off: 'rounded',
    on: 'rounded-full',
  },
  size: {
    xs: 'text-xs font-medium px-2 py-1',
    sm: 'text-sm font-medium px-3 py-1.5',
    md: 'text-sm font-semibold px-3 py-2',
    lg: 'text-sm font-bold px-4 py-2.5',
    xl: 'text-sm font-bold px-4 py-3',
  },
  align: {
    left: 'flex items-center justify-start text-left',
    center: 'flex items-center justify-center text-center',
    right: 'flex items-center justify-end text-right',
  },
};
