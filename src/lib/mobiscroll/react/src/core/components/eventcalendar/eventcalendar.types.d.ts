// GKC: 01JPKNE8A7Z8AFWC717C0GBAA5
import { ILabelDragData, MbscCalendarEvent, MbscCalendarEventData } from '../../shared/calendar-view/calendar-view.types';
import { MbscEventList } from './eventcalendar.types.public';
import { STBase } from './shared/schedule-timeline-base';
/** @hidden */
export interface MbscEventcalendarState {
    activeDate?: number;
    eventList?: MbscEventList[];
    height?: number;
    isListScrollable?: boolean;
    isTouchDrag?: boolean;
    popoverContext?: string | HTMLElement;
    popoverDate?: number | undefined;
    popoverDrag?: boolean;
    popoverHidden?: boolean;
    popoverHost?: STBase;
    popoverKey?: number | string;
    popoverList?: MbscCalendarEventData[];
    selectedDate?: number;
    showPopover?: boolean;
    labelDragData?: ILabelDragData;
    width?: number;
}
export interface IEventDragStopArgs {
    action: 'click' | 'drag' | 'externalDrop';
    create?: boolean;
    collision: boolean | MbscCalendarEvent;
    domEvent: any;
    event: MbscCalendarEventData;
    external?: boolean;
    from: any;
    newResource?: number | string;
    newSlot?: number | string;
    oldResource?: number | string;
    oldSlot?: number | string;
    overlap: boolean | MbscCalendarEvent;
    resource?: number | string | Array<number | string>;
    slot?: number | string;
    source: 'calendar' | 'schedule' | 'timeline';
}
export * from './eventcalendar.types.public';
