// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { useFetch } from '@/hooks/use-fetch';
import MypageLayout from '../components/MypageLayout';
import { UserCurrentPoint } from '../components/UserCurrentPoint';
import { ExchangeableGiftList } from './components/ExchangeableGiftList';
import { IExchangeableGiftListResponse } from '@/types/exchange-points';
import exchangePointsApi from '@/services/internal/modules/exchange-points';
import { ExchangeableGiftListLoading } from './components/ExchangeableGiftListLoading';
import { ExchangeableGiftListError } from './components/ExchangeableGiftListError';

const ExchangePointScreen = () => {
  const {
    fetch: getExchangeableGiftListHandler,
    data: exchangeableGiftList,
    isFetching,
    isErrorFetching,
  } = useFetch<IExchangeableGiftListResponse[]>({
    fetcher: exchangePointsApi.getExchangeableGiftList,
    initialValue: [],
    dependencies: [],
  });

  return (
    <>
      <MypageLayout spacingTop="lg" spacingBottom="4xl">
        <div className="space-y-8">
          <UserCurrentPoint layout="short" padding="md" />

          {isFetching ? (
            <ExchangeableGiftListLoading />
          ) : isErrorFetching ? (
            <ExchangeableGiftListError retry={getExchangeableGiftListHandler} />
          ) : exchangeableGiftList && exchangeableGiftList.length > 0 ? (
            <ExchangeableGiftList gifts={exchangeableGiftList} />
          ) : (
            <ExchangeableGiftListError retry={getExchangeableGiftListHandler} />
          )}
        </div>
      </MypageLayout>
    </>
  );
};

export default ExchangePointScreen;
