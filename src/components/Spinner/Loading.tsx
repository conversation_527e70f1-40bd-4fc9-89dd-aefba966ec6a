// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import type { FC } from 'react';
import { ISpinnerProps, Spinner } from './Spinner';

const Loading: FC<ISpinnerProps> = ({
  size = '3xl',
  color = 'primary',
  ...props
}) => {
  return (
    <div className="container mx-auto">
      <div className="w-full h-screen md:max-w-md mx-auto flex items-center justify-center">
        <Spinner size={size} color={color} {...props} />
      </div>
    </div>
  );
};

export default Loading;
