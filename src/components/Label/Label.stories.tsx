// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import type { Meta, Story } from '@storybook/react';
import type { ILabelProps } from './Label';
import { Label } from './Label';
import { styles } from './Label.styles';

export default {
  title: 'Components/Label',
  component: Label,
  argTypes: {
    color: {
      options: Object.keys(styles.colors),
      control: { type: 'select' },
    },
  },
} as Meta;

const Template: Story<ILabelProps> = args => <Label {...args} />;

export const DefaultLabel = Template.bind({});
DefaultLabel.storyName = 'Label';
DefaultLabel.args = {
  children: 'Label',
};
