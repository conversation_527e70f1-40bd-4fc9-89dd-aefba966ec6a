// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React from 'react';
import { range } from 'lodash';

export const ExchangedGiftListLoading = () => {
  return (
    <div className="animate-pulse" data-testid="loading">
      <div className="space-y-2">
        {[...range(3)].map((indexGift: number) => (
          <div
            key={indexGift}
            className="flex border border-gray-10 px-3 py-2 rounded gap-3"
          >
            <div className="w-[100px] h-[100px] bg-slate-200 rounded" />
            <div className="flex flex-1 flex-col justify-between gap-2">
              <div className="space-y-2">
                <div className="w-3/4 h-2 bg-slate-200 rounded" />
                <div className="min-h-[40px] space-y-1">
                  <div className="w-full h-2 bg-slate-200 rounded" />
                  <div className="w-1/2 h-2 bg-slate-200 rounded" />
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="w-[120px] h-6 bg-slate-200 rounded" />
                <div className="w-[40px] h-6 bg-slate-200 rounded" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
