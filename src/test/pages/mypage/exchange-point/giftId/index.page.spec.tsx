// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { GetServerSidePropsContext } from 'next';
import { act, render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { routers } from '@/constants/routes';
import { LiffContextMockProviders } from '@/test/mocks';
import { EXCHANGEABLE_GIFT_DETAIL } from '@/mocks/exchange-points';
import GiftDetailScreen, {
  getServerSideProps,
} from '@/pages/mypage/exchange-point/[giftId]/index.page';
import pointApi from '@/services/internal/modules/point';
import transactionsApi from '@/services/internal/modules/transactions';
import exchangePointsApi from '@/services/internal/modules/exchange-points';

jest.mock('next/router', () => require('next-router-mock'));

const GiftDetailScreenTest = () => {
  return (
    <LiffContextMockProviders>
      <GiftDetailScreen giftId="lorem" />
    </LiffContextMockProviders>
  );
};

const confirmExchangeGift = async (
  action: 'confirm' | 'cancel' = 'confirm'
) => {
  const user = userEvent.setup();
  const button = screen.getAllByRole('button')[0];
  await act(() => user.click(button));
  // open confirm modal
  const modalConfirm = screen.getByTestId('confirm-exchange-gift-modal');
  if (action === 'confirm') {
    const buttonConfirm = within(modalConfirm).getAllByRole('button')[0];
    await act(() => user.click(buttonConfirm));
  } else {
    const buttonCancel = within(modalConfirm).getAllByRole('button')[1];
    await act(() => user.click(buttonCancel));
  }

  return modalConfirm;
};

describe('pages/mypage/exchange-point/giftId/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<GiftDetailScreenTest />);
      const loading = screen.getByTestId('loading');

      expect(loading).toBeInTheDocument();
    });
  });

  describe('Server side', () => {
    it('should return giftId prop', async () => {
      const context = {
        query: { giftId: 'lorem' },
      } as unknown as GetServerSidePropsContext;
      const value = await getServerSideProps(context);
      expect(value).toEqual({
        props: { giftId: 'lorem' },
      });
    });

    it('should redirect when have not giftId query', async () => {
      const context = {
        query: {},
      } as unknown as GetServerSidePropsContext;
      const value = await getServerSideProps(context);

      expect(value).toEqual({
        redirect: { permanent: false, destination: routers.exchangePoint },
      });
    });
  });

  describe('Fetching', () => {
    describe('Fetching success', () => {
      it('should call api success return response exchangeable gift detail', async () => {
        jest.spyOn(pointApi, 'getCurrentPoint').mockResolvedValue(1000000);
        jest
          .spyOn(exchangePointsApi, 'getExchangeableGiftDetail')
          .mockResolvedValue(EXCHANGEABLE_GIFT_DETAIL);

        render(<GiftDetailScreenTest />);

        await waitFor(() => {
          const heading = screen.getAllByRole('heading');
          expect(heading.length).toEqual(1);
        });
      });
    });

    describe('Fetching failure', () => {
      it('should call api failure return error', async () => {
        jest
          .spyOn(exchangePointsApi, 'getExchangeableGiftDetail')
          .mockRejectedValue({ statusCode: 500 });

        render(<GiftDetailScreenTest />);

        await waitFor(() => {
          const button = screen.getByRole('button');
          expect(button).toBeInTheDocument();
        });
      });
    });
  });

  describe('User interactions', () => {
    beforeAll(() => {
      jest.clearAllMocks();
    });

    beforeEach(() => {
      jest
        .spyOn(exchangePointsApi, 'getExchangeableGiftDetail')
        .mockResolvedValue(EXCHANGEABLE_GIFT_DETAIL);

      jest
        .spyOn(transactionsApi, 'generateTransactionTokenExchangeGift')
        .mockResolvedValue({ transactionToken: 'lorem' });

      jest.spyOn(pointApi, 'getCurrentPoint').mockResolvedValue(1000000);
    });

    it('should close confirm exchange gift modal', async () => {
      render(<GiftDetailScreenTest />);

      await waitFor(async () => {
        const modalConfirm = await confirmExchangeGift('cancel');

        expect(modalConfirm).not.toBeInTheDocument();
      });
    });

    it('should exchange gift success', async () => {
      const user = userEvent.setup();
      jest.spyOn(exchangePointsApi, 'confirmExchangeGift').mockResolvedValue({
        gift_url: 'https://giftee.biz',
        gift_type: 'fixed',
        available_period: 'available',
      });

      render(<GiftDetailScreenTest />);

      await waitFor(async () => {
        await confirmExchangeGift();
        // open success modal
        const modalSuccess = screen.getByTestId('success-exchange-gift-modal');
        const buttons = within(modalSuccess).getAllByRole('button');
        await act(() => user.click(buttons[0]));
        await act(() => user.click(buttons[1]));

        expect(buttons[1]).toHaveFocus();
      });
    });

    it('should exchange gift failure', async () => {
      const user = userEvent.setup();
      jest.spyOn(exchangePointsApi, 'confirmExchangeGift').mockRejectedValue({
        statusCode: 500,
      });

      render(<GiftDetailScreenTest />);

      await waitFor(async () => {
        await confirmExchangeGift();
        // open error modal
        const modalError = screen.getByTestId('error-exchange-gift-modal');
        const buttons = within(modalError).getAllByRole('button');
        await act(() => user.click(buttons[0]));
        await act(() => user.click(buttons[1]));

        expect(buttons[1]).toHaveFocus();
      });
    });

    it('should reexchange gift when lost internet', async () => {
      jest
        .spyOn(transactionsApi, 'getTransactionStatus')
        .mockResolvedValue({ status: 'ready' });

      jest.spyOn(exchangePointsApi, 'confirmExchangeGift').mockRejectedValue({
        code: 'ERR_NETWORK',
      });

      render(<GiftDetailScreenTest />);

      await waitFor(async () => {
        const modalConfirm = await confirmExchangeGift();

        expect(modalConfirm).toBeInTheDocument();
      });
    });

    it('should exchange gift success when lost internet', async () => {
      jest
        .spyOn(transactionsApi, 'getTransactionStatus')
        .mockResolvedValue({ status: 'completed' });

      jest.spyOn(exchangePointsApi, 'confirmExchangeGift').mockRejectedValue({
        code: 'ERR_NETWORK',
      });

      render(<GiftDetailScreenTest />);

      await waitFor(async () => {
        await confirmExchangeGift();

        // open success modal
        const modalSuccess = screen.getByTestId('success-exchange-gift-modal');

        expect(modalSuccess).toBeInTheDocument();
      });
    });

    it('should exchange gift failure when lost internet', async () => {
      jest
        .spyOn(transactionsApi, 'getTransactionStatus')
        .mockResolvedValue({ status: 'point_reduced' });

      jest.spyOn(exchangePointsApi, 'confirmExchangeGift').mockRejectedValue({
        code: 'ERR_NETWORK',
      });

      render(<GiftDetailScreenTest />);

      await waitFor(async () => {
        await confirmExchangeGift();

        // open error modal
        const modalError = screen.getByTestId('error-exchange-gift-modal');

        expect(modalError).toBeInTheDocument();
      });
    });

    it('should reexchange gift when lost internet and call api get transaction status failure', async () => {
      jest
        .spyOn(transactionsApi, 'getTransactionStatus')
        .mockRejectedValue({ statusCode: 500 });

      render(<GiftDetailScreenTest />);

      await waitFor(async () => {
        await confirmExchangeGift();

        // open error modal
        const modalError = screen.getByTestId('error-exchange-gift-modal');

        expect(modalError).toBeInTheDocument();
      });
    });
  });
});
