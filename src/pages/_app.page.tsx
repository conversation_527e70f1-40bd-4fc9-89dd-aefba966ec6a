// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import 'react-toastify/dist/ReactToastify.css';
import 'swiper/css';
import '@/styles/css/styles.css';
import '@/styles/scss/styles.scss';
import type { AppContext, AppProps } from 'next/app';
import { LiffProvider } from '@/contexts/liff';
import { parseCookiesApp, setCookieApp } from '@/utils/cookie';
import { UNKNOWN } from '@/constants/define';
import { USER_COMPANY, USER_COMPANY_EXPIRES } from '@/constants/user';
import ToastMessage from '@/components/ToastMessage/ToastMessage';
import { META_SEO, routers } from '@/constants/routes';
import { ROUTERS } from '@/types/app';
import logger from '@/utils/logger';
import { randomUuid } from '@/utils/random';
import { useRouter } from 'next/router';

const MyApp = ({ Component, pageProps }: AppProps) => {
  const router = useRouter();

  return router.pathname === '/healthcheck' ? (
    <Component {...pageProps} />
  ) : (
    <LiffProvider {...pageProps}>
      <Component {...pageProps} />
      <ToastMessage />
    </LiffProvider>
  );
};

MyApp.getInitialProps = async ({ ctx }: AppContext) => {
  const allCookies = parseCookiesApp(ctx);

  if (typeof window === 'undefined') {
    const logInfo = {
      id: randomUuid(),
      time: new Date().toISOString(),
      status_code: ctx.res?.statusCode,
      method: ctx.req?.method,
      referer_url: ctx.req?.headers.referer,
      access_url: ctx.pathname,
      user_agent: ctx.req?.headers['user-agent'],
      peer_conne_id: allCookies.peer_conne_id,
    };
    logger.info(logInfo);
  }

  const liffId = process.env.LIFF_ID || UNKNOWN;
  const { company_id: companyIdFromQuery } = ctx.query;
  const { userCompany: companyIdFromCookie } = allCookies;
  companyIdFromQuery &&
    setCookieApp(ctx, USER_COMPANY, companyIdFromQuery.toString(), {
      maxAge: USER_COMPANY_EXPIRES,
    });
  const currentCompany = companyIdFromQuery || companyIdFromCookie;

  const appConfig = { liffId, companyId: currentCompany, cookies: allCookies };

  const metaSeoKey = Object.keys(routers).find(
    (r: string) => routers[r as keyof ROUTERS] === ctx.pathname
  );
  const metaSeo = metaSeoKey
    ? META_SEO[metaSeoKey as keyof ROUTERS]
    : META_SEO.notFound;

  return {
    pageProps: {
      appConfig,
      meta: metaSeo,
    },
  };
};

export default MyApp;
