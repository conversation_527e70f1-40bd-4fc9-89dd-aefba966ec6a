// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
export interface IThemeBoolean {
  off: string;
  on: string;
}

export interface IThemeStateColors {
  info: string;
  failure: string;
  success: string;
  warning: string;
}

export interface IThemeColors extends IThemeStateColors {
  [key: string]: string;
  primary: string;
  secondary: string;
}

export interface IThemeSizes {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  '3xl': string;
  '4xl': string;
  '5xl': string;
  '6xl': string;
  '7xl': string;
}

export interface IThemePositions {
  'bottom-left': string;
  'bottom-right': string;
  'bottom-center': string;
  'top-left': string;
  'top-center': string;
  'top-right': string;
  'center-left': string;
  center: string;
  'center-right': string;
}

export interface IThemeAligns {
  left: string;
  center: string;
  right: string;
}
