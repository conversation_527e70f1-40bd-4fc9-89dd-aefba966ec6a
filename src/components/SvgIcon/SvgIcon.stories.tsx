// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import type { Meta, Story } from '@storybook/react';
import { SvgIcon } from './SvgIcon';
import { SvgListIcons } from './icons';
import type { ISvgIconProps } from './SvgIcon';

export default {
  title: 'Components/SvgIcon',
  component: SvgIcon,
  argTypes: {
    name: {
      options: Object.keys(SvgListIcons),
      control: { type: 'select' },
    },
  },
} as Meta;

const Template: Story<ISvgIconProps> = args => <SvgIcon {...args} />;

export const Default = Template.bind({});
Default.storyName = 'SvgIcon';
Default.args = {
  name: 'CheckAccentIcon',
};
