// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
/* eslint-disable import/no-extraneous-dependencies */
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx}',
    './src/components/**/*.{js,ts,jsx,tsx}',
    './node_modules/flowbite/**/*.js',
  ],
  theme: {
    container: {
      padding: {
        DEFAULT: '1rem',
        sm: '2rem',
        lg: '4rem',
        xl: '5rem',
        '2xl': '6rem',
      },
    },
    extend: {
      borderRadius: {
        modal: '1.25rem',
      },
      fontFamily: {
        noto: ['Noto Sans JP, sans-serif'],
        serif: ['Noto Serif TC, serif'],
        hind: ['Hind, sans-serif'],
      },
      colors: {
        theme: {
          primary: '#b88770',
          secondary: '#71a0b8',
          main: '#333',
          sub: '#757575',
          pale: '#bdbdbd',
          link: '#06c',
          'error-primary': '#d32f2f',
          'error-secondary': '#fdefef',
          'error-third': '#F05252',
          'warning-primary': '#ed6c02',
          'warning-secondary': '#fff4e5',
          'info-primary': '#0288d1',
          'info-secondary': '#e5f6fe',
          'success-primary': '#2e7d32',
          'success-secondary': '#edf7ed',
          'modal-gradient-top': '#F8F2F0',
          'modal-gradient-middle': '#F8F2F0',
          'modal-gradient-bottom': '#F8F3F1',
        },
        gray: {
          3: '#828282',
          5: '#f2f2f2',
          8: '#e2e2e2',
          9: '#eee',
          10: '#e6e6e6',
          20: '#ccc',
          30: '#b3b3b3',
          40: '#999',
          50: '#808080',
          60: '#666',
          70: '#4d4d4d',
          80: '#333',
        },
        calendar: {
          yellow: '#FEDD00',
          orange: '#EE7910',
        },
      },
      backgroundImage: {
        'partner-group': "url('/partner-group-bg.png')",
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/line-clamp'),
    require('flowbite/plugin'),
  ],
};
