// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import classNames from 'classnames';
import type { ComponentProps, FC, PropsWithChildren } from 'react';
import { styles } from './Modal.styles';
import { useModalContext } from './ModalContext';

export type IModalBodyProps = PropsWithChildren<ComponentProps<'div'>>;

export const ModalBody: FC<IModalBodyProps> = ({
  children,
  className,
  ...props
}) => {
  const { popup } = useModalContext();
  const theme = styles.body;

  return (
    <div
      className={classNames(
        theme.base,
        {
          [theme.popup]: popup,
        },
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};
