// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { twMerge } from 'tailwind-merge';
import { ComponentProps, FC } from 'react';
import { styles } from './GiftContent.styles';

export interface GiftContentTheme {
  wrapper: string;
  base: string;
  title: string;
  company: string;
  exchangedAt: string;
  exchangedExpire: string;
}

export interface GiftContentProps extends ComponentProps<'div'> {
  title?: string;
  company?: string;
  description?: string;
  exchangedAt?: string;
  exchangedExpire?: string;
  customTitleClassName?: string;
}

export const GiftContent: FC<GiftContentProps> = ({
  title,
  company,
  exchangedAt,
  exchangedExpire,
  customTitleClassName,
  className,
  children,
  ...props
}) => {
  return (
    <div className={twMerge(styles.wrapper, className)} {...props}>
      <div className={styles.base}>
        {company && (
          <h5 title={company} className={styles.company}>
            {company}
          </h5>
        )}
        {exchangedAt && <div className={styles.exchangedAt}>{exchangedAt}</div>}
      </div>

      {title && (
        <h3 className={twMerge(styles.title, customTitleClassName)}>{title}</h3>
      )}

      {exchangedExpire && (
        <p className={styles.exchangedExpire}>{exchangedExpire}</p>
      )}

      {children}
    </div>
  );
};
