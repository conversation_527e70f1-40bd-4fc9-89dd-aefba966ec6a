// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen } from '@testing-library/react';
import MyPageTopScreen from '@/pages/mypage/top.page';
import { LiffContextMockProviders } from '@/test/mocks';

jest.mock('next/router', () => require('next-router-mock'));

const MyPageTopScreenTest = () => {
  return (
    <LiffContextMockProviders>
      <MyPageTopScreen />
    </LiffContextMockProviders>
  );
};

describe('pages/mypage/top/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<MyPageTopScreenTest />);
      const menu = screen.getAllByRole('listitem');

      expect(menu.length).toEqual(5);
    });
  });
});
