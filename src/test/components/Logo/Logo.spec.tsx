// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen } from '@testing-library/react';
import { Logo } from '@/components/Logo';

describe('components/Logo/Logo.spec.tsx', () => {
  describe('A11y', () => {
    it('should have `role="img"` by default', () => {
      render(<Logo />);
      const logo = screen.getByRole('img');

      expect(logo).toBeInTheDocument();
    });
  });

  describe('Prop', () => {
    it('should be box when `box={true}`', () => {
      render(<Logo box aria-label="My logo" />);
      const logo = screen.getByLabelText('My logo');

      expect(logo.classList.contains('w-[140px]')).toBe(true);
    });
  });
});
