/* eslint-disable @typescript-eslint/no-explicit-any */
// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { useRouter } from 'next/router';
import { useLiff } from '@/contexts/liff';
import { useSessionStorage } from '@/hooks/use-storage';
import { routers } from '@/constants/routes';
import { ICheckinResponse } from '@/types/checkin';
import { USER_CHECKIN_RESULT } from '@/constants/user';
import { Button } from '@/components/Button';
import { BoxContent } from '@/components/BoxContent';
import { redirectToLineChatRoomOAWithCheckUtou } from '@/utils/redirect';
import { formatCurrentPoint } from '@/utils/convert';
import Image from 'next/image';
import GOLD_GYM_IMG from '@/assets/images/GoldGym.png';
import { EStatusChecking } from '@/constants/enum';
import { sendDataLayerEvent } from '@/utils';
import { URL_SOURCE } from '@/constants/checkin';

const CheckinResultScreen = () => {
  const router = useRouter();
  const { liff } = useLiff();

  const [userCheckinResult] = useSessionStorage<ICheckinResponse>({
    key: USER_CHECKIN_RESULT,
    callback: (value: ICheckinResponse) => {
      if (value) {
        sendDataLayerEvent('Checkin success', {
          At: router.pathname,
        });
      }
      !value && router.push(routers.checkinReadQR);
    },
  });

  const backChatRoomHandler = () => {
    redirectToLineChatRoomOAWithCheckUtou(liff);
  };

  const checkinCalendarHandler = async () => {
    try {
      sessionStorage.setItem(URL_SOURCE, 'checkin-result');
      router.push(routers.checkinCalendar);
    } catch {
      throw new Error();
    }
  };

  return (
    <>
      <div className="container mx-auto">
        <BoxContent>
          <BoxContent.Body className="pt-[96px]">
            {userCheckinResult && userCheckinResult.success ? (
              <div className="text-center">
                <h3 className="text-base font-bold mb-8">
                  【{userCheckinResult?.company_name}】 <br />
                  ポイントを獲得しました <br />
                  {userCheckinResult.company_id === 'C0003' &&
                    'スタンプを獲得しました'}
                </h3>
                <p className="font-bold text-theme-primary relative w-max mx-auto pr-[68px]">
                  <span className="text-3xl">＋</span>
                  <span className="text-[38px] leading-[38px] font-hind">
                    {userCheckinResult.point_amount_checkin}
                  </span>
                  <span className="text-[32px] leading-[32px]"> ポイント</span>
                  <Image
                    className="absolute right-0 top-[-5px] text-white flex items-center justify-center w-12 h-12 rounded-[50%]"
                    width={48}
                    height={48}
                    alt=""
                    src={userCheckinResult.company_image || GOLD_GYM_IMG}
                  />
                </p>

                <div className="inline-flex gap-x-6 justify-between mt-10 px-11 py-4 bg-[#F8F3F1] font-bold text-[#333333] h-[50px] items-center">
                  <p className="text-[16px]">保有ポイント</p>
                  <p className="text-[16px]">
                    <span className="text-[24px] mr-1.5">
                      {formatCurrentPoint(
                        userCheckinResult.current_total_point || 0
                      )}
                    </span>
                    pt
                  </p>
                </div>
              </div>
            ) : userCheckinResult?.company_id === 'C0003' ? (
              <div className="text-center">
                <h3 className="text-base font-bold mb-4">
                  QRコードを読み取りました
                </h3>
                <p className="font-bold leading-6">
                  【{userCheckinResult?.company_name}】 <br />
                  本日のポイントは獲得済です
                  <br />
                  {userCheckinResult?.stamp_status ===
                  EStatusChecking.time_locked ? (
                    'スタンプは付与制限中です'
                  ) : userCheckinResult?.stamp_status ===
                    EStatusChecking.with_only_stamp ? (
                    <div className="flex items-center flex-col">
                      スタンプを獲得しました
                      <Image
                        className="mt-4 text-white bg-[#D9D9D9] rounded-[50%] flex items-center justify-center w-12 h-12"
                        width={48}
                        height={48}
                        alt=""
                        src={userCheckinResult.company_image || GOLD_GYM_IMG}
                      />
                    </div>
                  ) : (
                    '本日のスタンプは獲得済です'
                  )}
                </p>
              </div>
            ) : (
              <div className="text-center">
                <h3 className="text-base font-bold mb-4">
                  QRコードを読み取りました
                </h3>
                <p className="font-bold leading-6">
                  【{userCheckinResult?.company_name}】 <br />
                  本日のポイントは獲得済です
                </p>
              </div>
            )}
          </BoxContent.Body>
          <BoxContent.Footer>
            <Button
              fullSized
              type="submit"
              size="xl"
              color="primary"
              onClick={backChatRoomHandler}
            >
              トーク画面に戻る
            </Button>
            <Button
              fullSized
              size="xl"
              color="primary"
              outline
              className="mt-5"
              onClick={() =>
                router.push({
                  pathname: routers.pointHistory,
                })
              }
            >
              ポイント履歴を確認する
            </Button>
            {userCheckinResult && userCheckinResult.company_id === 'C0003' && (
              <Button
                fullSized
                size="xl"
                color="primary"
                outline
                className="mt-5"
                onClick={checkinCalendarHandler}
              >
                入館カレンダーをみる
              </Button>
            )}
          </BoxContent.Footer>
        </BoxContent>
      </div>
    </>
  );
};

export default CheckinResultScreen;
