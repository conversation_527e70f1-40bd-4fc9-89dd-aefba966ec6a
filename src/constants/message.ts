// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
export const Message = {
  error: {
    internet: 'インターネット問題が発生しました。',
    server: '想定しないエラーが発生しました。',
    wrongQr: '正しいQRコードを読み込んでください。',
    chatMessagePermision: 'トークへのメッセージ送信を許可してください。',
    friendNotAdd: '友だち追加してください。',
    canNotSendMessage: 'メッセージがトーク画面へ送信できません。',
    canNotChangeRichMenu: 'リッチメニューが切り替えられません。',
    wrongFormatImage: '画像の形式はpng、jpgでお願い致します。',
    canNotApplyLottery: '画像アップロードが失敗しました。',
    canNotUpdateDeliverySetting: 'LINEメッセージ配信設定が失敗されました。',
    canNotGetLINEFriend: 'LINEの友達追加状態は不明です。',
    canNotRegister: '会員登録が失敗しました。',
    maximumCapacityFileSize:
      '画像が大きすぎます。5MBを超える画像は選択できません。',
    membership: 'お客さまはすでに退会済です。',
    expired: '期限切れのリンク',
  },
  success: {
    updateDeliverySetting: 'LINEメッセージの配信設定を変更しました',
    updateProfile: '会員登録情報を変更しました',
  },
  validation: {
    required: '${field}を入力してください。',
    emailFormatInvalid:
      '入力されたメールアドレスの形式はご登録いただけません。',
    emailExisted: 'すでに同じメールアドレスが登録されています。',
    passwordLengthInvalid:
      '英数字を組み合わせた8文字以上20文字以内の文字列を入力してください。',
    passwordConfirmRequired: 'パスワードを再入力してください。',
    passwordConfirmNotSame: 'パスワードとパスワード再入力が一致しません。',
  },
};
