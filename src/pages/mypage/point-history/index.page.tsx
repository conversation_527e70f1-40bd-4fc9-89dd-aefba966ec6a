// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { Tabs } from '@/components/Tabs';
import {
  TABS_POINT_HISTORY,
  TAB_EXCHANGED_GIFT,
  TAB_POINT_HISTORY,
} from '@/constants/mypage';
import { routers } from '@/constants/routes';
import { useDetectTab } from '@/hooks/use-detect-tab';
import { useRouter } from 'next/router';
import { FC } from 'react';
import MypageLayout from '../components/MypageLayout';
import { UserCurrentPoint } from '../components/UserCurrentPoint';
import { ExchangedGiftList } from './components/ExchangedGiftList';
import { PointHistoryList } from './components/PointHistoryList';
import { NextSeo } from 'next-seo';

const MyPageTopScreen: FC = () => {
  const router = useRouter();
  const { tabValue } = useDetectTab({
    name: 'tab',
    tabs: TABS_POINT_HISTORY,
    defaultTab: TAB_POINT_HISTORY,
  });

  return (
    <>
      <NextSeo
        title={
          router.query?.tab === TAB_EXCHANGED_GIFT
            ? 'ポイント交換履歴'
            : 'ポイント履歴'
        }
      />
      <MypageLayout spacingTop="lg" spacingBottom="md">
        <div className="flex flex-col flex-1 gap-6">
          <UserCurrentPoint
            layout="full"
            link={{ href: routers.howToGetPoints }}
            className="h-[160px]"
            padding="md"
          />

          <Tabs
            value={tabValue}
            variant="separate"
            defaultValue={TAB_POINT_HISTORY}
            className="flex flex-col flex-1"
          >
            <Tabs.List>
              {router.query?.tab === TAB_EXCHANGED_GIFT ? (
                <Tabs.Tab className="p-[11px]" value={TAB_EXCHANGED_GIFT}>
                  交換履歴
                </Tabs.Tab>
              ) : (
                <Tabs.Tab className="p-[11px]" value={TAB_POINT_HISTORY}>
                  履歴
                </Tabs.Tab>
              )}
            </Tabs.List>
            {router.query?.tab === TAB_EXCHANGED_GIFT ? (
              <Tabs.Panel
                value={TAB_EXCHANGED_GIFT}
                className="flex flex-col flex-1"
              >
                <ExchangedGiftList />
              </Tabs.Panel>
            ) : (
              <PointHistoryList />
            )}
          </Tabs>
        </div>
      </MypageLayout>
    </>
  );
};

export default MyPageTopScreen;
