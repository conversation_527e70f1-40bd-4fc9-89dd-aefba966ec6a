// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen } from '@testing-library/react';
import { ChevronDownIcon } from '@heroicons/react/24/solid';
import { Select } from '@/components/Select';

describe('components/Select/Select.spec.tsx', () => {
  describe('A11y', () => {
    it('should have `role="combobox"` by default', () => {
      render(<Select />);
      const textInput = screen.getByRole('combobox');
      expect(textInput).toBeInTheDocument();
    });

    it('should have Icon if selected ', () => {
      render(<Select rightIcon={ChevronDownIcon} />);
      const page = screen.getByTestId('right-icon');

      expect(page).toBeInTheDocument();
    });
  });

  describe('Prop', () => {
    it('should be disabled when `disabled={true}`', () => {
      render(<Select disabled />);

      expect(screen.getByRole('combobox')).toBeDisabled();
    });
  });
});
