// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import type { Meta, Story } from '@storybook/react';
import type { ITextInputProps } from './TextInput';
import { TextInput } from './TextInput';

export default {
  title: 'Components/TextInput',
  component: TextInput,
} as Meta;

const Template: Story<ITextInputProps> = args => <TextInput {...args} />;

export const Default = Template.bind({});
Default.storyName = 'TextInput';
Default.args = {
  color: 'info',
  sizing: 'md',
  placeholder: 'Default placeholder example',
};
