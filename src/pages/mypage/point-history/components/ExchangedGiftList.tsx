// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { useState } from 'react';
import { useRouter } from 'next/router';
import { orderBy, isNil } from 'lodash';
import { randomUuid } from '@/utils/random';
import { Tabs } from '@/components/Tabs';
import { useFetch } from '@/hooks/use-fetch';
import { useDetectTab } from '@/hooks/use-detect-tab';
import exchangePointsApi from '@/services/internal/modules/exchange-points';
import {
  IExchangedGiftDetail,
  IExchangedGiftDetailResponse,
  IExchangedGiftListParams,
} from '@/types/exchange-points';
import { ExchangedGiftListLoading } from './ExchangedGiftListLoading';
import { ExchangedGiftListUsed } from './ExchangedGiftListUsed';
import { ExchangedGiftListAvailable } from './ExchangedGiftListAvailable';
import { ExchangedGiftListUsedEmpty } from './ExchangedGiftListUsedEmpty';
import { ExchangedGiftListUsedError } from './ExchangedGiftListUsedError';
import { ExchangedGiftListAvailableEmpty } from './ExchangedGiftListAvailableEmpty';
import { ExchangedGiftListAvailableError } from './ExchangedGiftListAvailableError';
import {
  TABS_EXCHANGED_GIFT,
  TAB_EXCHANGED_GIFT_AVAILABLE,
  TAB_EXCHANGED_GIFT_USED,
} from '@/constants/mypage';

export type GiftTabType =
  | typeof TAB_EXCHANGED_GIFT_AVAILABLE
  | typeof TAB_EXCHANGED_GIFT_USED;

export interface HandleToggleGiftProps {
  uid: string;
  type: GiftTabType;
}

export interface HandleSortGiftListProps {
  data: IExchangedGiftDetailResponse[];
  sort?: 'asc' | 'desc';
}

export const ExchangedGiftList = () => {
  const router = useRouter();
  const { tabValue, setTabValue } = useDetectTab({
    name: 'type',
    tabs: TABS_EXCHANGED_GIFT,
    defaultTab: TAB_EXCHANGED_GIFT_AVAILABLE,
  });
  const [giftsUsed, setGiftsUsed] = useState<IExchangedGiftDetail[]>([]);
  const [giftsAvailable, setGiftsAvailable] = useState<IExchangedGiftDetail[]>(
    []
  );

  const handleSortGiftList = ({
    data = [],
    sort = 'desc',
  }: HandleSortGiftListProps): IExchangedGiftDetail[] => {
    const giftListSort = orderBy(
      // additional uuid to gift item
      data.map(item => ({ ...item, uid: randomUuid(), loading: false })),
      // sort desc gift by exchanged_at
      [gift => new Date(gift.exchanged_at)],
      [sort]
    );

    return giftListSort;
  };

  const {
    fetch: getExchangedGiftListAvailableHandler,
    isFetching: isFetchingGiftListAvailable,
    isErrorFetching: isErrorFetchingGiftListAvailable,
  } = useFetch<IExchangedGiftDetailResponse[], IExchangedGiftListParams>({
    fetcher: exchangePointsApi.getExchangedGiftList,
    handleResponse: (gifts: IExchangedGiftDetailResponse[]) => {
      const result = handleSortGiftList({ data: gifts, sort: 'asc' });
      setGiftsAvailable(result);
      return result;
    },
    initialValue: [],
    payload: { is_available: '1' },
  });

  const {
    fetch: getExchangedGiftListUsedHandler,
    isFetching: isFetchingGiftListUsed,
    isErrorFetching: isErrorFetchingGiftListUsed,
  } = useFetch<IExchangedGiftDetailResponse[], IExchangedGiftListParams>({
    fetcher: exchangePointsApi.getExchangedGiftList,
    handleResponse: (gifts: IExchangedGiftDetailResponse[]) => {
      const result = handleSortGiftList({ data: gifts });
      setGiftsUsed(result);
      return result;
    },
    initialValue: [],
    payload: { is_available: '0' },
  });

  const onTabChange = (tab: string) => {
    setTabValue(tab);
    router.replace({ query: { ...router.query, type: tab } });
  };

  const isTabExchangedAvailable = (type: GiftTabType): boolean => {
    return type === TAB_EXCHANGED_GIFT_AVAILABLE;
  };

  const handleLoading = ({
    type,
    uid,
    loading,
  }: {
    type: GiftTabType;
    uid: string;
    loading: boolean;
  }) => {
    const setData = isTabExchangedAvailable(type)
      ? setGiftsAvailable
      : setGiftsUsed;

    setData(prev => {
      // create a shallow copy of the original array
      const cloned = [...prev];
      // find position gift need update
      const index = prev.findIndex(g => g.uid === uid);
      // if gift not found
      if (index < 0) throw new Error('Gift not found');
      // update gift loading
      cloned[index] = { ...cloned[index], loading };

      return cloned;
    });
  };

  const toggleGiftUsageHandler = async (
    gift: IExchangedGiftDetail,
    type: GiftTabType
  ) => {
    try {
      handleLoading({ type, uid: gift.uid, loading: true });
      await exchangePointsApi.toggleGiftUsage(gift.exchange_id, {
        is_used: isTabExchangedAvailable(type) ? '1' : '0',
      });
      return { isSuccess: true, statusCode: 200 };
    } catch (error: unknown) {
      const errors = error as { errors: { statusCode: number } };
      const errorCode = errors?.errors?.statusCode;
      return { isSuccess: false, statusCode: errorCode };
    } finally {
      handleLoading({ type, uid: gift.uid, loading: false });
    }
  };

  const handleToggleGift = async ({ uid, type }: HandleToggleGiftProps) => {
    // get array gift of tab active
    const data = isTabExchangedAvailable(type) ? giftsAvailable : giftsUsed;
    // usage to remove gift from old array
    const setOldData = isTabExchangedAvailable(type)
      ? setGiftsAvailable
      : setGiftsUsed;
    // usage to append gift to new array
    const setNewData = isTabExchangedAvailable(type)
      ? setGiftsUsed
      : setGiftsAvailable;
    // find gift
    const gift = data.find(g => g.uid === uid);
    // if gift not found
    if (isNil(gift)) return;
    // update to database
    const { isSuccess, statusCode } = await toggleGiftUsageHandler(gift, type);
    /**
     * when toggle gift usage return statusCode === 403, it mean this gift is expried time
     * so, we need to refetch api to get new gift exchanged list
     */
    if (statusCode === 403) {
      // reset state
      setGiftsUsed([]);
      setGiftsAvailable([]);
      // call api
      await Promise.all([
        getExchangedGiftListAvailableHandler({ is_available: '1' }),
        getExchangedGiftListUsedHandler({ is_available: '0' }),
      ]);
    }
    // if toggle failure, stop process
    if (!isSuccess) return;
    // remove gift from old array gift
    setOldData(prev => prev.filter(g => g.uid !== uid));
    // insert gift to new array gift
    setNewData(prev =>
      handleSortGiftList({
        data: [...prev, gift],
        sort: isTabExchangedAvailable(type) ? 'desc' : 'asc',
      })
    );
  };

  return (
    <div className="space-y-4">
      <Tabs
        value={tabValue}
        defaultValue="used"
        variant="separate"
        onTabChange={onTabChange}
      >
        <Tabs.List>
          <Tabs.Tab
            value={TAB_EXCHANGED_GIFT_AVAILABLE}
            className="text-[13px] font-normal p-1.5 border-b-2 max-[320px]:!text-[10px]"
          >
            使えるギフト
          </Tabs.Tab>
          <Tabs.Tab
            value={TAB_EXCHANGED_GIFT_USED}
            className="text-xs font-normal leading-5 p-1.5 border-b-2 max-[320px]:!text-[10px]"
          >
            利用済み・期間切れのギフト
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value={TAB_EXCHANGED_GIFT_AVAILABLE}>
          {isFetchingGiftListAvailable ? (
            <ExchangedGiftListLoading />
          ) : isErrorFetchingGiftListAvailable ? (
            <ExchangedGiftListAvailableError
              retry={getExchangedGiftListAvailableHandler}
            />
          ) : giftsAvailable && giftsAvailable.length > 0 ? (
            <ExchangedGiftListAvailable
              gifts={giftsAvailable}
              toggle={handleToggleGift}
            />
          ) : (
            <ExchangedGiftListAvailableEmpty />
          )}
        </Tabs.Panel>

        <Tabs.Panel value={TAB_EXCHANGED_GIFT_USED}>
          {isFetchingGiftListUsed ? (
            <ExchangedGiftListLoading />
          ) : isErrorFetchingGiftListUsed ? (
            <ExchangedGiftListUsedError
              retry={getExchangedGiftListUsedHandler}
            />
          ) : giftsUsed && giftsUsed.length > 0 ? (
            <ExchangedGiftListUsed
              gifts={giftsUsed}
              toggle={handleToggleGift}
            />
          ) : (
            <ExchangedGiftListUsedEmpty />
          )}
        </Tabs.Panel>
      </Tabs>
    </div>
  );
};
