// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { IGetLotteryResponse } from '@/types/lottery';

export const LOTTERY_DETAIL_RESPONSE: IGetLotteryResponse = {
  verify: 1,
  lottery: {
    summary: {
      top_text: 'Top text',
      application_times: 'once_a_day',
      notes: 'Lorem ipsum',
      prize_name: '「ゴールドジムの抽選の景品名」',
      same_screenshot_img_link: 'https://example.com/upload/image.png',
      target_screenshot_explanation: '「ゴールドジムの画像」',
      ended_at: '2030-03-31T11:11:11.625Z',
      prize_img_link: 'https://example.com/upload/image.png',
      start_at: '2010-02-02T11:11:11.625Z',
    },
    num_of_winners: 11,
  },
  hasPreviewFlag: false,
  lotteryId: 'L111',
};
