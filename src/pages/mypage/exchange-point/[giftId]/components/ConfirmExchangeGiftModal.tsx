// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { Dispatch, FC, SetStateAction } from 'react';
import { Modal } from '@/components/Modal';
import { Button } from '@/components/Button';
import { Spinner } from '@/components/Spinner';
import { IExchangeableGiftDetailResponse } from '@/types/exchange-points';
import { transformText } from '@/utils/text';
import { styles } from './styles';

interface ConfirmExchangeGiftModalProps {
  gift: IExchangeableGiftDetailResponse;
  loading: boolean;
  isShowModal: boolean;
  setIsShowModal: Dispatch<SetStateAction<boolean>>;
  submit: () => Promise<void>;
}

const ConfirmExchangeGiftModal: FC<ConfirmExchangeGiftModalProps> = ({
  gift,
  loading,
  isShowModal,
  setIsShowModal,
  submit,
}) => {
  return (
    <Modal
      size="md"
      show={isShowModal}
      data-testid="confirm-exchange-gift-modal"
      innerClass="relative w-full rounded-modal bg-white shadow max-h-full overflow-y-auto"
      innerStyle={{ maxHeight: '80vh', paddingBlock: 0 }}
    >
      <Modal.Header>
        <h3 className="text-theme-primary text-lg text-center font-bold">
          本当に交換しますか？
        </h3>
      </Modal.Header>
      <Modal.Body>
        <div className="mt-2.5 mb-2">
          <div className="space-y-1.5">
            <h5
              title={gift.gift_company_name}
              className="text-sm text-theme-main line-clamp-2"
            >
              {gift.gift_company_name}
            </h5>
            <h3
              title={gift.gift_name}
              className="text-base text-theme-main font-bold line-clamp-3"
            >
              {gift.gift_name}
            </h3>
          </div>

          <div className="inline-block relative mt-4">
            <span className="text-[13px] text-theme-main leading-6">
              必要ポイント数
            </span>
            <span className="text-lg text-theme-secondary font-semibold font-hind leading-6 absolute bottom-[-2px] left-[calc(100%+16px)]">
              {`${gift.point_amount}pt`}
            </span>
          </div>

          <div className="mt-6">
            <pre
              dangerouslySetInnerHTML={{
                __html: transformText(gift.gift_notes_confirmation, styles),
              }}
              className={styles.base}
            />
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer className="space-y-4">
        <Button
          customSpan="!px-5"
          fullSized
          size="xl"
          color="primary"
          disabled={loading}
          onClick={submit}
        >
          {loading && <Spinner size="lg" color="primary" className="mr-3" />}
          交換する
        </Button>
        <Button
          customSpan="!px-5"
          outline
          fullSized
          size="xl"
          color="primary"
          disabled={loading}
          onClick={() => setIsShowModal(false)}
        >
          戻る
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ConfirmExchangeGiftModal;
