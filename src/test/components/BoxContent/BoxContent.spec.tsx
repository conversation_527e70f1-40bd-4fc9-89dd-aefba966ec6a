// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen } from '@testing-library/react';
import { Button } from '@/components/Button';
import { BoxContent } from '@/components/BoxContent';

const TestBoxContent = (): JSX.Element => {
  return (
    <BoxContent>
      <BoxContent.Header>
        <h3 className="text-theme-main text-lg text-center font-bold">
          Terms of Service
        </h3>
      </BoxContent.Header>
      <BoxContent.Body className="pt-6 space-y-6 mb-auto">
        <p className="text-base leading-relaxed text-gray-500 dark:text-gray-400">
          Lorem ipsum dolor sit amet, consectetur adipisicing elit. Laboriosam
          unde tenetur eius odit doloribus accusamus repudiandae quis at? Iure
          quos placeat voluptatibus modi corporis? Unde ratione dicta iste.
          Numquam, porro!
        </p>
      </BoxContent.Body>
      <BoxContent.Footer className="space-y-2">
        <Button fullSized size="lg">
          I accept
        </Button>
      </BoxContent.Footer>
    </BoxContent>
  );
};

describe('components/BoxContent/BoxContent.spec.tsx', () => {
  describe('A11y', () => {
    it('should component mounted', async () => {
      render(<TestBoxContent />);

      expect(screen.getByRole('button')).toBeDefined();
    });

    it('should component render ui', async () => {
      render(<TestBoxContent />);

      expect(screen.getByRole('button')).toHaveTextContent('I accept');
    });
  });
});
