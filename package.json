{"name": "wellnessconnect-fe", "version": "0.1.0", "private": true, "scripts": {"dev": "node server.js", "build": "next build", "build:storybook": "build-storybook", "start": "next start", "start:local": "next start -p 2999", "start:dev": "next dev", "start:storybook": "start-storybook -p 6006", "lint": "next lint", "check:type": "bash -c tsc --noEmit", "check:format": "prettier --check .", "check:style": "stylelint \"**/*.{css,scss,sass}\"", "check:lint": "eslint . --ext .ts,.tsx,.js,.jsx", "fix:format": "prettier --write .", "fix:style": "stylelint \"**/*.{css,scss,sass}\" --fix", "fix:lint": "eslint --fix .", "fix:all": "yarn fix:format && yarn fix:style && yarn fix:lint", "prepare": "husky install", "build-local": "yarn build && yarn start:local", "proxy": "npx local-ssl-proxy --key ./.certificate/localhost-key.pem --cert ./.certificate/localhost.pem --source 3000 --target 2999", "test": "jest --watch --coverage --watchAll", "test:ci": "jest --ci"}, "lint-staged": {"*.{ts,tsx}": ["yarn check:type"], "*.{css,scss,sass}": ["yarn check:style"], "*.{ts,js,tsx,jsx}": ["yarn check:format", "yarn check:lint"]}, "dependencies": {"@heroicons/react": "^2.0.13", "@holiday-jp/holiday_jp": "^2.4.0", "@hookform/resolvers": "^2.9.10", "@line/liff": "^2.21.4", "@microsoft/applicationinsights-web": "^3.1.2", "@types/react-gtm-module": "^2.0.3", "axios": "^1.2.1", "browser-image-compression": "^2.0.0", "classnames": "^2.3.2", "crypto-js": "^4.1.1", "dayjs": "^1.11.9", "flowbite": "^1.5.5", "heic2any": "^0.0.4", "lodash": "^4.17.21", "next": "13.5.9", "next-seo": "^6.6.0", "nookies": "^2.5.2", "pino": "^9.4.0", "punycode": "^2.3.0", "react": "18.2.0", "react-dom": "18.2.0", "react-gtm-module": "^2.0.11", "react-hook-form": "^7.41.1", "react-select": "^5.8.3", "react-toastify": "^9.1.1", "swiper": "^11.1.9", "tailwind-merge": "^1.14.0", "validator": "^13.9.0", "yup": "^0.32.11"}, "devDependencies": {"@babel/core": "^7.20.5", "@commitlint/cli": "^17.3.0", "@commitlint/config-conventional": "^17.3.0", "@storybook/addon-actions": "^6.5.15", "@storybook/addon-essentials": "^6.5.15", "@storybook/addon-interactions": "^6.5.15", "@storybook/addon-links": "^6.5.15", "@storybook/addon-postcss": "^2.0.0", "@storybook/builder-webpack5": "^6.5.15", "@storybook/manager-webpack5": "^6.5.15", "@storybook/react": "^6.5.15", "@storybook/testing-library": "^0.0.13", "@svgr/webpack": "^6.5.1", "@tailwindcss/line-clamp": "^0.4.2", "@tailwindcss/typography": "^0.5.8", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/crypto-js": "^4.1.1", "@types/lodash": "^4.14.191", "@types/node": "^18.11.15", "@types/nookies": "^2.0.3", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.9", "@types/react-toastify": "^4.1.0", "@types/testing-library__jest-dom": "^5.14.5", "@types/validator": "^13.7.17", "@typescript-eslint/eslint-plugin": "^5.46.1", "@typescript-eslint/parser": "^5.46.1", "autoprefixer": "^10.4.13", "babel-loader": "^8.3.0", "eslint": "8.22.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-next": "^13.0.7", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-storybook": "^0.6.8", "eslint-plugin-testing-library": "^5.10.3", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^8.0.2", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "jest-transformer-svg": "^2.0.1", "lint-staged": "^13.1.0", "local-ssl-proxy": "^1.3.0", "next-router-mock": "^0.9.3", "postcss": "^8.4.20", "prettier": "^2.8.1", "sass": "^1.57.1", "storybook-addon-next-router": "^4.0.2", "stylelint": "^14.16.0", "stylelint-checkstyle-formatter": "^0.1.2", "stylelint-config-prettier": "^9.0.4", "stylelint-config-standard": "^29.0.0", "stylelint-scss": "^4.3.0", "tailwindcss": "^3.2.4", "typescript": "^4.9.4"}, "browserslist": "cover 99.5%", "engines": {"node": ">= 16.0.0 <22.9.0"}}