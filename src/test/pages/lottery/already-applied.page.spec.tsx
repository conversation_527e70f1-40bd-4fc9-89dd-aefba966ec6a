// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { fireEvent, render, screen } from '@testing-library/react';
import type { Liff } from '@line/liff';
import { LiffContextMockProviders } from '@/test/mocks';
import LotteryAlreadyApplied from '@/pages/lottery/already-applied.page';

jest.mock('next/router', () => require('next-router-mock'));
// eslint-disable-next-line @typescript-eslint/no-var-requires
const useRouter = jest.spyOn(require('next/router'), 'useRouter');

const useMockRouter = (query: Record<string, string | string[]> = {}) => {
  return useRouter.mockReturnValue({
    query,
    push: jest.fn(),
  });
};

const TestLotteryAlreadyApplied = (): JSX.Element => {
  const liffContextValue = {
    liff: {
      closeWindow: () => ({}),
    } as unknown as Liff,
  };
  return (
    <LiffContextMockProviders contextValue={liffContextValue}>
      <LotteryAlreadyApplied />
    </LiffContextMockProviders>
  );
};

describe('pages/lottery/already-applied.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      useMockRouter();

      render(<TestLotteryAlreadyApplied />);
      const text = screen.getByText('期間中には1回のみチャレンジ可能です。');

      expect(text).toBeInTheDocument();
    });
  });

  describe('User interactions', () => {
    it('should trigger `onClick` button', async () => {
      useMockRouter({ is_once_a_day: 'true' });

      render(<TestLotteryAlreadyApplied />);
      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(button).toBeInTheDocument();
    });
  });
});
