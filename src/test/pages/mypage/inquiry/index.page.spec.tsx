// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { render, screen } from '@testing-library/react';
import InquiryScreen from '@/pages/mypage/inquiry.page';
import { LiffContextMockProviders } from '@/test/mocks';

jest.mock('next/router', () => require('next-router-mock'));

interface TestInquiryScreenProps {
  isLoggedIn?: boolean;
}

const TestInquiryScreen: FC<TestInquiryScreenProps> = ({
  isLoggedIn = true,
}) => {
  const contextValue = { isLoggedIn };

  return (
    <LiffContextMockProviders contextValue={contextValue}>
      <InquiryScreen />
    </LiffContextMockProviders>
  );
};

describe('pages/mypage/inquiry/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document when signed in', async () => {
      render(<TestInquiryScreen />);
      const href = screen.getByRole('link');

      expect(href).toBeInTheDocument();
    });

    it('should render in the document when not sign in', async () => {
      render(<TestInquiryScreen isLoggedIn={false} />);
      const loading = screen.getByRole('status');

      expect(loading).toBeInTheDocument();
    });
  });
});
