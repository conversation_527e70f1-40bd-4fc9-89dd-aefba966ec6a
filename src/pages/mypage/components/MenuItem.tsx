// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { SvgIcon } from '@/components/SvgIcon';
import { ISvgListICons } from '@/components/SvgIcon/icons';
import Link from 'next/link';
import { FC, ReactNode } from 'react';

export interface IMenuItemProps {
  label: string;
  icon?: keyof ISvgListICons;
  arrow?: keyof ISvgListICons;
  to?: string;
  customContent?: ReactNode;
  noArrow?: boolean;
}

const MenuItem: FC<IMenuItemProps> = ({
  label,
  icon,
  arrow,
  to,
  customContent,
  noArrow,
}) => {
  return (
    <Link href={to || ''}>
      <li className="py-[22px] border-b border-gray-10 cursor-pointer">
        <div data-testid="menu-item" className="relative flex items-center">
          {icon && <SvgIcon name={icon}></SvgIcon>}
          {customContent ?? (
            <span className="ml-2 text-sm font-normal">{label}</span>
          )}
          <div className="absolute right-0">
            {!noArrow && <SvgIcon name={arrow ?? 'ArrowRight'}></SvgIcon>}
          </div>
        </div>
      </li>
    </Link>
  );
};

export default MenuItem;
