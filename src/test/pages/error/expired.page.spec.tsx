// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { Message } from '@/constants/message';
import ExpiredErrorScreen from '@/pages/error/expired.page';
import { LiffContextMockProviders } from '@/test/mocks';
import { act, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

jest.mock('next/router', () => require('next-router-mock'));

const TestExpiredErrorScreen = (): JSX.Element => {
  const liffContextValue = {
    error: { message: Message.error.expired },
  };

  return (
    <LiffContextMockProviders contextValue={liffContextValue}>
      <ExpiredErrorScreen />
    </LiffContextMockProviders>
  );
};
describe('pages/error/expired.page.spec.tsx', () => {
  it('should render the expired error message', () => {
    render(<TestExpiredErrorScreen />);
    expect(
      screen.getByText('ポイント付与期間は終了しています。')
    ).toBeInTheDocument();
  });

  it('should render Logo component', () => {
    render(<TestExpiredErrorScreen />);
    expect(screen.getByRole('img', { name: 'Logo' })).toBeInTheDocument();
  });

  it('should render BoxContent component', () => {
    render(<TestExpiredErrorScreen />);
    expect(screen.getByRole('region')).toBeInTheDocument();
  });

  describe('User interactions', () => {
    it('should trigger `onClick` button', async () => {
      const user = userEvent.setup();

      render(<TestExpiredErrorScreen />);
      const button = screen.getByRole('button');
      await act(() => user.click(button));

      expect(button).toHaveFocus();
    });
  });
});
