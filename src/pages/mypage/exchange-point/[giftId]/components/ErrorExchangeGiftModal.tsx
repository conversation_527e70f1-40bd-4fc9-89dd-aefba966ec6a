// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { Button } from '@/components/Button';
import { Modal } from '@/components/Modal';
import { routers } from '@/constants/routes';

interface ErrorExchangeGiftModalProps {
  isShowModal: boolean;
  submit: (url: string) => void;
}

const ErrorExchangeGiftModal: FC<ErrorExchangeGiftModalProps> = ({
  submit,
  isShowModal,
}) => {
  return (
    <Modal
      size="md"
      show={isShowModal}
      data-testid="error-exchange-gift-modal"
      innerClass="relative w-full rounded-modal bg-white shadow max-h-full overflow-y-auto"
      innerStyle={{ maxHeight: '80vh', paddingBlock: 0 }}
    >
      <Modal.Header>
        <h3 className="text-[#CA4B4B] text-lg text-center font-bold">
          エラーが発生しました
        </h3>
      </Modal.Header>
      <Modal.Body>
        <div className="mt-2.5 mb-2">
          <div className="text-sm">
            交換ギフト一覧から再度やり直してください。
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer className="space-y-4">
        <Button
          customSpan="!py-3 !px-5"
          fullSized
          size="lg"
          color="primary"
          onClick={() => submit(routers.exchangePoint)}
        >
          交換ギフト一覧からやり直す
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ErrorExchangeGiftModal;
