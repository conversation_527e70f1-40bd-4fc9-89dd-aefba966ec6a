// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
export interface ICheckinCalendarParams {
  yearMonth: string;
  companyId: string;
}

export interface ICalendarMemoPayload {
  company_id: string;
  date: string;
  memo: string;
}

export interface ICheckinCalendarStampPerDay {
  [date: string]: number;
}

export interface INewCheckinCalendarStampPerDay {
  [date: string]: {
    stamps: number;
    agencyName: string[];
    memo: string;
  };
}

export interface ICheckinCalendarResponse {
  registeredDate: string;
  stamp_count: number;
  data: INewCheckinCalendarStampPerDay;
}

export interface IActivity {
  date: string;
  stamps: number;
  agencyName?: [];
  memo?: string;
}

export interface IDateMemo {
  date: string;
  agencyName: string[] | [null];
  memo?: string;
}

export interface ICalendarData {
  registeredDate: string;
  activities: IActivity[];
  stamp_count: {
    date: string;
    count: number;
  }[];
}

export type TypeSwipeDirections = 'left' | 'right' | null;

export type TypeSwipeDirectionsWithoutNull = Exclude<TypeSwipeDirections, null>;
