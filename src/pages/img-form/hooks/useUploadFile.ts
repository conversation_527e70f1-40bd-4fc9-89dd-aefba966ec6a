// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import {
  ALL_FILE_SIZE_LIMIT,
  FILE_SIZE_LIMIT,
  IMAGE_UPLOAD_VALIDATE,
  IMAGE_FORM_MESSAGE,
  TEXT_ALERT_INVALID_ALL_FILE_SIZE,
  TEXT_ALERT_INVALID_COUNT_FILE,
  TEXT_ALERT_INVALID_FILE_EXTENSION,
  TEXT_ALERT_INVALID_FILE_SIZE,
  TEXT_ALERT_OTHER,
} from '@/constants/img-form';
import {
  IFileUpload,
  IGetImageFormResponse,
  IUploadImageFormResponse,
} from '@/types/img-form';
import { useRouter } from 'next/router';
import type { Liff } from '@line/liff';
import { useState } from 'react';
import { useFetch } from '@/hooks/use-fetch';
import imageFormApi from '@/services/internal/modules/img-form';
import { compressImage, getMimeType } from '@/utils/file';
import { IMAGE_TYPE_HEIC, IMAGE_TYPE_JPEG } from '@/constants/file';
import { routers } from '@/constants/routes';

type ErrorType = 'count' | 'fileSize' | 'allFileSize' | 'fileType' | 'other';

export const useUploadFile = (liff: Liff) => {
  const [uploadedImages, setUploadedImages] = useState<IFileUpload[]>([]);

  const [textError, setTextError] = useState<string | null>(null);
  const [isLoadingUploadForm, setIsLoadingUploadForm] =
    useState<boolean>(false);
  const [isLoadingChangeImage, setIsLoadingChangeImage] =
    useState<boolean>(false);
  const [uploadResponse, setUploadResponse] =
    useState<IUploadImageFormResponse>();

  const router = useRouter();
  const {
    query: { id: imgFormId },
  } = router;

  const {
    isFetching: isFetchingImageForm,
    data: { data: imageFormResponse, status: imageFormStatus },
  } = useFetch<IGetImageFormResponse>({
    fetcher: () => imageFormApi.getImageForm(imgFormId as string),
    handleError: () => {
      router.push(routers.notFound);
    },
    initialValue: {} as IGetImageFormResponse,
  });

  const handlePopupError = (type: ErrorType) => {
    switch (type) {
      case 'count':
        setTextError(
          TEXT_ALERT_INVALID_COUNT_FILE(
            Number(imageFormResponse?.image_count_max)
          )
        );
        break;
      case 'fileSize':
        setTextError(TEXT_ALERT_INVALID_FILE_SIZE);
        break;
      case 'fileType':
        setTextError(TEXT_ALERT_INVALID_FILE_EXTENSION);
        break;
      case 'allFileSize':
        setTextError(TEXT_ALERT_INVALID_ALL_FILE_SIZE);
        break;
      case 'other':
        setTextError(TEXT_ALERT_OTHER);
        break;
      default:
        break;
    }
  };

  const calculateTotalFileSize = (images: IFileUpload[]): number => {
    return images.reduce((total, img) => {
      return img.file ? total + img.file.size : total;
    }, 0);
  };

  const validateImage = (file: File | undefined) => {
    if (file) {
      const extension = file.name
        .substring(file.name.lastIndexOf('.'))
        .toLowerCase();
      const isValidExtension = IMAGE_UPLOAD_VALIDATE.includes(extension);
      const isValidSize = file.size >= FILE_SIZE_LIMIT;

      if (!isValidExtension) {
        handlePopupError('fileType');
        return false;
      }

      if (isValidSize) {
        handlePopupError('fileSize');
        return false;
      }
    }

    return file;
  };

  const handleCompressFile = async (file: File) => {
    const fileType = await getMimeType(file);
    let convertFile = file;
    if (fileType === IMAGE_TYPE_HEIC) {
      // because heic2any not support server side
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      const heic2any = require('heic2any');
      // get image as blob url
      const blobURL = URL.createObjectURL(file);
      // convert "fetch" the new blob url
      const blobRes = await fetch(blobURL);
      // convert response to blob
      const blob = await blobRes.blob();
      // convert to JPEG - response is blob
      const conversionResult = await heic2any({
        blob,
        quality: 1,
        toType: IMAGE_TYPE_JPEG,
      });

      conversionResult.name = file.name
        .toLowerCase()
        .replace(/\.hei(c|f)$/, '.jpeg');
      conversionResult.lastModified = new Date().getTime();

      convertFile = conversionResult;
    }
    const compressedFileBlob = await compressImage(convertFile);
    if (typeof compressedFileBlob === 'undefined') throw new Error();
    const compressedFile = new File(
      [compressedFileBlob],
      compressedFileBlob.name,
      {
        type: compressedFileBlob.type,
      }
    );
    return compressedFile;
  };

  const handleUploadImages = async (
    event: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    const file = validateImage(event.target.files?.[0]);

    if (file) {
      try {
        setIsLoadingChangeImage(true);

        const reader = new FileReader();
        reader.onloadend = () => {
          setUploadedImages(prevImages => {
            const updatedImages = [...prevImages];
            updatedImages[index] = {
              file: file,
              image: URL.createObjectURL(file),
            };
            const totalSizeMB =
              calculateTotalFileSize(updatedImages) / (1024 * 1024);
            if (totalSizeMB >= ALL_FILE_SIZE_LIMIT) {
              handlePopupError('allFileSize');
              return prevImages;
            }

            return updatedImages;
          });
        };
        reader.readAsDataURL(file);
      } finally {
        setIsLoadingChangeImage(false);
      }
    }
  };

  const handleDeleteImage = (index: number) => {
    setUploadedImages(prevImages => {
      const updatedImages = [...prevImages];
      updatedImages.splice(index, 1);
      return updatedImages;
    });
  };

  const checkMaximumImageUpload =
    uploadedImages.length < Number(imageFormResponse?.image_count_max) || 0;

  const isDisabledButtonSubmit =
    uploadedImages.length === 0 ||
    uploadedImages.some(item => item.image === null);

  const sendMessageHandler = async () => {
    await liff.sendMessages([
      {
        type: 'text',
        text: IMAGE_FORM_MESSAGE,
      },
    ]);
  };

  const onSubmit = async () => {
    try {
      setIsLoadingUploadForm(true);
      const formData = new FormData();

      const newImgUpload = await Promise.all(
        uploadedImages.map(async item => {
          return handleCompressFile(item.file as File);
        })
      );

      newImgUpload.map(item => formData.append('images', item as File));
      const res = await imageFormApi.uploadImageForm(
        imgFormId as string,
        formData
      );

      const lineContext = liff.getContext();
      lineContext?.type === 'utou' && (await sendMessageHandler());
      setUploadResponse(res);
    } catch {
      handlePopupError('other');
    } finally {
      setIsLoadingUploadForm(false);
    }
  };

  return {
    uploadedImages,
    handleUploadImages,
    handleDeleteImage,
    checkMaximumImageUpload,
    imageFormResponse,
    isDisabledButtonSubmit,
    onSubmit,
    textError,
    setTextError,
    isLoadingUploadForm,
    isFetchingImageForm,
    uploadResponse,
    imageFormStatus,
    isLoadingChangeImage,
  };
};
