// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { WEBSITE_NAME } from '@/constants/common';

const useId: () => string | undefined =
  (React as any)['useId'.toString()] || (() => undefined);

export function useReactId() {
  const id = useId();
  return id
    ? `${WEBSITE_NAME.toLocaleLowerCase()}-${id.replace(/:/g, '')}`
    : '';
}
