// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import CustomUiDate from '@/pages/checkin-calendar/components/CustomUiDate';
import GuideCalendar from '@/pages/checkin-calendar/components/GuideCalendar';
import { useCheckinCalendar } from '@/pages/checkin-calendar/hooks/useCheckinCalendar';
import { IAppConfig } from '@/types/app';
import {
  Datepicker,
  MbscDatepickerPageChangeEvent,
  localeJa,
} from '@/lib/mobiscroll/react/esm5/mobiscroll.react.min';
import dayjs from 'dayjs';
import ModalComfirmMemo from './components/ModalComfirmMemo';
import ModalSwipeDate from './components/ModalSwipeDate';
import RankStatus from './components/RankStatus';
import SelectYearMonth from './components/SelectYearMonth';
import useCheckinMemo from './hooks/useCheckinMemo';
import { useEffect } from 'react';
import { useFetch } from '@/hooks/use-fetch';
import { URL_SOURCE } from '@/constants/checkin';
import accessLogApi from '@/services/internal/modules/access-log';
import { ISaveLogPayload } from '@/types/access-log';

export interface IRegisterConfirmScreenProps {
  appConfig: IAppConfig;
}

const CheckinCalendar: React.FC<IRegisterConfirmScreenProps> = () => {
  const {
    queryDate,
    swipeDirection,
    currentMonthData,
    isResetMonth,
    setMonthCall,
    setQueryDateInitMonth,
    setIsResetMonth,
    handleSelectPicker,
    onMonthChange,
    createInitMonth,
    handleMonth,
    setCurrentMonthData,
    defaultData,
    queryDateInitMonth,
    setIntiMonthCall,
    monthCall,
    setRecall,
    setSpecifyMonthReCall,
  } = useCheckinCalendar('C0003');

  const {
    dates,
    activeDateMemo,
    isEditable,
    swiperRef,
    memoValue,
    handleClickDate,
    handleSlideChange,
    onCloseModal,
    onEditable,
    onCancelEdit,
    onChangeMemoValue,
    isOpenModal,
    isOpenModalConfirm,
    setIsOpenModal,
    setIsOpenModalConfirm,
    setActiveDateMemo,
  } = useCheckinMemo({
    currentMonthData,
    monthCall,
    setMonthCall,
    setRecall,
    setSpecifyMonthReCall,
  });

  const { fetch: calendarLogHandler } = useFetch({
    fetcher: accessLogApi.saveLog,
    dependencies: false,
  });

  const handleCalendarLog = async (data?: ISaveLogPayload) => {
    await calendarLogHandler(data);
  };

  useEffect(() => {
    document.body.classList.add('bg-[#FFFADE]');

    return () => {
      document.body.classList.remove('bg-[#FFFADE]');
    };
  }, []);

  useEffect(() => {
    const hasCalledAPI = sessionStorage.getItem('apiCalled');
    const urlSourceData = sessionStorage.getItem(URL_SOURCE);

    if (!hasCalledAPI) {
      handleCalendarLog({
        url_source: urlSourceData || '',
        screen_type: 'calendar',
        screen_path: '/checkin-calendar',
      });

      sessionStorage.setItem('apiCalled', 'true');
    }

    return () => {
      sessionStorage.removeItem(URL_SOURCE);
      sessionStorage.removeItem('apiCalled');
    };
  }, []);

  return (
    <>
      <div className="mx-auto">
        <div className="w-full md:max-w-md mx-auto min-h-screen pb-[78px]">
          <div className="p-[18px] bg-calendar-yellow relative">
            <SelectYearMonth
              id="calendar"
              registeredDate={currentMonthData.registeredDate}
              onSelectPicker={handleSelectPicker}
              swipeDirection={swipeDirection}
              onClickSubmit={(year: number, month: number) => {
                const formattedDate = dayjs(`${year}/${month}`).format(
                  'YYYY/MM'
                );
                handleMonth(year, month);
                setCurrentMonthData(defaultData);
                const newArrMonth = createInitMonth(formattedDate);
                setQueryDateInitMonth(formattedDate);
                setMonthCall(newArrMonth);
                setIntiMonthCall(newArrMonth);
                setIsResetMonth(true);
              }}
            />
          </div>

          <RankStatus
            stampCount={
              currentMonthData.stamp_count.find(
                item => item?.date === queryDate
              )?.count || 0
            }
            className="mt-[35px] mb-4 px-4"
          />

          <Datepicker
            theme="ios"
            controls={['calendar']}
            display="inline"
            touchUi={true}
            renderDay={args => (
              <CustomUiDate args={args} currentMonthData={currentMonthData} />
            )}
            className="mobiscroll-datepicker"
            themeVariant="light"
            locale={localeJa}
            onPageChange={(args: MbscDatepickerPageChangeEvent) =>
              !isResetMonth && onMonthChange(args)
            }
            min={
              currentMonthData.registeredDate &&
              dayjs(currentMonthData.registeredDate)
                .startOf('month')
                .format('YYYY/MM/DD')
            }
            max={dayjs().endOf('month').add(1, 'year').format('YYYY/MM/DD')}
            value={dayjs(queryDateInitMonth).format('YYYY-MM-DD')}
            dateFormat="YYYY-MM-DD"
            returnFormat="iso8601"
            onCellClick={async args => {
              await setIsOpenModal(true);
              await setActiveDateMemo(dayjs(args.date).format('YYYY/MM/DD'));
              handleClickDate(args);
            }}
          />

          <GuideCalendar />
        </div>
      </div>

      <ModalSwipeDate
        isOpenModal={isOpenModal}
        setIsOpenModal={setIsOpenModal}
        onCloseModal={onCloseModal}
        isEditable={isEditable}
        onEditable={onEditable}
        activeDateMemo={activeDateMemo}
        handleSlideChange={handleSlideChange}
        swiperRef={swiperRef}
        dates={dates}
        setIsOpenModalConfirm={setIsOpenModalConfirm}
        memoValue={memoValue}
        onChangeMemoValue={onChangeMemoValue}
      />

      <ModalComfirmMemo
        isOpenModalConfirm={isOpenModalConfirm}
        setIsOpenModalConfirm={setIsOpenModalConfirm}
        onCancelEdit={onCancelEdit}
      />
    </>
  );
};

export default CheckinCalendar;
