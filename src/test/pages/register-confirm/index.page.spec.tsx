// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { act, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import type { Liff } from '@line/liff';
import { IAppConfig } from '@/types/app';
import RegisterConfirm from '@/pages/register-confirm/index.page';
import { LiffContextMockProviders, MockLiffProvidersProps } from '@/test/mocks';

jest.mock('next/router', () => require('next-router-mock'));

const TestRegisterConfirmScreen = (): JSX.Element => {
  const liffContextValue: MockLiffProvidersProps = {
    liff: {
      getContext: () => ({}),
      getFriendship: () => ({ friendFlag: true }),
    } as unknown as Liff,
  };

  const appConfig: IAppConfig = {
    liffId: 'lorem-123',
    companyId: 'ipsum-456',
  };

  return (
    <LiffContextMockProviders contextValue={liffContextValue}>
      <RegisterConfirm appConfig={appConfig} />
    </LiffContextMockProviders>
  );
};

describe('pages/register-confirm/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<TestRegisterConfirmScreen />);

      await waitFor(() => {
        expect(screen.getByRole('img')).toBeInTheDocument();
      });
    });
  });

  describe('User interactions', () => {
    it('should trigger backToRegisterScreen button', async () => {
      const user = userEvent.setup();

      render(<TestRegisterConfirmScreen />);
      const buttonRedirect = screen.getAllByRole('button')[1];
      await act(() => user.click(buttonRedirect));

      expect(buttonRedirect).toHaveFocus();
    });
  });
});
