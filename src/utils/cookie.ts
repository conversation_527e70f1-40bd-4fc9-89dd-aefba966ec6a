// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import type { NextPageContext } from 'next';
import { parseCookies, setCookie, destroyCookie } from 'nookies';
import { windowExists } from '.';

interface IOptions {
  domain?: string;
  encode?: ((val: string) => string) | undefined;
  expires?: Date;
  httpOnly?: boolean;
  maxAge?: number;
  path?: string;
  sameSite?: boolean;
  secure?: boolean;
}

const defaultOption: IOptions = {
  path: '/',
  secure: true,
  httpOnly: false,
};

const getHostName = (context?: NextPageContext | null) => {
  const isClient = windowExists();
  const hostName = isClient
    ? window.location.hostname
    : context?.req?.headers.host?.split(':')[0];

  return hostName;
};

export const setCookieApp = (
  context: NextPageContext | null,
  name: string,
  value: string,
  options: IOptions = {}
) => {
  return setCookie(context, name, value, {
    domain: getHostName(context),
    ...defaultOption,
    ...options,
  });
};

export const parseCookiesApp = (
  context?: NextPageContext | null,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  options?: any
): {
  [key: string]: string;
} => {
  return parseCookies(context, options);
};

export const destroyCookieApp = (
  context: NextPageContext | null,
  name: string,
  options: IOptions = {}
) => {
  const optionsOverride = {
    path: defaultOption.path,
    domain: getHostName(context),
    ...options,
  };

  // Destroy cookie with normal domain and not options (ex: subdomain.example.com)
  destroyCookie(context, name);
  // Destroy cookie with special domain and options (ex: .subdomain.example.com)
  destroyCookie(context, name, optionsOverride);
};
