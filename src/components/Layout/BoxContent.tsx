// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';

interface BoxContentProps {
  label: string;
  value: string;
}

export const BoxContent: FC<BoxContentProps> = ({ label, value }) => {
  return (
    <div>
      <div className="text-theme-sub text-sm font-normal mb-2">{label}</div>
      <div className="text-theme-main text-sm font-normal mb-4">{value}</div>
      <hr className="h-px bg-theme-primary/10 border-0"></hr>
    </div>
  );
};
