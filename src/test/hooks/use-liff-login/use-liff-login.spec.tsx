// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { default as liff } from '@line/liff';
import { act, renderHook } from '@testing-library/react';
import { useLiffLogin } from '@/hooks/use-liff-login';

jest.mock('@line/liff', () => ({
  __esModule: true,
  default: {
    init: jest.fn(),
  },
}));

describe('hooks/use-liff-login/use-liff-login.spec.tsx', () => {
  describe('A11y', () => {
    it('should return false as initial login state', () => {
      const { result } = renderHook(() => useLiffLogin(undefined));

      expect(result.current[0]).toBe(false);
    });
  });

  describe('User interactions', () => {
    describe('When liff is undefined', () => {
      const render = () => renderHook(() => useLiffLogin(undefined));

      it('should return false when call login', () => {
        const { result } = render();
        act(() => {
          result.current[1].login();
        });

        expect(result.current[0]).toBe(false);
      });

      it('should return false when call logout', () => {
        const { result } = render();
        act(() => {
          result.current[1].logout();
        });

        expect(result.current[0]).toBe(false);
      });
    });

    describe('When liff is defined', () => {
      const render = () => renderHook(() => useLiffLogin(liff));

      it('should return true when call login', () => {
        liff.isLoggedIn = jest.fn().mockReturnValue(true);

        const { result } = render();
        act(() => {
          result.current[1].login();
        });

        expect(result.current[0]).toBe(true);
      });

      it('should return false when logout call', () => {
        liff.isLoggedIn = jest.fn().mockReturnValue(false);

        const { result } = render();
        act(() => {
          result.current[1].logout();
        });

        expect(result.current[0]).toBe(false);
      });
    });
  });
});
