// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import Loading from '@/components/Spinner/Loading';
import { UNKNOWN } from '@/constants/define';
import { routers } from '@/constants/routes';
import { useLiff } from '@/contexts/liff';
import { useRedirect } from '@/hooks/use-redirect';
import redirectApi from '@/services/internal/modules/redirect';
import { Liff } from '@line/liff';
import router from 'next/router';

type FormDataType = {
  external_id: string;
  email: string;
};

const RedirectKyosaiScreen = () => {
  const { userInfo } = useLiff();
  const liffId = process.env.LIFF_ID || UNKNOWN;

  const handle = async () => {
    const liffInitial =
      window.liff ?? ((await import('@line/liff')).default as Liff);
    await liffInitial.init({ liffId: liffId });
    return { email: userInfo.email, external_id: userInfo.peer_conne_id };
  };

  const postRedirectWithJSON = (url: string, data: FormDataType) => {
    const formData = new FormData();

    formData.append('external_id', data.external_id);
    formData.append('email', data.email);

    const form = document.createElement('form');
    form.method = 'POST';
    form.action = url;
    form.style.display = 'none';

    for (const [key, value] of formData.entries()) {
      const input = document.createElement('input');
      input.type = 'hidden';
      input.name = key;
      input.value = value instanceof File ? value.name : value;
      form.appendChild(input);
    }

    document.body.appendChild(form);
    form.submit();
  };

  useRedirect(async ({ query: { kyosaiId } }) => {
    const data: FormDataType = await handle();

    try {
      const { url } = await redirectApi.verifyRedirectKyosai({
        kyosaiId,
      });
      postRedirectWithJSON(url, {
        external_id: data.external_id,
        email: data.email,
      });
    } catch (error) {
      router.push(routers.notFound);
    }
  });

  return <Loading />;
};

export default RedirectKyosaiScreen;
