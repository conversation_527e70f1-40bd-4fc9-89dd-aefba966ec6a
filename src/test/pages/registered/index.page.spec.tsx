// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { act, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import type { Liff } from '@line/liff';
import RegisteredScreen from '@/pages/registered/index.page';
import { LiffContextMockProviders } from '@/test/mocks';

jest.mock('next/router', () => require('next-router-mock'));

const TestRegisteredScreen = (): JSX.Element => {
  const liffContextValue = {
    liff: { openWindow: () => ({}) } as unknown as Liff,
  };

  return (
    <LiffContextMockProviders contextValue={liffContextValue}>
      <RegisteredScreen />
    </LiffContextMockProviders>
  );
};

describe('pages/registered/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<TestRegisteredScreen />);

      expect(screen.getAllByRole('img').length).toEqual(3);
    });
  });

  describe('User interactions', () => {
    it('should trigger `onClick` button', async () => {
      const user = userEvent.setup();

      render(<TestRegisteredScreen />);
      const button = screen.getByRole('button');
      await act(() => user.click(button));

      expect(button).toHaveFocus();
    });
  });
});
