// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { BoxContent } from '@/components/BoxContent';
import { Button } from '@/components/Button';
import { SvgIcon } from '@/components/SvgIcon';
import { useLiff } from '@/contexts/liff';
import { IGetImageFormData } from '@/types/img-form';
import { redirectToLineChatRoomOAWithCheckUtou } from '@/utils/redirect';
import { transformText2 } from '@/utils/text';
import React from 'react';
import { styles } from './styles';

interface ImageUploadOutOfPeriodProps {
  submmittedRes: IGetImageFormData;
}

const ImageUploadOutOfPeriod: React.FC<ImageUploadOutOfPeriodProps> = ({
  submmittedRes,
}) => {
  const { liff } = useLiff();
  return (
    <>
      <div className="container mx-auto">
        <BoxContent>
          <BoxContent.Body className="pt-[160px]">
            <div className="text-base space-y-4">
              <div className="flex justify-center">
                <SvgIcon name={'OutOfPeriodLotteryIcon'} />
              </div>
              <div className="mt-4">
                <pre
                  dangerouslySetInnerHTML={{
                    __html: transformText2(
                      submmittedRes?.text.text_out_of_period as string,
                      styles
                    ),
                  }}
                  className={styles.textOutOfPeriod}
                  style={{ display: 'flex', flexDirection: 'column' }}
                />
              </div>
            </div>
          </BoxContent.Body>
          <BoxContent.Footer>
            <Button
              fullSized
              type="submit"
              size="xl"
              color="primary"
              onClick={() => redirectToLineChatRoomOAWithCheckUtou(liff)}
            >
              トーク画面に戻る
            </Button>
          </BoxContent.Footer>
        </BoxContent>
      </div>
    </>
  );
};

export default ImageUploadOutOfPeriod;
