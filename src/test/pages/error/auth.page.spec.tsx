// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { act, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Message } from '@/constants/message';
import { LiffContextMockProviders } from '@/test/mocks';
import AuthErrorScreen from '@/pages/error/auth.page';

jest.mock('next/router', () => require('next-router-mock'));

const TestAuthErrorScreen = (): JSX.Element => {
  const liffContextValue = {
    error: { message: Message.error.chatMessagePermision },
  };

  return (
    <LiffContextMockProviders contextValue={liffContextValue}>
      <AuthErrorScreen />
    </LiffContextMockProviders>
  );
};

describe('pages/error/auth.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<TestAuthErrorScreen />);

      expect(screen.getByRole('img')).toBeInTheDocument();
    });
  });

  describe('User interactions', () => {
    it('should trigger `onClick` button', async () => {
      const user = userEvent.setup();

      render(<TestAuthErrorScreen />);

      const openButton = screen.getByRole('button');

      const href = 'https://example.com';
      jest
        .spyOn(window, 'location', 'get')
        .mockImplementation(() => ({ href } as Location));

      await act(() => user.click(openButton));

      expect(window.location.href).toEqual(href);
    });
  });
});
