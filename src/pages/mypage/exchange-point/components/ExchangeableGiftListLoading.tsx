import { range } from 'lodash';

// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
export const ExchangeableGiftListLoading = () => {
  return (
    <div className="animate-pulse">
      <div className="space-y-4">
        {[...range(2)].map((indexGroup: number) => (
          <div key={indexGroup} className="space-y-2">
            <h3 className="w-1/2 h-5 bg-slate-200 rounded" />
            <div className="space-y-2">
              {[...range(2)].map((indexGift: number) => (
                <div
                  key={indexGift}
                  className="flex border border-gray-10 px-3 py-2 rounded gap-3"
                >
                  <div className="w-20 h-20 bg-slate-200 rounded" />
                  <div className="flex flex-1 flex-col justify-between gap-2">
                    <div className="space-y-2">
                      <div className="w-3/4 h-2 bg-slate-200 rounded" />
                      <div className="min-h-[40px] space-y-1">
                        <div className="w-full h-2 bg-slate-200 rounded" />
                        <div className="w-1/2 h-2 bg-slate-200 rounded" />
                      </div>
                    </div>
                    <div className="flex items-center justify-end">
                      <div className="w-[136px] h-6 bg-slate-200 rounded" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
