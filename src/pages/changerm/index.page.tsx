// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { RichMenuContent } from '@/components/Richmenu';
import { OptionType, Select2 } from '@/components/Select2';
import Loading from '@/components/Spinner/Loading';
import { useLiff } from '@/contexts/liff';
import { useFetch } from '@/hooks/use-fetch';
import authApi from '@/services/internal/modules/auth';
import companyApi from '@/services/internal/modules/company';
import richMenuApi from '@/services/internal/modules/richmenu';
import { ICompany, IGroupedCompanies } from '@/types/company';
import { IRichMenu } from '@/types/richmenu';
import { redirectToLineChatRoomOA } from '@/utils/redirect';
import { FC, useEffect, useState } from 'react';

const ChangeRichMenuScreen: FC = () => {
  const { isLoggedIn, liff } = useLiff();
  const [isLoadingLinkRichMenu, setIsLoadingLinkRichMenu] = useState(false);
  const [richMenuIdSelected, setRichMenuIdSelected] = useState<string>('');
  const [selectedGroup, setSelectedGroup] = useState<OptionType>();
  const [selectedRichMenuContent, setSelectedRichMenuContent] =
    useState<ICompany[]>();

  const onSelectRichMenu = async ({ company_id }: IRichMenu) => {
    try {
      if (isLoadingLinkRichMenu) return;
      setRichMenuIdSelected(company_id);
      setIsLoadingLinkRichMenu(true);
      await richMenuApi.linkRichMenuWithCompanyId({ companyId: company_id });
      redirectToLineChatRoomOA(liff);
    } finally {
      setIsLoadingLinkRichMenu(false);
    }
  };
  const { data: myPartnerGroup } = useFetch<OptionType[]>({
    fetcher: authApi.myPartnerGroup,
    initialValue: [],
  });

  const { data: groupedCompanies } = useFetch<IGroupedCompanies>({
    fetcher: companyApi.getCompanyList,
    initialValue: [] as unknown as IGroupedCompanies,
  });

  const onChangeGroup = (option?: OptionType) => {
    setSelectedGroup(option);
    option && setSelectedRichMenuContent(groupedCompanies[option.value]);
  };

  const filteredOptions = myPartnerGroup.filter(option => {
    if (!selectedGroup) return true;
    return option.value !== selectedGroup.value;
  });

  useEffect(() => {
    if (myPartnerGroup.length) {
      setSelectedGroup(myPartnerGroup[0]);
      const partnerGroupKey = myPartnerGroup[0].value;
      setSelectedRichMenuContent(groupedCompanies[partnerGroupKey]);
    }
  }, [myPartnerGroup, groupedCompanies]);

  return (
    <>
      {!isLoggedIn ? (
        <Loading />
      ) : (
        <div className="container mx-auto">
          <div className="w-full md:max-w-md mx-auto pt-6 pb-10">
            <div className="!mb-auto">
              {myPartnerGroup.length > 1 && (
                <>
                  <h5 className="text-xs font-normal mb-2">
                    下のプルダウンから、表示する企業グループを変更できます。
                  </h5>
                  <Select2
                    options={filteredOptions}
                    placeholder="Placeholder"
                    onChange={(value: OptionType | null) => {
                      if (value) {
                        onChangeGroup(value);
                      }
                    }}
                    defaultValue={selectedGroup}
                    className="mb-8"
                  />
                </>
              )}
              {selectedRichMenuContent && (
                <RichMenuContent
                  richmenus={selectedRichMenuContent}
                  loading={isLoadingLinkRichMenu}
                  richMenuIdSelected={richMenuIdSelected}
                  onSelectRichMenu={onSelectRichMenu}
                />
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ChangeRichMenuScreen;
