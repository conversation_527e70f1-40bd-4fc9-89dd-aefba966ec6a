// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { render, screen } from '@testing-library/react';
import { BoxContent } from '@/components/Layout/BoxContent';

describe('components/Layout/BoxContent.spec.tsx', () => {
  describe('A11y', () => {
    it('should render box content', async () => {
      render(
        <BoxContent label="Box content label" value="Box content value" />
      );

      expect(screen.getByText('Box content label')).toBeInTheDocument();
    });
  });
});
