// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { NextRequest, NextResponse } from 'next/server';
import logger from './utils/logger';
import { routers } from './constants/routes';
import { randomUuid } from './utils/random';

function parseCookies(cookieHeader: string | null): Record<string, string> {
  const cookies: Record<string, string> = {};
  if (!cookieHeader) return cookies;

  cookieHeader.split(';').forEach(cookie => {
    const [name, value] = cookie.split('=').map(c => c.trim());
    cookies[name] = decodeURIComponent(value);
  });

  return cookies;
}

const getGroupInfos = async (url: string, accessToken: string) => {
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${accessToken}`,
    },
  });
  const data = await response.json();
  return data;
};

export async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;
  const excludePaths = ['/_next/static', '/_next/webpack-hmr', '/favicon.ico'];
  const cookies = parseCookies(req.headers.get('cookie'));
  let statusCode = 200;
  const validRoutes = Object.values({ ...routers }).filter(
    route => route !== routers.notFound
  );
  const convertedRoute = validRoutes.map(route =>
    route.replace(/\[.+?\]/g, '').replace(/(?!^\/$)\/$/, '')
  );

  try {
    if (excludePaths.some(path => pathname.startsWith(path))) {
      return NextResponse.next();
    }

    if (!convertedRoute.some(path => pathname === path)) {
      statusCode = 404;
    }

    const logInfo = JSON.stringify({
      id: randomUuid(),
      time: new Date().toISOString(),
      ip: req.ip,
      method: req.method,
      referer_url: req.headers.get('referer'),
      access_url: req.url,
      user_agent: req.headers.get('user-agent'),
      peer_conne_id: cookies.peer_conne_id,
      status_code: statusCode,
    });
    logger.info(logInfo);

    const url = req.nextUrl.clone();
    const companyId = url.searchParams.get('company_id');

    //Redirect to join partner group page with below conditions
    if (companyId && cookies?.accessToken) {
      const data = await getGroupInfos(
        `${process.env.INTERNAL_BASE_URL}/group/registrable/${companyId}`,
        cookies?.accessToken
      );

      if (data.registrable) {
        const newURL = new URL(
          `${routers.joinPartnerGroup}?company_id=${companyId}&prev=${url.pathname}`,
          req.url
        );

        return NextResponse.rewrite(newURL);
      } else {
        return NextResponse.next();
      }
    }

    return NextResponse.next();
  } catch (err) {
    logger.error(err);
  }
}

export const config = {
  matcher: '/:path*',
};
