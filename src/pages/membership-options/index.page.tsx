// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React from 'react';
import { BoxContent } from '@/components/BoxContent';
import { Button } from '@/components/Button';
import { Logo } from '@/components/Logo';
import { routers } from '@/constants/routes';
import { useRouter } from 'next/router';
import Head from 'next/head';

const MemberOptionsScreen = () => {
  const router = useRouter();

  return (
    <>
      <Head>
        <title>ピアコネ</title>
      </Head>
      <div className="container mx-auto">
        <BoxContent>
          <BoxContent.Body className="pt-[24px]">
            <Logo box className="mb-6 !h-auto" />
            <div className="text-center mt-[120px]">
              <h3 className="text-[15px] text-left">
                ピアコネのご利用には会員登録が必要です。すでにアカウントをお持ちの方はログインしてください。
              </h3>
            </div>

            <div className="space-y-4 mt-[54px]">
              <Button
                customSpan="!px-5"
                fullSized
                size="xl"
                color="primary"
                onClick={() => router.push(routers.login)}
              >
                ログイン
              </Button>
              <Button
                customSpan="!px-5"
                outline
                fullSized
                size="xl"
                color="primary"
                onClick={() => router.push(routers.register)}
              >
                新規会員登録
              </Button>
            </div>
          </BoxContent.Body>
        </BoxContent>
      </div>
    </>
  );
};

export default MemberOptionsScreen;
