// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import { IModalProps, Modal } from './Modal';
import { SvgIcon } from '../SvgIcon';

interface INoWifiModal extends IModalProps {
  isShowModal: boolean;
}

const NoWifiModal: FC<INoWifiModal> = ({ isShowModal, ...props }) => {
  return (
    <Modal size="md" show={isShowModal} {...props}>
      <Modal.Header>
        <h3 className="text-theme-primary text-lg text-center font-bold">
          ネットワークエラー
        </h3>
      </Modal.Header>
      <Modal.Body>
        <div className="w-full mb-[50px]">
          <div className="flex flex-col items-center gap-9">
            <div className="text-sm text-center">
              ネットワークに問題が発生しました。ネット
              <br />
              ワーク環境を確認して、再度アクセスしてく
              <br />
              ださい。
            </div>
            <SvgIcon name="NoWifi" />
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default NoWifiModal;
