// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import {
  IExchangeableGiftDetailResponse,
  IExchangeableGiftListResponse,
  IExchangedGiftDetailResponse,
} from '@/types/exchange-points';

export const EXCHANGED_GIFT_LIST: IExchangedGiftDetailResponse[] = [
  {
    exchange_id: 'EX1686812131nqtm',
    gift: {
      available_period: '5ヶ月後の月末',
      gift_id_in_pc: 'CG1686108998hgyo',
      gift_url:
        'https://g4b.giftee.biz/gift_cards/7e85bc2c-db97-42d5-b11e-966a3bae8684',
      gift_name: 'Update gift_name 111 - test exchange point feature',
      gift_company_name: 'gift_company_name 111',
      gift_image:
        'https://wellnessstoragedev.blob.core.windows.net/contents/config-gift/CG1686108998hgyo/b451b39b-6523-4e33-9bfa-439e938c812c.jpg',
    },
    point_amount: 100,
    exchanged_at: '2023-06-15T06:55:31.890Z',
    gift_type: 'fixed',
  },
  {
    exchange_id: 'EX1686812330qzcl',
    exchanged_at: '2023-06-15T06:58:50.011Z',
    gift: {
      available_period: '5ヶ月後の月末',
      gift_id_in_pc: 'CG1686108998hgyh',
      gift_url:
        'https://g4b.giftee.biz/gift_cards/5f0a5609-5c02-42ba-adef-1f88cde8608d',
      gift_name: 'Update gift_name 111 - test exchange point feature',
      gift_company_name: 'gift_company_name 111',
      gift_image:
        'https://wellnessstoragedev.blob.core.windows.net/contents/config-gift/CG1686108998hgyo/b451b39b-6523-4e33-9bfa-439e938c812c.jpg',
    },
    gift_type: 'fixed',
    point_amount: 100,
  },
  {
    gift: {
      gift_url:
        'https://g4b.giftee.biz/gift_cards/cf70b8d8-0c81-47e6-8114-c50accda18c2',
      available_period: '5ヶ月後の月末',
      gift_id_in_pc: 'CG1686109200nmvc',
      gift_name: 'gift_name 112 fixed - test exchange point feature',
      gift_company_name: 'gift_company_name 112',
      gift_image:
        'https://wellnessstoragedev.blob.core.windows.net/contents/config-gift/CG1686109200nmvc/e6d99411-267f-43a0-a626-366eaee54420.jpg',
    },
    gift_type: 'fixed',
    point_amount: 150,
    exchanged_at: '2023-06-15T07:02:55.823Z',
    exchange_id: 'EX1686812575nrdn',
  },
];

export const EXCHANGEABLE_GIFT_LIST: IExchangeableGiftListResponse[] = [
  {
    gift_required_amount: 50,
    gift_list: [
      {
        gift_id_in_pc: 'CG1725851962wbbz',
        gift_name: 'Sale 9/9 (fixed)',
        gift_company_name: 'C0003',
        gift_image:
          'https://wellnessstoragedev.blob.core.windows.net/contents/config-gift/CG1725851962wbbz/b9611e42-872c-4921-9d8c-e64c1b60b877.jpg',
      },
    ],
  },
  {
    gift_required_amount: 100,
    gift_list: [
      {
        gift_id_in_pc: 'CG1725852006rkrr',
        gift_name: 'Sale 10/10 (box)',
        gift_company_name: 'C0003',
        gift_image:
          'https://wellnessstoragedev.blob.core.windows.net/contents/config-gift/CG1725852006rkrr/8c4ede02-8dc1-49f2-a31f-5455ed6580bc.png',
      },
    ],
  },
  {
    gift_required_amount: 150,
    gift_list: [
      {
        gift_id_in_pc: 'CG1725852397rtsb',
        gift_name: 'Gift for Wellness hehee (choosable)',
        gift_company_name: 'C0003',
        gift_image:
          'https://wellnessstoragedev.blob.core.windows.net/contents/config-gift/CG1725852397rtsb/9e32f8ad-d683-46e4-8242-46056a93bbd5.jpg',
      },
    ],
  },
];

export const EXCHANGEABLE_GIFT_DETAIL: IExchangeableGiftDetailResponse = {
  gift_id_in_pc: 'CG1725851962wbbz',
  gift_type: 'fixed',
  gift_name: 'Sale 9/9 (fixed)',
  gift_company_name: 'C0003',
  gift_image:
    'https://wellnessstoragedev.blob.core.windows.net/contents/config-gift/CG1725851962wbbz/b9611e42-872c-4921-9d8c-e64c1b60b877.jpg',
  point_amount: 100,
  exchange_points: false,
  gift_about:
    'コンビの公式オンラインショップ「コンビ公式ブランドストア」でお買い物の際、 3,000円（税込）までご利用いただけるギフトチケットです。\nコンビ公式ブランドストアでは、ベビーカーやチャイルドシート、ベビーラック＆ベビーチェア、哺乳びんなど、ご出産準備に欠かせないアイテムをはじめ、ベビー食器、ベビーマグなど赤ちゃんとの生活に役立つベビーグッズなどをご用意しています。',
  gift_how_to_use:
    '+ コンビ公式ブランドストアにアクセスしてください。\n+ お好きな商品をカートに入れてください。\n+ 購入手続きに進み、「ご注文の確認」画面にある「クーポンの利用」欄にギフトコード（クーポンコード）を入力してください。\n+ 内容を確認し、「注文を確定する」ボタンを押してください。\n- コンビ公式ブランドストアでのみご利用いただけます。\n- お買上金額が5,500円（税込）未満の場合は、送料をご負担いただきます。\n　ギフトチケットによる送料のお支払いはできません。\n【コンビ公式ブランドストア】\n[[https://www.combi.co.jp/store/>https://www.combi.co.jp/store/]]',
  gift_notes:
    '【ギフトチケットご利用上の注意】\n- ギフトチケットは1枚につき、1回限りのご利用となります。\n- 1回の決済でご利用いただけるギフトチケットは1枚までとなります。\n- チケット額面を超えたご注文の際は、超えた金額をお支払いください。\n- おつりは出ません。また、ギフトチケットの金額に満たない商品購入の場合、ギフトチケットに残高は残りません。\n- 商品代金に対してのみご利用いただけます。（送料・手数料へのご利用はできません。）\n- ギフトチケットには、有効期限があります。チケット画面にて期限をお確かめの上、有効期限内にご利用ください。\n- ギフトチケットの返品・交換・換金はできません。\n- ギフトチケットの再発行はできません。また、ギフトチケットご利用で商品購入後にキャンセルした場合、ギフトチケットはご利用いただけなくなりますのでご注意ください。\n- コンビ公式ブランドストアのクーポンは併用できません。\n- チケットショップやインターネットオークション、フリマアプリなどでの販売を含む、ギフトチケットの営利目的での転売を禁じます。',
  gift_notes_confirmation:
    '- ギフトに交換すると、ピアコネポイントを元に戻すことはできません。\n- コンビ公式ブランドストアギフトチケットの利用には期限がございます。画面に表示される期限までに、コンビ公式ブランドストアにてご利用ください。\n- コンビ公式ブランドストアギフトチケット交換後の変更・キャンセルはできません。\n- コンビ公式ブランドストアギフトチケットの追加チャージはできません。',
};
