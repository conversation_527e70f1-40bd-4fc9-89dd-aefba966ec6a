// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import type { ComponentProps, FC } from 'react';
import { SvgListIcons } from './icons';
import type { ISvgListICons } from './icons';

export interface ISvgIconProps extends ComponentProps<'svg'> {
  name: keyof ISvgListICons;
}

export const SvgIcon: FC<ISvgIconProps> = ({ name, ...props }) => {
  const Component = SvgListIcons[name];

  return <Component {...props} />;
};
