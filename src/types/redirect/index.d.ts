import { NOT_FOUND } from '@/constants/status-response';
// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { EBrowserType } from '@/constants/enum';
import {
  LINK_TAPPED_REDIRECT_TYPE,
  LAUNCH_APP_REDIRECT_TYPE,
} from '@/constants/redirect';

export interface IVerifyRedirectExternalPayload {
  redirectId: string;
  index: number;
}

export interface IVerifyRedirectExternalResponse {
  withPoint: boolean;
  redirect: string;
  url_type: typeof LINK_TAPPED_REDIRECT_TYPE | typeof LAUNCH_APP_REDIRECT_TYPE;
  browser_type: EBrowserType;
}

export interface IVerifyRedirectExternalError {
  errors: {
    statusCode: UNAUTHENTICATED | GONE | NOT_FOUND;
    message: string;
    error: string;
  };
}

export interface IVerifyRedirectGifteePayload {
  lotteryId: string;
}

export interface IVerifyRedirectGifteeResponse {
  isWinner: boolean;
  redirect: string;
}

export interface RedirectOptions {
  withExternalBrowser?: boolean;
}

export interface IRedirectUtils {
  query: ParsedUrlQuery;
  redirect: (url: string, options?: RedirectOptions) => void;
  sendMessage: (message?: string) => Promise<void>;
}

export interface IVerifyRedirectGoogleFormPayload {
  questionnaireId: string;
}

export interface IVerifyRedirectGoogleFormResponse {
  redirect_url: string;
}

export interface IVerifyRedirectKyosaiId {
  kyosaiId: string;
}

export interface IVerifyRedirectKyosaiIdResponse {
  url: string;
}

export interface IVerifyRedirectShotanId {
  shotanId: string;
}

export interface IVerifyRedirectShotanIdResponse {
  url: string;
}
