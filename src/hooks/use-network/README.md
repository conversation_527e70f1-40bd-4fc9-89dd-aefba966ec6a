```javascript
import { useNetwork } from '@/hooks/use-network';

const DemoScreen = () => {
  const networkStatus = useNetwork();

  return (
    <div className="container mx-auto pt-6 pb-10">
      <div className="w-full md:max-w-md mx-auto space-y-6">
        <div className="relative overflow-x-auto">
          <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
            <thead className="text-xs text-gray-700 uppercase bg-gray-10 dark:bg-gray-700 dark:text-gray-400">
              <tr>
                <th scope="col" className="px-6 py-3">
                  Property
                </th>
                <th scope="col" className="px-6 py-3">
                  Value
                </th>
              </tr>
            </thead>
            <tbody>
              <tr className="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                <th
                  scope="row"
                  className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white"
                >
                  Online
                </th>
                <td
                  className={`px-6 py-4 ${
                    networkStatus.online ? 'text-blue-600' : 'text-red-600'
                  }`}
                >
                  {networkStatus.online ? 'Online' : 'Offline'}
                </td>
              </tr>
              <tr className="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                <th
                  scope="row"
                  className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white"
                >
                  rtt
                </th>
                <td className="px-6 py-4">{networkStatus.rtt}</td>
              </tr>
              <tr className="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                <th
                  scope="row"
                  className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white"
                >
                  downlink
                </th>
                <td className="px-6 py-4">{networkStatus.downlink}</td>
              </tr>
              <tr className="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                <th
                  scope="row"
                  className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white"
                >
                  effectiveType
                </th>
                <td className="px-6 py-4">{networkStatus.effectiveType}</td>
              </tr>
              <tr className="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                <th
                  scope="row"
                  className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white"
                >
                  saveData
                </th>
                <td
                  className={`px-6 py-4 ${
                    networkStatus.saveData ? 'text-blue-600' : 'text-red-600'
                  }`}
                >
                  {networkStatus.saveData ? 'true' : 'false'}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default DemoScreen;
```
