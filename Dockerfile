FROM node:16-alpine AS base

# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json yarn.lock* .npmrc ./

RUN yarn install --frozen-lockfile \
    && yarn cache clean

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ARG APP_ENV
ARG CLIENT_BASE_URL
ARG HASH_SECRET_KEY
ARG LIFF_ID
ARG LIFF_BASE_URL
ARG LINE_OA_URL
ARG LINE_LOGIN_URL
ARG LINE_BASE_URL
ARG INTERNAL_BASE_URL
ARG INTERNAL_BASE_API
ARG INTERNAL_WEB_BASE_URL
ARG GTM_KEY
ARG APPINSIGHTS_CONNECTION_STRING

ENV APP_ENV=$APP_ENV \
 CLIENT_BASE_URL=$CLIENT_BASE_URL  \
 HASH_SECRET_KEY=$HASH_SECRET_KEY  \
 LIFF_ID=$LIFF_ID \
 LIFF_BASE_URL=$LIFF_BASE_URL \
 LINE_OA_URL=$LINE_OA_URL \
 LINE_LOGIN_URL=$LINE_LOGIN_URL \
 LINE_BASE_URL=$LINE_BASE_URL \
 INTERNAL_BASE_URL=$INTERNAL_BASE_URL \
 INTERNAL_BASE_API=$INTERNAL_BASE_API \
 INTERNAL_WEB_BASE_URL=$INTERNAL_WEB_BASE_URL \
 GTM_KEY=$GTM_KEY \
 APPINSIGHTS_CONNECTION_STRING=$APPINSIGHTS_CONNECTION_STRING
# Create and print the .env environment
RUN touch .env
RUN printenv > .env

# Next.js collects completely anonymous telemetry data about general usage.
# Learn more here: https://nextjs.org/telemetry
# Uncomment the following line in case you want to disable telemetry during the build.
# ENV NEXT_TELEMETRY_DISABLED 1

RUN yarn build \
    && rm -rf node_modules \
    && yarn install --production --frozen-lockfile --ignore-scripts

# If using npm comment out above and use below instead
# RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

# Uncomment the following line in case you want to disable telemetry during runtime.
# ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
# set hostname to localhost
ENV HOSTNAME "0.0.0.0"

# server.js is created by next build from the standalone output
# https://nextjs.org/docs/pages/api-reference/next-config-js/output
CMD ["node", "server.js"]
