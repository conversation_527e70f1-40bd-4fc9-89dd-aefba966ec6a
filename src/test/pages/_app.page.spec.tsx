// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { render, screen, waitFor } from '@testing-library/react';
import MyApp from '@/pages/_app.page';
import HomeScreen from '@/pages/index.page';
import { Router, useRouter } from 'next/router';

jest.mock('next/router', () => ({
  useRouter: jest.fn(),
  __esModule: true,
  default: {
    push: jest.fn(),
    replace: jest.fn(),
    asPath: '/lorem',
  },
}));

describe('pages/_app.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      useRouter.mockReturnValue({
        pathname: '/lorem',
        query: {
          redirectTo: '/lorem',
        },
      });

      render(
        <MyApp pageProps={{}} Component={HomeScreen} router={{} as Router} />
      );

      await waitFor(() => {
        expect(screen.getByRole('status')).toBeInTheDocument();
      });
    });
  });

  describe('Server side', () => {
    it('should return props with companyId', async () => {
      const props = await MyApp.getInitialProps({
        ctx: { query: { company_id: 'lorem' } },
      });

      expect(props).toMatchObject({
        pageProps: {
          appConfig: { liffId: '1661231941-MWakWrjD', companyId: 'lorem' },
        },
      });
    });

    it('should return props without companyId', async () => {
      const props = await MyApp.getInitialProps({
        ctx: { query: {} },
      });

      expect(props).toMatchObject({
        pageProps: {
          appConfig: { liffId: '1661231941-MWakWrjD', companyId: undefined },
        },
      });
    });
  });
});
