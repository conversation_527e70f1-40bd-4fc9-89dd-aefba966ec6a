// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { IThemeBoolean, IThemeColors, IThemeSizes } from '@/types/theme';
import classNames from 'classnames';
import type { ComponentProps, FC, ReactNode } from 'react';
import { forwardRef } from 'react';

import { HelperText } from '../HelperText';
import { styles } from './Select.styles';

export interface ISelectTheme {
  base: string;
  field: {
    base: string;
    icon: {
      base: string;
      svg: string;
    };
    rightIcon: {
      base: string;
      svg: string;
    };
    select: {
      base: string;
      withIcon: IThemeBoolean;
      withRightIcon: IThemeBoolean;
      withShadow: IThemeBoolean;
      sizes: ISelectSizes;
      colors: ISelectColors;
    };
  };
}

export interface ISelectColors extends IThemeColors {
  [key: string]: string;
  default: string;
}

export interface ISelectSizes
  extends Pick<IThemeSizes, 'xs' | 'sm' | 'md' | 'lg'> {
  [key: string]: string;
}

export interface ISelectProps
  extends Omit<ComponentProps<'select'>, 'color' | 'ref'> {
  sizing?: keyof ISelectSizes;
  shadow?: boolean;
  helperText?: ReactNode;
  icon?: FC<ComponentProps<'svg'>>;
  rightIcon?: FC<ComponentProps<'svg'>>;
  color?: keyof ISelectColors;
}

export const Select = forwardRef<HTMLSelectElement, ISelectProps>(
  (
    {
      children,
      sizing = 'md',
      shadow,
      helperText,
      icon: Icon,
      rightIcon: RightIcon,
      color = 'default',
      className,
      ...props
    },
    ref
  ) => {
    const theme = styles;

    return (
      <>
        <div className={classNames(theme.base, className)}>
          <div className={theme.field.base}>
            {Icon && (
              <div className={theme.field.icon.base}>
                <Icon className={theme.field.icon.svg} />
              </div>
            )}
            {RightIcon && (
              <div
                data-testid="right-icon"
                className={theme.field.rightIcon.base}
              >
                <RightIcon className={theme.field.rightIcon.svg} />
              </div>
            )}
            <select
              className={classNames(
                theme.field.select.base,
                theme.field.select.colors[color],
                theme.field.select.withIcon[Icon ? 'on' : 'off'],
                theme.field.select.withRightIcon[RightIcon ? 'on' : 'off'],
                theme.field.select.withShadow[shadow ? 'on' : 'off'],
                theme.field.select.sizes[sizing]
              )}
              {...props}
              ref={ref}
            >
              {children}
            </select>
          </div>
        </div>
        {helperText && <HelperText color={color}>{helperText}</HelperText>}
      </>
    );
  }
);

Select.displayName = 'Select';
