// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { ComponentProps, FC } from 'react';
import { twMerge } from 'tailwind-merge';
import { styles } from './MessageBox.styles';
import { MessageBoxIcon } from './MessageBoxIcon/MessageBoxIcon';
import { MessageBoxBody } from './MessageBoxBody/MessageBoxBody';
import { MessageBoxAction } from './MessageBoxAction/MessageBoxAction';
import { MessageBoxContent } from './MessageBoxContent/MessageBoxContent';

export interface MessageBoxTheme {
  base: string;
}

const MessageBoxComponent: FC<ComponentProps<'div'>> = ({
  className,
  children,
  ...props
}) => {
  return (
    <div className={twMerge(styles.base, className)} {...props}>
      {children}
    </div>
  );
};

MessageBoxComponent.displayName = 'MessageBox';
export const MessageBox = Object.assign(MessageBoxComponent, {
  Icon: MessageBoxIcon,
  Body: MessageBoxBody,
  Action: MessageBoxAction,
  Content: MessageBoxContent,
});
