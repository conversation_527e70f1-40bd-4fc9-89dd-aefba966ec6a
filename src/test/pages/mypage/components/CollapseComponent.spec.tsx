// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { fireEvent, render, screen } from '@testing-library/react';
import { FAQ_MENU } from '@/constants/mypage';
import CollapseComponent from '@/pages/mypage/components/CollapseComponent';

const TestCollapseComponent = (): JSX.Element => {
  return (
    <>
      {FAQ_MENU.map((item, index) => (
        <CollapseComponent
          key={index}
          categogy={item.categogy}
          content={item.content}
        />
      ))}
    </>
  );
};

describe('pages/mypage/components/CollapseComponent.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<TestCollapseComponent />);
      const list = screen.getAllByRole('list');

      expect(list.length).toEqual(24);
    });
  });

  describe('User interactions', () => {
    it('should trigger `onClick` li to open faq', async () => {
      render(<TestCollapseComponent />);

      const list = screen.getAllByRole('listitem');
      const parentCollapse = list[0];
      fireEvent.click(parentCollapse);
      expect(parentCollapse).toBeInTheDocument();

      const childrenCollapse = list[2];
      fireEvent.click(childrenCollapse);
      expect(childrenCollapse).toBeInTheDocument();
    });
  });
});
