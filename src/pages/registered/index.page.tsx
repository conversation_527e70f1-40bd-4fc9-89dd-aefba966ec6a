// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import type { FC } from 'react';
import { useLiff } from '@/contexts/liff';
import { Logo } from '@/components/Logo';
import { Button } from '@/components/Button';
import { BoxContent } from '@/components/BoxContent';
import { redirectToLineChatRoomOA } from '@/utils/redirect';

const RegisteredScreen: FC = () => {
  const { liff } = useLiff();

  return (
    <>
      <div className="container mx-auto">
        <BoxContent>
          <BoxContent.Body className="pt-[124px]">
            <Logo box className="mb-8" />
            <div className="text-center">
              <h3 className="text-base font-bold">すでに会員登録済みです</h3>
            </div>

            <div></div>
          </BoxContent.Body>
          <BoxContent.Footer>
            <Button
              fullSized
              type="submit"
              size="xl"
              color="primary"
              onClick={() => redirectToLineChatRoomOA(liff)}
            >
              トーク画面に戻る
            </Button>
          </BoxContent.Footer>
        </BoxContent>
      </div>
    </>
  );
};

export default RegisteredScreen;
