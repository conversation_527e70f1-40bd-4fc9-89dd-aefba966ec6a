import { MetaSeo, ROUTERS } from '@/types/app';

// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
const myPage = '/mypage';
const accountSetting = '/account-settings';
const re = '/re';
const checkin = '/checkin';
const lottery = '/lottery';
const error = '/error';
const partnerGroup = '/partner-group';
const imageForm = '/img-form';

export const routers: ROUTERS = {
  home: '/',
  register: '/register',
  registered: '/registered',
  registerConfirm: '/register-confirm',
  authError: error + '/auth',
  friendshipError: error + '/friendship',
  blockedError: error + '/blocked',
  membershipError: error + '/membership',
  maintenance: '/maintenance',
  changeRm: '/changerm',
  checkinReadQR: checkin,
  checkinWrongQR: checkin + '/wrong-qr',
  checkinResult: checkin + '/result',
  checkinCalendar: '/checkin-calendar',
  lottery: lottery + '/[rmAliasId]',
  lotteryAlreadyApplied: lottery + '/already-applied',
  lotteryOutOfPeriod: lottery + '/out-of-period',
  myPageTop: myPage + '/top',
  newsList: myPage + '/news-list',
  new: myPage + '/news-list/[newsId]',
  accountSetting: myPage + accountSetting,
  changeProfile: myPage + accountSetting + '/change-profile',
  notificationSetting: myPage + accountSetting + '/message-delivery-settings',
  pointHistoryExchanged: myPage + '/point-history?tab=exchange',
  pointHistory: myPage + '/point-history',
  exchangePoint: myPage + '/exchange-point',
  exchangePointDetail: myPage + '/exchange-point/[giftId]',
  couponDetail: myPage + '/exchange-point/coupon',
  howToGetPoints: myPage + '/how-to-get-points',
  serviceOverview: myPage + '/service-overview',
  partnerList: myPage + '/partner-list',
  help: myPage + '/help',
  faq: myPage + '/faq',
  inquiry: myPage + '/inquiry',
  withdrawal: myPage + '/withdrawal',
  notFound: '/404',
  checkCookie: '/check-cookie',
  myPage: myPage + '/top',
  redirect: re + '/ex',
  redirectGift: re + '/prize/[lotteryId]',
  redirectGoogleForm: re + '/q/[questionnaireId]',
  redirectKyosaiId: re + '/kyosai/[kyosaiId]',
  service: '/service',
  healthCheck: '/healthcheck',
  joinPartnerGroup: partnerGroup,
  imageForm: `${imageForm}[id]`,
  expiredError: error + '/expired',
  membershipOptions: '/membership-options',
  login: '/login',
  webLogin: '/web-login',
};

export const META_SEO: MetaSeo = {
  home: { title: 'ピアコネ会員登録' }, //
  register: { title: 'ピアコネ会員登録' }, //
  registered: { title: '会員登録済み' }, //
  registerConfirm: { title: 'ピアコネ会員登録' }, //
  authError: { title: '送信許可エラー' }, //
  friendshipError: { title: 'ピアコネ会員登録の流れ' }, //
  blockedError: { title: '友だち追加エラー' }, //
  membershipError: { title: 'エラー' }, //
  changeRm: { title: 'サービス変更' }, //
  checkinCalendar: { title: '入館カレンダー' },
  checkinReadQR: { title: 'チェックインdeポイント' }, //
  checkinWrongQR: { title: 'チェックインdeポイント' }, //
  checkinResult: { title: 'チェックインdeポイント' }, //
  lottery: {}, //custom configuration
  lotteryAlreadyApplied: { title: '抽選チャレンジ' }, //
  lotteryOutOfPeriod: { title: '抽選チャレンジ' }, //
  myPage: { title: 'マイページ' }, //
  myPageTop: { title: 'マイページ' }, //
  newsList: { title: 'お知らせ一覧' }, //
  new: { title: 'お知らせ詳細' }, //
  accountSetting: { title: 'アカウント設定' }, //
  changeProfile: { title: '会員登録情報変更' }, //
  notificationSetting: { title: '配信設定' }, //
  pointHistoryExchanged: {}, ////custom configuration
  pointHistory: {}, ////custom configuration
  exchangePoint: { title: 'ポイント交換' }, //
  exchangePointDetail: { title: 'ポイント交換' }, //
  couponDetail: { title: 'クーポン' }, //
  howToGetPoints: { title: 'ポイント獲得一覧' }, //
  partnerList: { title: 'ピアコネパートナー企業' }, //
  help: { title: 'ヘルプ' }, //
  faq: { title: 'FAQ' }, //
  inquiry: { title: 'お問い合わせ' }, //
  withdrawal: { title: '退会' }, //
  notFound: { title: 'ページが開けません' }, //
  checkCookie: { title: 'Check cookie' }, // todo
  serviceOverview: { title: 'serviceOverview' }, // todo
  maintenance: { title: 'メンテナンス中' }, //
  redirect: { title: 'Redirect' },
  redirectGift: { title: 'Redirect Gift' },
  redirectGoogleForm: { title: 'Redirect Google Form' },
  redirectKyosaiId: { title: 'Redirect Kyosai Id' },
  service: { title: 'Service screen' },
  healthCheck: { title: 'healthCheck screen' },
  joinPartnerGroup: { title: '新規グループ追加' },
  imageForm: {},
  expiredError: { title: 'ポイント付与期間終了' },
  membershipOptions: { title: 'ピアコネ' },
  login: { title: 'ログイン' },
  webLogin: { title: 'ログイン' },
};
