// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { ComponentProps, FC } from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { Gift } from '@/components/Gift';
import { Button } from '@/components/Button';

interface GiftTestProps extends ComponentProps<'div'> {
  name?: string;
  thumbnail?: string;
  description?: string;
  action?: () => void;
}

const GiftTest: FC<GiftTestProps> = ({
  name = 'Gift name',
  description,
  thumbnail,
  action,
  ...props
}) => {
  return (
    <Gift {...props}>
      <Gift.Thumb src={thumbnail} alt={name} />
      <Gift.Body>
        <Gift.Content title={name} description={description} />
        <Gift.Action>
          <Button outline size="xs" color="primary" onClick={() => action?.()}>
            Action
          </Button>
        </Gift.Action>
      </Gift.Body>
    </Gift>
  );
};

describe('components/Gift/Gift.spec.tsx', () => {
  describe('A11y', () => {
    it('should component render ui', () => {
      render(<GiftTest data-testid="gift" />);
      const gift = screen.getByTestId('gift');

      expect(gift).toBeInTheDocument();
    });
  });

  describe('Props', () => {
    it('should render gift name prop', () => {
      const giftName = 'Gift name props';
      render(<GiftTest name={giftName} />);
      const title = screen.getByRole('heading');

      expect(title).toHaveTextContent(giftName);
    });

    it('should render gift image prop', () => {
      const giftThumbnail = 'https://example.com/upload/lorem.png';
      render(<GiftTest thumbnail={giftThumbnail} />);
      const thumbnail = screen.getByRole('img');

      expect(thumbnail).toBeInTheDocument();
    });
  });

  describe('User interactions', () => {
    it('should handle click', () => {
      const spy = jest.fn();

      render(<GiftTest action={spy} />);
      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(spy).toHaveBeenCalledTimes(1);
    });
  });
});
