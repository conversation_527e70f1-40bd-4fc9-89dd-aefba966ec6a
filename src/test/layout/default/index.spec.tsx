// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { ComponentProps, FC } from 'react';
import { render, screen } from '@testing-library/react';
import LayoutDefault from '@/layout/default';
import { MetaSeoDetail } from '@/types/app';

interface LayoutDefaultProps extends ComponentProps<'div'> {
  isReady: boolean;
  isLoading: boolean;
  meta: MetaSeoDetail;
}

const metaSeo: MetaSeoDetail = {
  title: 'This is title',
  description: 'This is description',
};

const TestLayoutDefault: FC<LayoutDefaultProps> = props => {
  return (
    <LayoutDefault {...props}>
      <div data-testid="test-default-layout">Test default layout</div>
    </LayoutDefault>
  );
};

describe('layout/default/index.spec.tsx', () => {
  describe('A11y', () => {
    it('should show loading when liff init', async () => {
      render(
        <TestLayoutDefault isReady={false} isLoading={true} meta={metaSeo} />
      );

      expect(screen.getByRole('status')).toBeInTheDocument();
    });

    it('should render when `children={Test default layout}`', async () => {
      render(
        <TestLayoutDefault isReady={true} isLoading={false} meta={metaSeo} />
      );

      expect(screen.getByTestId('test-default-layout')).toHaveTextContent(
        'Test default layout'
      );
    });
  });
});
