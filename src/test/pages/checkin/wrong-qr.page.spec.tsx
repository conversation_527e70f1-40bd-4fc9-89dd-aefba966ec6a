// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { fireEvent, render, screen } from '@testing-library/react';
import CheckinWrongQrScreen from '@/pages/checkin/wrong-qr.page';

jest.mock('next/router', () => require('next-router-mock'));

describe('pages/checkin/wrong-qr.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<CheckinWrongQrScreen />);
      const headings = screen.getAllByRole('heading');

      expect(headings.length).toEqual(2);
    });
  });

  describe('User interactions', () => {
    it('should trigger `onClick` button', async () => {
      render(<CheckinWrongQrScreen />);
      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(button).toBeInTheDocument();
    });
  });
});
