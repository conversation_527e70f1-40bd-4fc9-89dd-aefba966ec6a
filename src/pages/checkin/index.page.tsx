// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useLiff } from '@/contexts/liff';
import { parseQueryString, sendDataLayerEvent } from '@/utils/index';
import { routers } from '@/constants/routes';
import authApi from '@/services/internal/modules/auth';
import { ICheckinPayload, ICheckinResponse } from '@/types/checkin';
import { Button } from '@/components/Button';
import Loading from '@/components/Spinner/Loading';
import { BoxContent } from '@/components/BoxContent';
import { useSessionStorage } from '@/hooks/use-storage';
import { USER_CHECKIN_RESULT } from '@/constants/user';
import { Message } from '@/constants/message';
import {
  redirectWithQueryActiveHistory,
  redirectToLineChatRoomOAWithCheckUtou,
} from '@/utils/redirect';

interface RedirectAfterScanQrOptions {
  closeAppWhenBack?: boolean;
  queryParams?: Record<string, string | boolean>;
}

interface CheckinHandlerOptions {
  closeAppWhenBack?: boolean;
}

const CheckinReadQrScreen = () => {
  const router = useRouter();
  const {
    is_active: isActive,
    company_id: companyId,
    agency_id: agencyId,
    agency_token: agencyToken,
  } = router.query;
  const { isLoggedIn, liff, userInfo } = useLiff();
  const [isLoadingScanQR, setIsLoadingScanQR] = useState(true);

  const [, setUserCheckinResult] = useSessionStorage<ICheckinResponse>({
    key: USER_CHECKIN_RESULT,
  });

  const handleRedirectAfterScanQr = async (
    redirect: string,
    options?: RedirectAfterScanQrOptions
  ): Promise<void> => {
    const { closeAppWhenBack = false, queryParams = {} } = options || {};
    closeAppWhenBack
      ? await redirectWithQueryActiveHistory(redirect, queryParams)
      : await router.push({ pathname: redirect, query: queryParams });
  };

  const isCheckinWithoutRM = () => {
    return companyId && agencyId && agencyToken;
  };

  const checkinHandler = async (
    checkinInfo: ICheckinPayload,
    { closeAppWhenBack = false }: CheckinHandlerOptions = {}
  ) => {
    try {
      const result = await authApi.checkin(checkinInfo);
      setUserCheckinResult(result);
      handleRedirectAfterScanQr(routers.checkinResult, {
        closeAppWhenBack,
        queryParams: result.success ? {} : { withpoints: false },
      });
    } catch {
      throw new Error();
    }
  };

  const scanQrHandler = async () => {
    try {
      // When the QR code is read from other than the rich menu.
      if (isCheckinWithoutRM()) {
        await checkinHandler(
          {
            company_id: `${companyId?.toString()}`,
            agency_id: `${agencyId?.toString()}`,
            agency_token: `${agencyToken?.toString()}`,
          },
          // Handle case close LIFF app when back from CheckinResultScreen to ScanQrCodeScreen
          { closeAppWhenBack: true }
        );
        // When the QR code is read from the rich menu.
      } else {
        setIsLoadingScanQR(false);
        const result = await liff.scanCodeV2();
        if (result.value) {
          const url = new URL(result.value);
          const companyInfo = parseQueryString(
            url.search
          ) as unknown as ICheckinPayload;
          if (
            companyInfo.company_id &&
            companyInfo.agency_id &&
            companyInfo.agency_token
          ) {
            await checkinHandler(companyInfo);
          } else {
            throw new Error(Message.error.wrongQr);
          }
        }
      }
    } catch (e: unknown) {
      sendDataLayerEvent('Checkin wrong', {
        At: routers.checkinWrongQR,
      });
      sessionStorage.removeItem(USER_CHECKIN_RESULT);
      isCheckinWithoutRM()
        ? handleRedirectAfterScanQr(routers.checkinWrongQR, {
            closeAppWhenBack: true,
          })
        : handleRedirectAfterScanQr(routers.checkinWrongQR);
    }
  };

  useEffect(() => {
    if (isActive) {
      redirectToLineChatRoomOAWithCheckUtou(liff);
      return;
    }

    if (isLoggedIn && userInfo && Object.keys(userInfo).length > 0) {
      scanQrHandler();
    }
  }, [isLoggedIn, userInfo]);

  return (
    <>
      {isLoadingScanQR ? (
        <Loading />
      ) : (
        <div className="container mx-auto">
          <BoxContent>
            <BoxContent.Body className="pt-[96px]">
              <div className="text-center">
                <h3 className="text-base font-bold mb-4">
                  各店舗設置の
                  <br />
                  「チェックインdeポイント」
                  <br />
                  専用QRコードを読み取ってください
                </h3>
                <div className="flex justify-center">
                  <p className="text-sm text-left ml-[-40px]">
                    チェックインによるポイント獲得は
                    <br />
                    1日1回までとなります。
                  </p>
                </div>
              </div>
            </BoxContent.Body>
            <BoxContent.Footer>
              <Button
                fullSized
                type="submit"
                size="xl"
                color="primary"
                onClick={scanQrHandler}
              >
                QRコード読み取り
              </Button>
            </BoxContent.Footer>
          </BoxContent>
        </div>
      )}
    </>
  );
};

export default CheckinReadQrScreen;
