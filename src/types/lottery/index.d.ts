// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { ONCE_A_DAY, ONCE_IN_A_PERIOD } from '@/constants/lottery';

export interface IGetLotteryPayload {
  rmAliasId: string;
}

export interface IGetLotteryResponse {
  lotteryId: string;
  verify: number;
  hasPreviewFlag: boolean;
  lottery?: {
    summary: {
      application_times: typeof ONCE_A_DAY | typeof ONCE_IN_A_PERIOD;
      prize_img_link: string;
      prize_name: string;
      same_screenshot_img_link: string;
      target_screenshot_explanation: string;
      start_at: string;
      ended_at: string;
      notes: string;
      top_text?: string;
    };
    num_of_winners: number;
  };
  isExist?: boolean;
  isExpired?: boolean;
  isPause?: boolean;
  isJoin?: boolean;
  aplicationTimes?: typeof ONCE_A_DAY | typeof ONCE_IN_A_PERIOD;
}

export interface ILotteryApplyResponse {
  id: string;
  lotteryId: string;
  memberLineId: string;
  imageUrl: string;
  joinDate: string;
}
