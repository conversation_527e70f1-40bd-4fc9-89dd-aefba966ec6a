// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { fireEvent, render, screen } from '@testing-library/react';
import type { Liff } from '@line/liff';
import { USER_CHECKIN_RESULT } from '@/constants/user';
import { LiffContextMockProviders } from '@/test/mocks';
import CheckinResultScreen from '@/pages/checkin/result.page';

jest.mock('next/router', () => require('next-router-mock'));

const TestCheckinResultScreen = (): JSX.Element => {
  const liffContextValue = {
    liff: { getContext: () => ({}), openWindow: () => ({}) } as unknown as Liff,
  };
  return (
    <LiffContextMockProviders contextValue={liffContextValue}>
      <CheckinResultScreen />
    </LiffContextMockProviders>
  );
};

describe('pages/checkin/result.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<TestCheckinResultScreen />);
      const headings = screen.getAllByRole('heading');

      expect(headings.length).toEqual(1);
    });
  });

  describe('User interactions', () => {
    beforeEach(() => {
      window.sessionStorage.setItem(
        USER_CHECKIN_RESULT,
        JSON.stringify({ success: true })
      );
    });

    afterEach(() => {
      window.sessionStorage.removeItem(USER_CHECKIN_RESULT);
    });

    it('should trigger `onClick` button back to chat room', async () => {
      render(<TestCheckinResultScreen />);
      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(button).toBeInTheDocument();
    });
  });
});
