// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { FC } from 'react';
import Loading from '@/components/Spinner/Loading';
import { useRouter } from 'next/router';
import { useRedirect } from '@/hooks/use-redirect';
import redirectApi from '@/services/internal/modules/redirect';
import { routers } from '@/constants/routes';

const RedirectGoogleFormScreen: FC = () => {
  const router = useRouter();
  useRedirect(async ({ query: { questionnaireId }, redirect }) => {
    const { redirect_url: redirectUrl } =
      await redirectApi.verifyRedirectGoogleFormUrl({
        questionnaireId,
      });

    if (!redirectUrl) {
      router.push(routers.notFound, router.asPath);
      return;
    }

    redirect(redirectUrl);
  });

  return <Loading />;
};

export default RedirectGoogleFormScreen;
