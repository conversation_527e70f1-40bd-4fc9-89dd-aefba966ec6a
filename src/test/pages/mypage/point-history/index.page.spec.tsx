// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { act, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { LiffContextMockProviders } from '@/test/mocks';
import PointHistoryScreen from '@/pages/mypage/point-history/index.page';

jest.mock('next/router', () => require('next-router-mock'));
// eslint-disable-next-line @typescript-eslint/no-var-requires
const useRouter = jest.spyOn(require('next/router'), 'useRouter');
const useMockRouter = (query: Record<string, string | string[]> = {}) => {
  return useRouter.mockReturnValue({
    query,
    push: jest.fn(),
  });
};

const PointHistoryScreenTest = () => {
  return (
    <LiffContextMockProviders>
      <PointHistoryScreen />
    </LiffContextMockProviders>
  );
};

describe('pages/mypage/point-history/index.page.spec.tsx', () => {
  describe('A11y', () => {
    it('should render in the document', async () => {
      render(<PointHistoryScreenTest />);
      const tabs = screen.getAllByRole('tab');
      expect(tabs.length).toEqual(2);
    });
  });

  describe('User interactions', () => {
    it('should trigger `onClick` tab button', async () => {
      useMockRouter({ tab: ['point', 'exchange'] });
      const user = userEvent.setup();

      const { rerender } = render(<PointHistoryScreenTest />);
      const tabPoint = screen.getAllByRole('tab')[0];
      await act(() => user.click(tabPoint));
      expect(tabPoint).toHaveFocus();

      jest.clearAllMocks();

      useMockRouter({ tab: 'point' });
      rerender(<PointHistoryScreenTest />);
      const tabExchange = screen.getAllByRole('tab')[1];
      await act(() => user.click(tabExchange));
      expect(tabExchange).toHaveFocus();
    });
  });
});
