// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { createContext, useContext } from 'react';

type IModalContext = {
  popup?: boolean;
  onClose?: () => void;
};

export const ModalContext = createContext<IModalContext | undefined>(undefined);

export function useModalContext(): IModalContext {
  const context = useContext(ModalContext);

  if (!context) {
    throw new Error(
      'useModalContext should be used within the ModalContext provider!'
    );
  }

  return context;
}
