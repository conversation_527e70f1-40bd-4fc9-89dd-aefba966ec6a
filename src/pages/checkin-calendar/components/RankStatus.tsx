// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import Image from 'next/image';
import React from 'react';
import BronzeRank from '@/assets/images/BronzeRank.png';
import FightRank from '@/assets/images/FightRank.png';
import GoldRank from '@/assets/images/GoldRank.png';
import SilverRank from '@/assets/images/SilverRank.png';
import PointShadowText from '@/assets/images/PointShadowText.png';
import classNames from 'classnames';

interface RankStatusProps {
  stampCount: number;
  className?: string;
}

const RankStatus: React.FC<RankStatusProps> = ({ stampCount, className }) => {
  const rankAndRibbonMapping = [
    {
      threshold: 20,
      rank: GoldRank,
      label: 'GOLD',
      bgClass: 'bg-[#FEDD00]',
      borderClass: '!border-[#FEDD00]',
    },
    {
      threshold: 12,
      rank: SilverRank,
      label: 'SILVER',
      bgClass: 'bg-[#C8C9CA]',
      borderClass: '!border-[#C8C9CA]',
    },
    {
      threshold: 4,
      rank: BronzeRank,
      label: 'BRONZE',
      bgClass: 'bg-[#DFB297]',
      borderClass: '!border-[#DFB297]',
    },
    {
      threshold: 0,
      rank: FightRank,
      label: 'FIGHT',
      bgClass: 'bg-[#5D5D5D] text-white',
      borderClass: '!border-[#5D5D5D]',
    },
  ];

  const getRankAndRibbonStatus = (point: number) => {
    for (const {
      threshold,
      rank,
      label,
      bgClass,
      borderClass,
    } of rankAndRibbonMapping) {
      if (point >= threshold) {
        return { rank, label, bgClass, borderClass };
      }
    }
    return {
      rank: FightRank,
      label: 'FIGHT',
      bgClass: 'bg-[#5D5D5D] text-white',
      borderClass: 'border-[#5D5D5D]',
    };
  };

  const { rank, label, bgClass, borderClass } =
    getRankAndRibbonStatus(stampCount);

  return (
    <div
      className={classNames(
        'space-x-5 flex items-center justify-center',
        className
      )}
    >
      <div className="flex items-center">
        <Image width={88} src={rank} alt="rank" className="z-[1]" />
        <div className={classNames('custom-stamp-number', borderClass)}>
          {stampCount}
          <Image
            width={150}
            src={PointShadowText}
            alt="個"
            className="text z-0"
          />
        </div>
      </div>
      <div className="text-center">
        <p className="text-md font-bold mb-3">今月のステータス</p>
        <div
          className={classNames(
            'w-[161px] leading-[34px] rounded-full font-serif text-[22px] font-black',
            bgClass
          )}
        >
          {label}
        </div>
      </div>
    </div>
  );
};

export default RankStatus;
