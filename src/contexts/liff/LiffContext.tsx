// gkc_hash_code : 01G<PERSON>RB9WX5C044ZNH4YVRMCANH
import type { Liff } from '@line/liff';
import { useRouter } from 'next/router';
import { FC, ReactNode, Dispatch, SetStateAction, useRef } from 'react';
import { createContext, useContext, useEffect, useMemo, useState } from 'react';
import authApi from '@/services/internal/modules/auth';
import appApi from '@/services/internal/modules/app';
import LayoutDefault from '@/layout/default';
import { useLiffLogin } from '@/hooks/use-liff-login';
import { IAppConfig, MetaSeoDetail } from '@/types/app';
import { ImemberStatus, IUserInfo } from '@/types/user';
import { IUserLineInfo } from '@/types/user/line';
import { ILiffContext } from '@/types/contexts/liff';
import { IIErrorDetailLineResponse } from '@/types/errors';
import {
  destroy<PERSON><PERSON>ie<PERSON><PERSON>,
  parseC<PERSON><PERSON><PERSON><PERSON>,
  setCookie<PERSON><PERSON>,
} from '@/utils/cookie';
import {
  redirectToLineChatRoomOA,
  redirectToLineChatRoomOAWithCheckUtou,
  redirectWithQueryActiveHistory,
} from '@/utils/redirect';
import { UNKNOWN } from '@/constants/define';
import { routers } from '@/constants/routes';
import { Message } from '@/constants/message';
import {
  COMPANY_ID,
  SERVICE_ID,
  LINE_ACCESS_TOKEN,
  ONE_TIME_TOKEN,
  PARTNER_GROUPS_ID,
  PEER_CONNE_ID,
  USER_ACCESS_TOKEN,
  USER_ACCESS_TOKEN_EXPIRES,
} from '@/constants/user';
import {
  MAINTENANCE_MODE,
  FORBIDDEN,
  BAD_REQUEST,
} from '@/constants/status-response';
import { useAppInsight } from '@/hooks/use-app-insight';
import TagManager from 'react-gtm-module';

export const LiffContext = createContext<ILiffContext>({
  isLoggedIn: false,
  isReady: false,
  liff: {} as Liff,
  userInfo: {} as IUserInfo,
  setUserInfo: (() => ({})) as Dispatch<SetStateAction<IUserInfo>>,
  userLineInfo: {} as IUserLineInfo,
  setUserLineInfo: (() => ({})) as Dispatch<SetStateAction<IUserLineInfo>>,
  setError: (() => ({})) as Dispatch<SetStateAction<unknown>>,
  memberStatus: {} as ImemberStatus,
  setMemberStatus: (() => ({})) as Dispatch<SetStateAction<ImemberStatus>>,
});

export function useLiff(): ILiffContext {
  return useContext(LiffContext);
}

export interface ILiffContextProps {
  children?: ReactNode;
  appConfig: IAppConfig;
  meta: MetaSeoDetail;
}

export const LiffProvider: FC<ILiffContextProps> = ({
  children,
  appConfig,
  meta,
}) => {
  const router = useRouter();
  const errorRouter = useRef(
    `${routers.friendshipError}?unblocked=true&redirectTo=/register`
  );
  const [error, setError] = useState<unknown>();
  const [isReady, setIsReady] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [userInfo, setUserInfo] = useState<IUserInfo>({} as IUserInfo);
  const [userLineInfo, setUserLineInfo] = useState<IUserLineInfo>(
    {} as IUserLineInfo
  );
  const [memberStatus, setMemberStatus] = useState<ImemberStatus>(
    {} as ImemberStatus
  );

  const [originalLiff, setLiff] = useState<Liff>();
  const [isLoggedIn, liff] = useLiffLogin(originalLiff);
  const { appInsights, loadAppInsights } = useAppInsight();

  const getQueryParamsVerify = () => {
    const {
      company_id: companyId,
      ott: oneTimeToken,
      redirect_url: redirectUrl,
      service_id: serviceId,
      redirectTo,
      unblocked,
      is_active: isActive,
    } = router.query;

    return {
      companyId,
      oneTimeToken,
      redirectUrl,
      serviceId,
      redirectTo,
      unblocked,
      isActive,
    };
  };

  const handleLineLogin = (liffInitial: Liff) => {
    const currentLocation = window.location.href;
    liffInitial.login({ redirectUri: currentLocation });
  };

  const handleAccessTokenLineRevoked = (liffInitial: Liff) => {
    // First, logout to clear session
    liffInitial.logout();
    // After, relogin
    handleLineLogin(liffInitial);
  };

  const handleErrorFromLine = (e: unknown, liffInitial: Liff) => {
    const errorsDetail = e as IIErrorDetailLineResponse;
    // Handle case call API LINE failure
    errorsDetail.code && handleAccessTokenLineRevoked(liffInitial);
  };

  const handleError = async (e: unknown) => {
    const { errors } = e as unknown as { errors: { statusCode: number } };

    if (
      router.pathname === routers.checkCookie ||
      errors?.statusCode === MAINTENANCE_MODE ||
      errors?.statusCode === FORBIDDEN
    )
      return;

    setError(e);
    const pathname = router.pathname;
    const { redirectTo } = getQueryParamsVerify();
    const isInAuthErrorOrFriendshipError =
      pathname === routers.authError || pathname === routers.friendshipError;

    !redirectTo
      ? await redirectWithQueryActiveHistory(errorRouter.current)
      : await redirectWithQueryActiveHistory(errorRouter.current, {
          redirectTo: redirectTo
            ? redirectTo.toString()
            : isInAuthErrorOrFriendshipError
            ? routers.home
            : router.asPath,
        });
  };

  const getFriendshipLine = async (liffInitial: Liff) => {
    try {
      appInsights.trackEvent({ name: 'start-get-friend-line' });
      const { friendFlag } = await liffInitial.getFriendship();

      if (!friendFlag) {
        const lineContext = liffInitial.getContext();
        const { isRegister } = await authApi.isRegistered({
          lineUserId: lineContext?.userId || UNKNOWN,
        });
        const redirectTo = router.asPath;
        let decodeRedirectTo;
        try {
          decodeRedirectTo = decodeURIComponent(redirectTo);
        } catch (e) {
          console.error('Invalid URL encoding:', e);
          decodeRedirectTo = redirectTo;
        }
        // encode value before assign to query
        const encodedRedirectTo = encodeURIComponent(decodeRedirectTo);

        const redirectRoute = isRegister
          ? routers.blockedError
          : routers.friendshipError;

        errorRouter.current = `${redirectRoute}?unblocked=true&redirectTo=${
          encodedRedirectTo || '/register'
        }`;
        throw new Error(Message.error.friendNotAdd);
      }
      appInsights.trackEvent({ name: 'end-get-friend-line' });
    } catch (e: unknown) {
      const { errors } = e as unknown as { errors: { statusCode: number } };

      handleErrorFromLine(e, liffInitial);

      if (errors.statusCode === MAINTENANCE_MODE) {
        throw e;
      }

      throw new Error('Fail to get friendship of LINE');
    }
  };

  const handlePermissionSendMessage = async (liffInitial: Liff) => {
    try {
      appInsights.trackEvent({
        name: 'start-handle-permission-send-message',
      });
      const lineContext = liffInitial.getContext();

      // If user open link from rich menu of chat room (1-on-1 chat), the chat_message.write granted permission will be checked.
      // See more: https://developers.line.biz/en/reference/liff/#get-context
      if (lineContext?.type === 'utou') {
        const permissionSendMessageStatus = await liffInitial.permission.query(
          'chat_message.write'
        );
        // See more: https://developers.line.biz/en/reference/liff/#permission-query
        if (permissionSendMessageStatus.state !== 'granted') {
          const { is_maintenance } = await appApi.isMaintenance();
          await authApi.verifyUserV2({
            line_access_token: liffInitial.getAccessToken() as string,
          });

          if (is_maintenance) {
            errorRouter.current = routers.maintenance;
            throw new Error('During maintenance mode');
          }

          errorRouter.current = routers.authError;
          throw new Error(Message.error.chatMessagePermision);
        }
      }
      appInsights.trackEvent({
        name: 'end-handle-permission-send-message',
      });
    } catch (e: unknown) {
      const { errors } = e as unknown as { errors: { statusCode: number } };
      const errorMessage =
        errors?.statusCode === FORBIDDEN
          ? Message.error.membership
          : 'Fail to query permission chat_message.write of LINE';

      if (errors?.statusCode === FORBIDDEN) {
        errorRouter.current = routers.membershipError;
      }

      handleErrorFromLine(e, liffInitial);
      throw new Error(errorMessage);
    }
  };

  const updateUserAndCallGa4 = async (userData: IUserInfo) => {
    if (userData.peer_conne_id) {
      setCookieApp(null, PEER_CONNE_ID, userData.peer_conne_id, {
        maxAge: USER_ACCESS_TOKEN_EXPIRES,
      });
    }

    if (process.env.GTM_KEY && userData.peer_conne_id) {
      const tagManagerArgs = {
        gtmId: process.env.GTM_KEY,
        dataLayer: { user_id: userData.peer_conne_id },
      };

      await TagManager.initialize(tagManagerArgs);
    }

    if (userData.peer_conne_id) {
      setUserInfo({ ...userInfo, ...userData });
    }
  };

  const redirectAfterVerifyUser = async (liffInitial: Liff) => {
    const {
      companyId: companyIdFromQuery,
      redirectTo: redirectToFromQuery,
      unblocked: unblockedFromQuery,
      isActive: isActiveFromQuery,
    } = getQueryParamsVerify();
    /**
     * We handle case unblocked query === true
     * if has unblocked and redirect to path name === home then back to talk screen
     * else redirect to original url
     */
    const pathnameRedirectTo = redirectToFromQuery
      ? redirectToFromQuery.toString().split('?')[0]
      : '';

    if (unblockedFromQuery && pathnameRedirectTo === routers.home)
      await redirectToLineChatRoomOA(liffInitial);
    else if (router.pathname === routers.home) {
      if (isActiveFromQuery && !redirectToFromQuery)
        await redirectToLineChatRoomOAWithCheckUtou(liff);
      else if (!isActiveFromQuery && redirectToFromQuery) {
        const urlParams = new URLSearchParams(redirectToFromQuery as string);
        const redirectUrl = urlParams.get('redirect_url');
        redirectUrl
          ? await router.replace(redirectToFromQuery as string)
          : await redirectWithQueryActiveHistory(
              redirectToFromQuery.toString()
            );
      } else if (!isActiveFromQuery && !redirectToFromQuery) {
        if (userInfo && Object.keys(userInfo).length > 0) {
          !userInfo.email
            ? await redirectWithQueryActiveHistory(routers.register)
            : userInfo.email &&
              !companyIdFromQuery &&
              (await redirectWithQueryActiveHistory(routers.registered));
        } else await redirectWithQueryActiveHistory(routers.registered);
      } else if (!isActiveFromQuery && error)
        await redirectWithQueryActiveHistory(
          `${routers.friendshipError}?unblocked=true&redirectTo=/register`
        );
      else if (!companyIdFromQuery && redirectToFromQuery)
        await redirectWithQueryActiveHistory(redirectToFromQuery.toString());
      else await redirectWithQueryActiveHistory(routers.registered);
    }
  };

  const verifyUserV2 = async (liffInitial: Liff) => {
    try {
      const { company_id, service_id, one_time_token } = parseCookiesApp();
      const { companyId, serviceId, oneTimeToken, redirectTo } =
        getQueryParamsVerify();

      if (
        router.pathname !== routers.service &&
        !redirectTo?.includes('/service?')
      ) {
        const result = await authApi.verifyUserV2({
          line_access_token: liffInitial.getAccessToken() as string,
          company_id: (companyId || company_id) as string,
          token: (oneTimeToken || one_time_token) as string,
          service_id: (serviceId || service_id) as string,
        });

        setMemberStatus({
          ...memberStatus,
          is_member: result.is_member,
          is_user_linked_with_inflow_company:
            result.is_user_linked_with_inflow_company,
        });

        if (!result.is_member && router.pathname !== routers.login) {
          if (!redirectTo?.includes('/login')) {
            await router.replace(routers.membershipOptions);
            return;
          }
        } else if (
          result.is_member &&
          (router.pathname === routers.login || redirectTo?.includes('/login'))
        ) {
          redirectToLineChatRoomOA(liffInitial);
          return;
        } else {
          result.access_token &&
            setCookieApp(null, USER_ACCESS_TOKEN, result.access_token, {
              maxAge: USER_ACCESS_TOKEN_EXPIRES,
            });

          result.member_information &&
            Object.keys(result.member_information).length > 0 &&
            (await updateUserAndCallGa4(result.member_information));

          if (result.registrable_partner_group_ids?.length ?? 0 > 0) {
            const partnerGroupsIdCookie = JSON.stringify(
              result.registrable_partner_group_ids
            );
            result.registrable_partner_group_ids &&
              setCookieApp(null, PARTNER_GROUPS_ID, partnerGroupsIdCookie, {
                maxAge: USER_ACCESS_TOKEN_EXPIRES,
              });

            await router.replace({
              pathname: routers.joinPartnerGroup,
              query: { company_id: companyId || company_id },
            });
            return;
          } else {
            destroyCookieApp(null, COMPANY_ID);
          }
        }

        await redirectAfterVerifyUser(liffInitial);
      } else if (redirectTo?.includes('/service?')) {
        await router.replace(redirectTo as string);
      }
    } catch (e: unknown) {
      const { errors } = e as unknown as { errors: { statusCode: number } };
      if (errors?.statusCode === BAD_REQUEST) {
        return;
      }
      destroyCookieApp(null, COMPANY_ID);
      await handleError(e);
    } finally {
      destroyCookieApp(null, SERVICE_ID);
      destroyCookieApp(null, ONE_TIME_TOKEN);
    }
  };

  const getUserProfileLine = async (liffInitial: Liff) => {
    try {
      const userLineProfile = await liffInitial.getProfile();
      setUserLineInfo(userLineProfile || ({} as IUserLineInfo));
      await verifyUserV2(liffInitial);
    } catch (e: unknown) {
      handleErrorFromLine(e, liffInitial);
      throw new Error('Fail to get user profile of LINE');
    }
  };

  const verifyUserLine = async (liffInitial: Liff) => {
    const lineAccessToken = liffInitial.getAccessToken();
    lineAccessToken &&
      setCookieApp(null, LINE_ACCESS_TOKEN, lineAccessToken as string, {
        maxAge: USER_ACCESS_TOKEN_EXPIRES,
      });

    try {
      appInsights.trackEvent({ name: 'start-get-user-profile-line' });
      await getFriendshipLine(liffInitial);
      await handlePermissionSendMessage(liffInitial);
      await getUserProfileLine(liffInitial);
      appInsights.trackEvent({ name: 'end-get-user-profile-line' });
      appInsights.trackEvent({ name: 'end-verify-user-line' });
    } catch (e: unknown) {
      await handleError(e);
    }
  };

  const liffInit = async () => {
    //save company_id to cookies when router.pathname is /service
    const {
      redirectUrl: redirect_url,
      companyId,
      serviceId,
      oneTimeToken,
    } = getQueryParamsVerify();
    if (router.pathname === routers.service && redirect_url) {
      try {
        const urlObj = new URL(redirect_url as string);
        const companyIdFromQuery = urlObj.searchParams.get('company_id');
        companyIdFromQuery &&
          setCookieApp(null, COMPANY_ID, companyIdFromQuery as string, {
            maxAge: USER_ACCESS_TOKEN_EXPIRES,
          });
      } catch (e) {
        console.error(e);
      }
    }

    try {
      setIsLoading(true);
      appInsights.trackEvent({
        name: 'start-liff-init',
        properties: { liffId: appConfig.liffId },
      });
      const liffInitial =
        window.liff ?? ((await import('@line/liff')).default as Liff);
      await liffInitial.init({ liffId: appConfig.liffId });
      appInsights.trackEvent({
        name: 'end-liff-init',
        properties: { liffInitial },
      });
      setLiff(liffInitial);
      setIsReady(true);

      companyId &&
        setCookieApp(null, COMPANY_ID, companyId as string, {
          maxAge: USER_ACCESS_TOKEN_EXPIRES,
        });
      oneTimeToken &&
        setCookieApp(null, ONE_TIME_TOKEN, oneTimeToken as string, {
          maxAge: USER_ACCESS_TOKEN_EXPIRES,
        });
      serviceId &&
        setCookieApp(null, SERVICE_ID, serviceId as string, {
          maxAge: USER_ACCESS_TOKEN_EXPIRES,
        });

      const isLoginInitial = liffInitial.isLoggedIn();
      if (!isLoginInitial) {
        handleLineLogin(liffInitial);
      } else if (router.route !== routers.notFound) {
        await verifyUserLine(liffInitial);
      }
    } catch (e: unknown) {
      errorRouter.current = routers.notFound;
      await handleError(e);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadAppInsights();
    liffInit();
  }, []);

  const LiffContextValue = useMemo(
    () => ({
      error,
      isLoggedIn,
      isReady,
      liff,
      userInfo,
      memberStatus,
      setMemberStatus,
      setUserInfo,
      userLineInfo,
      setUserLineInfo,
      setError,
    }),
    [
      error,
      isLoggedIn,
      isReady,
      liff,
      userInfo,
      memberStatus,
      setMemberStatus,
      setUserInfo,
      userLineInfo,
      setUserLineInfo,
      setError,
    ]
  );

  return (
    <LiffContext.Provider value={LiffContextValue}>
      <LayoutDefault isReady={isReady} isLoading={isLoading} meta={meta}>
        {!isLoading && children}
      </LayoutDefault>
    </LiffContext.Provider>
  );
};
