// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
/* eslint-disable @next/next/no-img-element */
import classNames from 'classnames';
import { FC, useEffect } from 'react';
import React, { useRef, useState } from 'react';
import { useRouter } from 'next/router';
import { useLiff } from '@/contexts/liff';
import { Button } from '@/components/Button';
import { Spinner } from '@/components/Spinner';
import Loading from '@/components/Spinner/Loading';
import { BoxContent } from '@/components/BoxContent';
import LotteryModal from '@/components/Modal/LotteryModal';
import { IAppConfig } from '@/types/app';
import { IGetLotteryResponse } from '@/types/lottery';
import { compressImage, getMimeType } from '@/utils/file';
import { convertDateWithFormat } from '@/utils/time';
import { convertObjectToFormData } from '@/utils/convert';
import {
  redirectWithQueryActiveHistory,
  redirectToLineChatRoomOAWithCheckUtou,
  redirectWithoutEmptyQuery,
} from '@/utils/redirect';
import lotteryApi from '@/services/internal/modules/lottery';
import { routers } from '@/constants/routes';
import { IMAGE_TYPE_HEIC, IMAGE_TYPE_JPEG } from '@/constants/file';
import { SHOW_LOTTERY_MODAL } from '@/constants/common';
import {
  LOTTERY_UPLOAD_ACCEPT,
  LOTTERY_UPLOAD_TYPE_REGEX,
  LOTTERY_MESSAGE,
  ONCE_A_DAY,
} from '@/constants/lottery';
import { NextSeo } from 'next-seo';

interface ILotteryScreenProps {
  appConfig: IAppConfig;
}

const LotteryScreen: FC<ILotteryScreenProps> = ({ appConfig }) => {
  const { liff } = useLiff();
  const router = useRouter();
  const {
    rmAliasId,
    is_active: isActive,
    show: showQuery,
    image: imageQuery,
  } = router.query;
  const uploadLotteryRef = useRef<HTMLInputElement>(null);
  const [lotteryImageFile, setLotteryImageFile] = useState<File | null>(null);
  const [lotteryImagePreview, setLotteryImagePreview] = useState<string | null>(
    null
  );
  const [lotteryHeicBlobUrl, setLotteryHeicBlobUrl] = useState<string | null>(
    null
  );
  const [lotteryId, setLotteryId] = useState('');
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [isApplyLottery, setIsApplyLottery] = useState(false);
  const [isShowTermsModal, setIsShowTermsModal] = useState(
    () => showQuery === SHOW_LOTTERY_MODAL
  );
  const [isLoadingApplyLottery, setIsLoadingApplyLottery] = useState(false);
  const [isLoadingVerifyLottery, setIsLoadingVerifyLottery] = useState(true);
  const [isLoadingChangeImage, setIsLoadingChangeImage] = useState(false);
  const [numOfWinners, setNumOfWinners] = useState(0);
  const [applicationTimes, setApplicationTimes] = useState('');
  const [prizeImgLink, setPrizeImgLink] = useState('');
  const [prizeName, setPrizeName] = useState('');
  const [sameScreenshotLink, setSameScreenshotLink] = useState('');
  const [targetScreenshotExplanation, setTargetScreenshotExplanation] =
    useState('');
  const [startLottery, setStartLottery] = useState('');
  const [endLottery, setEndLottery] = useState('');
  const [notes, setNotes] = useState('');
  const [topText, setTopText] = useState('');

  const onChangeFileHandler = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    try {
      setIsLoadingChangeImage(true);
      const listFiles = event.target.files as FileList;
      if (listFiles && listFiles.length === 0) return;
      const file = listFiles[0] as File;
      const { size } = file;
      const type = await getMimeType(file);
      if (!LOTTERY_UPLOAD_TYPE_REGEX.test(type)) return;

      if (size > 5 * 1024 * 1024) return; // 5MB Max

      let convertFile = file;

      if (type === IMAGE_TYPE_HEIC) {
        // because heic2any not support server side
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const heic2any = require('heic2any');
        // get image as blob url
        const blobURL = URL.createObjectURL(file);
        // set to state for revoke later
        setLotteryHeicBlobUrl(blobURL);
        // convert "fetch" the new blob url
        const blobRes = await fetch(blobURL);
        // convert response to blob
        const blob = await blobRes.blob();
        // convert to JPEG - response is blob
        const conversionResult = await heic2any({
          blob,
          quality: 1,
          toType: IMAGE_TYPE_JPEG,
        });

        conversionResult.name = file.name
          .toLowerCase()
          .replace(/\.hei(c|f)$/, '.jpeg');
        conversionResult.lastModified = new Date().getTime();

        convertFile = conversionResult;
      }

      const compressedFileBlob = await compressImage(convertFile);
      if (typeof compressedFileBlob === 'undefined') throw new Error();
      const compressedFile = new File(
        [compressedFileBlob],
        compressedFileBlob.name,
        {
          type: compressedFileBlob.type,
        }
      );
      setLotteryImageFile(compressedFile);
      setLotteryImagePreview(URL.createObjectURL(compressedFile));
      await redirectWithoutEmptyQuery({ image: 'uploaded' });
    } finally {
      setIsLoadingChangeImage(false);
    }
  };

  const uploadLotteryFileHandler = () => {
    !isLoadingApplyLottery && uploadLotteryRef?.current?.click();
  };

  const sendMessageHandler = async () => {
    await liff.sendMessages([{ type: 'text', text: LOTTERY_MESSAGE }]);
  };

  const applyLotteryHandler = async () => {
    try {
      setIsLoadingApplyLottery(true);
      const payload = {
        lotteryId,
        companyId: appConfig.companyId,
        image: lotteryImageFile,
      };
      const payloadMultipart = convertObjectToFormData(payload);
      await lotteryApi.apply(payloadMultipart);
      const lineContext = liff.getContext();
      lineContext?.type === 'utou' && (await sendMessageHandler());
      setIsApplyLottery(true);
    } finally {
      setIsLoadingApplyLottery(false);
    }
  };

  const backToChatRoomOAHandler = () => {
    setLotteryImageFile(null);
    lotteryImagePreview && URL.revokeObjectURL(lotteryImagePreview);
    lotteryHeicBlobUrl && URL.revokeObjectURL(lotteryHeicBlobUrl);
    setLotteryImagePreview(null);
    setIsApplyLottery(false);
    liff.closeWindow();
  };

  const onClickHandler = async () => {
    !lotteryImageFile
      ? uploadLotteryFileHandler()
      : !isApplyLottery
      ? await applyLotteryHandler()
      : backToChatRoomOAHandler();
  };

  const verifyLotteryHandler = async (lotteryDetail: IGetLotteryResponse) => {
    try {
      if (lotteryDetail.isExist === false) {
        await redirectWithQueryActiveHistory(routers.lotteryOutOfPeriod);
        return;
      }
      if (lotteryDetail.isExpired || lotteryDetail.isPause) {
        await redirectWithQueryActiveHistory(routers.lotteryOutOfPeriod);
        return;
      }
      if (lotteryDetail.isJoin) {
        await redirectWithQueryActiveHistory(routers.lotteryAlreadyApplied, {
          is_once_a_day: lotteryDetail.aplicationTimes === ONCE_A_DAY,
        });
        return;
      }

      if (lotteryDetail?.lottery) {
        setNumOfWinners(lotteryDetail.lottery.num_of_winners);
        setApplicationTimes(
          lotteryDetail.lottery.summary.application_times.replace(
            /\n/g,
            '<br />'
          )
        );
        setPrizeImgLink(lotteryDetail.lottery.summary.prize_img_link);
        setPrizeName(lotteryDetail.lottery.summary.prize_name);
        setSameScreenshotLink(
          lotteryDetail.lottery.summary.same_screenshot_img_link
        );
        setTargetScreenshotExplanation(
          lotteryDetail.lottery.summary.target_screenshot_explanation
        );
        setStartLottery(
          convertDateWithFormat(
            lotteryDetail.lottery.summary.start_at,
            'YearMonthDateWithoutMinute'
          )
        );
        setEndLottery(
          convertDateWithFormat(
            lotteryDetail.lottery.summary.ended_at,
            'YearMonthDateWithoutMinute'
          )
        );
        setNotes(lotteryDetail.lottery.summary.notes);
        lotteryDetail.lottery.summary.top_text &&
          setTopText(lotteryDetail.lottery.summary.top_text);
      } else {
        throw new Error();
      }
    } catch {
      throw new Error();
    }
  };

  const getLotteryByAlias = async () => {
    try {
      setIsLoadingVerifyLottery(true);
      const result = await lotteryApi.getLottery({
        rmAliasId: `${rmAliasId?.toString()}`,
      });
      if (result?.lotteryId) {
        setLotteryId(result.lotteryId);
        setIsPreviewMode(result?.hasPreviewFlag);
        await verifyLotteryHandler(result);
      } else {
        await redirectWithQueryActiveHistory(routers.lotteryOutOfPeriod);
      }
    } catch {
      router.push(routers.notFound, router.asPath);
    } finally {
      setIsLoadingVerifyLottery(false);
    }
  };

  useEffect(() => {
    if (isActive) {
      redirectToLineChatRoomOAWithCheckUtou(liff);
      return;
    }

    getLotteryByAlias();
  }, []);

  useEffect(() => {
    if (imageQuery && !lotteryImagePreview) {
      redirectWithoutEmptyQuery({ image: '' });
    }
  }, [imageQuery]);

  return (
    <>
      <NextSeo
        title={
          isPreviewMode ? '抽選チャレンジ（プレビュー）' : '抽選チャレンジ'
        }
      />
      {isLoadingVerifyLottery ? (
        <Loading />
      ) : (
        <div className="container mx-auto">
          <BoxContent>
            <BoxContent.Body className="mt-4">
              <div className="text-sm font-normal space-y-6">
                {!isApplyLottery && (
                  <div className="w-full">
                    <img className="rounded" alt="Lottery" src={prizeImgLink} />
                  </div>
                )}
                {lotteryImagePreview && (
                  <div className="space-y-4">
                    <p
                      className={classNames('text-center font-bold', {
                        'text-base mt-10': isApplyLottery,
                      })}
                    >
                      {isApplyLottery
                        ? 'チャレンジ完了！'
                        : 'アップロードした画像'}
                    </p>
                    {isApplyLottery && (
                      <p>
                        当選者の発表は、ピアコネLINE公式アカウントから、LINEメッセージでの当選通知をもってかえさせていただきます。
                        <br />※
                        LINEメッセージでの当選通知はご当選者のみとなります。
                      </p>
                    )}
                    <img
                      src={lotteryImagePreview}
                      alt="Lottery"
                      className={classNames(
                        'mx-auto max-w-[160px] h-auto rounded object-cover',
                        { '!mt-6': isApplyLottery },
                        { '!mt-2': lotteryImagePreview && !isApplyLottery }
                      )}
                    />
                    <div
                      className={classNames('mx-auto space-y-4 font-bold', {
                        '!mt-6': lotteryImagePreview,
                      })}
                    >
                      {!isApplyLottery && (
                        <>
                          <Button
                            fullSized
                            type="submit"
                            size="xl"
                            color="primary"
                            onClick={onClickHandler}
                            disabled={isLoadingApplyLottery}
                          >
                            {isLoadingApplyLottery && (
                              <div className="mr-3">
                                <Spinner size="lg" color="primary" />
                              </div>
                            )}
                            この画像でチャレンジする
                          </Button>
                          <Button
                            outline
                            fullSized
                            size="xl"
                            color="primary"
                            onClick={uploadLotteryFileHandler}
                            className="hover:!bg-white hover:!text-[#B88770]"
                            disabled={isLoadingChangeImage}
                          >
                            {isLoadingChangeImage && (
                              <div className="mr-3">
                                <Spinner size="lg" color="primary" />
                              </div>
                            )}
                            画像を変更する
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                )}
                {!isApplyLottery && (
                  <div
                    className={classNames('space-y-4', {
                      'pb-10': lotteryImagePreview,
                    })}
                  >
                    <div className="space-y-2">
                      {topText && (
                        <p
                          dangerouslySetInnerHTML={{
                            __html: topText,
                          }}
                        />
                      )}
                      <p>
                        対象となる画像の登録1枚につき、１口応募できます。応募者の中から抽選で
                        <strong>{numOfWinners}</strong>名様に
                        <strong>{prizeName}</strong>をプレゼントします。
                      </p>
                      <p>
                        <span className="font-medium">詳細は</span>
                        <span
                          className="text-theme-link cursor-pointer underline decoration-solid"
                          onClick={() => setIsShowTermsModal(true)}
                        >
                          こちら
                        </span>
                        から
                      </p>
                    </div>
                    <div>
                      <h3 className="font-bold mb-1">応募期間</h3>
                      <p className="mb-3">
                        {startLottery} ～ {endLottery}
                      </p>
                      {applicationTimes === ONCE_A_DAY ? (
                        <ul>
                          <li>
                            ※
                            1日1回を上限に、期間中は毎日応募可能です。毎日の応募で当選確率もUP！
                          </li>
                          <li>
                            ※
                            毎日ご応募いただいた場合でも、当キャンペーンのご当選はおひとり様1回となります。
                          </li>
                        </ul>
                      ) : (
                        <ul>
                          <li>
                            ※
                            期間中、ご応募はお一人様1回限りとさせていただきます。
                          </li>
                        </ul>
                      )}
                    </div>
                    <div>
                      <h3 className="font-bold mb-1">応募資格</h3>
                      <ul>
                        <li>・ピアコネLINE公式アカウント会員</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="font-bold mb-1">対象となる画像</h3>
                      <p
                        dangerouslySetInnerHTML={{
                          __html: targetScreenshotExplanation,
                        }}
                      />
                    </div>
                    {!lotteryImagePreview &&
                      (sameScreenshotLink ? (
                        <div className="mx-auto h-[208px]">
                          <img
                            className="h-full w-auto max-w-full mx-auto object-cover"
                            alt="Same screenshot image"
                            src={sameScreenshotLink}
                          />
                        </div>
                      ) : (
                        <div className="mx-auto w-[117px] h-[208px] rounded bg-gray-10"></div>
                      ))}
                  </div>
                )}
              </div>
              <input
                hidden
                ref={uploadLotteryRef}
                type="file"
                data-testid="upload-lottery-file"
                accept={LOTTERY_UPLOAD_ACCEPT}
                onChange={onChangeFileHandler}
              />
            </BoxContent.Body>
            {(!lotteryImagePreview || isApplyLottery) && (
              <BoxContent.Footer>
                <Button
                  fullSized
                  type="submit"
                  size="xl"
                  color="primary"
                  onClick={onClickHandler}
                  disabled={isLoadingApplyLottery || isLoadingChangeImage}
                >
                  {(isLoadingApplyLottery || isLoadingChangeImage) && (
                    <div className="mr-3">
                      <Spinner size="lg" color="primary" />
                    </div>
                  )}
                  {!lotteryImagePreview
                    ? '画像をアップロードする'
                    : 'トーク画面に戻る'}
                </Button>
              </BoxContent.Footer>
            )}
          </BoxContent>

          <LotteryModal
            numOfWinners={numOfWinners}
            applicationTimes={applicationTimes}
            prizeName={prizeName}
            targetScreenshotExplanation={targetScreenshotExplanation}
            startLottery={startLottery}
            endLottery={endLottery}
            notes={notes}
            topText={topText}
            isShowModal={isShowTermsModal}
            setIsShowModal={setIsShowTermsModal}
          />
        </div>
      )}
    </>
  );
};

export default LotteryScreen;
