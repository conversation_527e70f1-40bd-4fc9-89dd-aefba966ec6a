/**
 * @see {@link https://github.com/animate-css/animate.css/blob/main/source/fading_entrances/fadeIn.css}
 */
@keyframes fade-in {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.fadeIn {
  animation: fade-in 0.5s ease both;
}

/**
 * @see {@link https://github.com/animate-css/animate.css/blob/main/source/fading_exits/fadeOut.css}
 */
@keyframes fade-out {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

.fadeOut {
  animation: fade-out 0.5s ease both;
}
