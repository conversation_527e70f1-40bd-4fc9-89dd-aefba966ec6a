// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import { useEffect, type FC } from 'react';
import Loading from '@/components/Spinner/Loading';
import { useRouter } from 'next/router';
import { useLiff } from '@/contexts/liff';

const HomeScreen: FC = () => {
  const router = useRouter();
  const { redirectTo, is_active } = router.query;
  const { userInfo } = useLiff();
  useEffect(() => {
    const reloadCount = parseInt(localStorage.getItem('reloadCount') || '0');

    if (!redirectTo && !is_active && Object.keys(userInfo).length > 0) {
      if (reloadCount > 0) {
        localStorage.removeItem('reloadCount');
        return;
      }

      localStorage.setItem('reloadCount', (reloadCount + 1).toString());
      router.reload();
    }
  }, [router.query, Object.keys(userInfo).length]);
  return <Loading />;
};

export default HomeScreen;
