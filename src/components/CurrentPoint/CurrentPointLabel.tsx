// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import classNames from 'classnames';
import { ComponentProps, FC } from 'react';
import { styles } from './CurrentPoint.styles';
import { formatCurrentPoint } from '@/utils/convert';

export interface CurrentPointLabelProps extends ComponentProps<'h2'> {
  point: number;
  unit?: string;
}

export const CurrentPointLabel: FC<CurrentPointLabelProps> = ({
  point,
  unit = 'pt',
  className,
  children,
  ...props
}) => {
  return (
    <h2 className={classNames(styles.label.base, className)} {...props}>
      {children && <span className={styles.label.label}>{children}</span>}
      <span className={styles.label.point}>{formatCurrentPoint(point)}</span>
      <span className={styles.label.unit}>{unit}</span>
    </h2>
  );
};
