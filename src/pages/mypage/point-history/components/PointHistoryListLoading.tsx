// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import React from 'react';
import { range } from 'lodash';

export const PointHistoryListLoading = () => {
  return (
    <div className="animate-pulse space-y-2" data-testid="loading">
      {[...range(6)].map(index => (
        <div
          key={index}
          className="w-full px-3 py-[7px] rounded border border-gray-10"
        >
          <div className="flex justify-between items-center gap-3">
            <div className="w-10 h-10 bg-slate-200 rounded" />
            <div className="flex-grow space-y-0.5">
              <div className="flex justify-between">
                <div className="w-20 h-5 bg-slate-200 rounded" />
                <div className="w-[90px] h-4 bg-slate-200 rounded" />
              </div>
              <div className="flex justify-between">
                <div className="w-[150px] h-5 bg-slate-200 rounded" />
                <div className="w-8 h-4 bg-slate-200 rounded" />
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
