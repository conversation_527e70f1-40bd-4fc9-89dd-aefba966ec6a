This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Prerequisite

- Node: >= 16.0.0

## Installation

- Add Certificate HTTPS Local: <https://github.com/framgia/WellnessConnect-FE/tree/develop/.certificate/README.md>
- Create Developer LINE App: <https://docs.google.com/spreadsheets/d/1Hj4yNS1RnJmJXA-5T3yftYbkXVG8v1ZeivcVLzJZmyQ>

## Getting Started

- Run the development enviroment:

```bash
yarn dev
```

- Build the development enviroment on local: Run the two commands below in **two separate terminals**

```bash
yarn build-local    #Build and start Next at port 2999
```

```bash
yarn proxy    #Proxy your https://localhost:3000 to Next at port 2999
```

Open [https://localhost:3000](https://localhost:3000) with your browser to see the result.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.
