// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
import Loading from '@/components/Spinner/Loading';
import { routers } from '@/constants/routes';
import { USER_ACCESS_TOKEN, USER_ACCESS_TOKEN_EXPIRES } from '@/constants/user';
import { useLiff } from '@/contexts/liff';
import authApi from '@/services/internal/modules/auth';
import { IVerifyUserV2Response } from '@/types/user';
import { setCookieApp } from '@/utils/cookie';
import { useRouter } from 'next/router';
import { FC, useEffect } from 'react';

const ExternalToPeerConnerLinkageScreen: FC = () => {
  const router = useRouter();
  const { redirect_url, service_id, ott } = router.query;
  const { liff, userLineInfo, setUserInfo } = useLiff();

  const externalLinkage = async () => {
    try {
      const { member_information, access_token }: IVerifyUserV2Response =
        await authApi.verifyUserV2({
          line_access_token: liff.getAccessToken() as string,
          service_id: service_id as string,
          token: ott as string,
        });

      if (member_information && Object.keys(member_information).length > 0) {
        setUserInfo(member_information);
      }
      access_token &&
        setCookieApp(null, USER_ACCESS_TOKEN, access_token, {
          maxAge: USER_ACCESS_TOKEN_EXPIRES,
        });
    } finally {
      if (!redirect_url) router.push(routers.notFound);
      else router.replace(redirect_url as string);
    }
  };

  useEffect(() => {
    userLineInfo.userId && externalLinkage();
  }, [redirect_url, service_id, ott]);

  return <Loading />;
};

export default ExternalToPeerConnerLinkageScreen;
