// gkc_hash_code : 01GKRB9WX5C044ZNH4YVRMCANH
export const USER_INFO = {
  line_user_id: 'line_user_id',
  peer_conne_id: 'peer_conne_id',
  company_id: 'C0001',
  email: '<EMAIL>',
  birthday: '2024-12-24',
  gender: 'male',
  prefecture: 'prefecture',
  is_follow: true,
  current_point: 9999,
};

export const USER_LINE_INFO = {
  userId: 'Uabc',
  displayName: 'Lorem Ipsum',
  pictureUrl: 'https://profile.line-scdn.net/lorem',
};

export const USER_REGISTER_FORM = {
  isAcceptPolicies: true,
  isAcceptTerms: true,
  email: '<EMAIL>',
  password: 'U2FsdGVkX1+Uu0boicjDOiorO/BfVM5anGyivRowcuo=',
  confirmPassword: 'U2FsdGVkX18hc/UFYMCA7AQuwq8Mt7nXnr7xYlVzrmo=',
  birthYear: '2000',
  birthMonth: '8',
  prefecture: '石川県',
  gender: 'male',
};

export const USER_REGISTER_SUCCESS = {
  memberLineId: 'Uc8c5ab4aadc11be86fc780b632f018cd',
  email: '<EMAIL>',
  companyId: 'C0003',
  amountPoint: 0,
  birthday: '2014-02',
  gender: 'female',
  prefecture: '青森県',
  isFollow: true,
  accessToken: '12312312',
};

export const USER_REGISTER_ERROR = [
  {
    property: 'email',
    constraints: {
      MemberUnique: '<EMAIL> is already taken',
    },
  },
];

export const USER_PREFECTURE_SELECTED_FORM = '石川県';

export const VERIFY_MEMBER_RESPONSE = {
  isRegister: true,
  accessToken: 'lorem',
};
